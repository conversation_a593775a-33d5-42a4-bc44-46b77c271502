import { typeList, helmetModelList, radarList } from "@/api/regulation";
export default {
    data() {
        return {
            form: {},
            modelFn: null,
            typeList: {
                1: "IOT",
                2: this.$t("system.app.helmet"),
                3: this.$t("system.app.radar")
            },
            modelData: {
                data: [],
                page: 1,
                more: true
            },
            // 表单校验
            rules: {
                type: [
                    {
                        required: true,
                        message: this.$t("form.select") + this.$t("bike.model.DeviceKind"),
                        trigger: "change"
                    }
                ],
                modelName: [
                    {
                        required: true,
                        // message: this.$t("form.select") + this.isModelName,
                        trigger: "change"
                    }
                ],
                versionName: [
                    {
                        required: true,
                        message: this.$t("form.input") + this.$t("system.app.versionNum"),
                        trigger: "blur"
                    }
                ],
                versionCode: [
                    {
                        required: true,
                        message: this.$t("form.input") + this.$t("system.app.versionSn"),
                        trigger: "blur"
                    }
                ],
                downLink: [
                    {
                        required: true,
                        message:
                            this.$t("form.upload") + this.$t("system.computer.downLink2"),
                        trigger: "blur"
                    }
                ]
            }
        }
    },
    computed: {
        isModelName() {
            let labelName = null;
            switch (this.form.type) {
                case 1:
                    labelName = this.$t("deviceType.IOTModel");
                    break;
                case 2:
                    labelName = this.$t("deviceType.helmetModel");
                    break;
                case 3:
                    labelName = this.$t("deviceType.radarModel");
                    break;
                default:
                    labelName = this.$t("acc.log.deviceModel");
            }
            this.rules.modelName[0].message = this.$t("form.select") + labelName;
            return labelName;
        }
    },
    watch: {
        "form.type"(type) {
            switch (type) {
                case 1:
                    this.modelFn = typeList;
                    break;
                case 2:
                    this.modelFn = helmetModelList;
                    break;
                case 3:
                    this.modelFn = radarList;
                    break;
            }
        }
    },
    methods: {
        // 设备型号
        getModelList({ page = 1, more = false, keyword = "" } = {}) {
            return new Promise((resolve, reject) => {
                this.modelFn({ p: page, key: keyword }).then(res => {
                    const { list, total, pageNum, pageSize } = res.data;
                    if (more) {
                        this.modelData.data = [...this.modelData.data, ...list];
                    } else {
                        this.modelData.data = list;
                    }
                    this.modelData.more = pageNum * pageSize < total;
                    this.modelData.page = pageNum;
                    resolve();
                });
            });
        },
        getModelInfo(info) {
            if (!info) {
                this.form.modelId = "";
                this.form.modelName = "";
                return;
            }
            const { id, code } = JSON.parse(info);
            this.form.modelId = id;
            this.form.modelName = code;
        },
        clearModelName() {
            this.modelData.data = [];
            this.$set(this.form, 'modelName', '');
        }
    }
}