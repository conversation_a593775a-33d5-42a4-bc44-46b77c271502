<template>
  <div class="app-container">
    
  </div>
</template>

<script>
import { detailBike } from "@/api/bike/bike";
// import { listDictCustomer,listDictBrand,listDictComputer } from "@/api/base/dict";

export default {
  name: "BikeBike",
  data() {
    return {
      // 遮罩层
      loading: true,
      detailData: {},
      // 表单参数
      form: {}
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      detailBike(this.$route.query.id).then(response => {
        this.detailData = response.data;
      });
    }
  }
};
</script>
