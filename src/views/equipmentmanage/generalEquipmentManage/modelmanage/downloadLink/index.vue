<template>
  <div class="app-container">
    <el-dialog
      :title="$t('bike.model.particulars')"
      :visible.sync="dialogTableVisible"
      width="880px"
      :close-on-click-modal="false"
    >
      <el-table v-loading="loading" :data="detailData">
        <el-table-column
          :label="$t('bike.computer.instructionBook')"
          align="center"
          prop="name"
        />
        <el-table-column
          :label="$t('acc.user.country')"
          align="center"
          prop="countryName"
        >
          <template slot-scope="{ row }">
            {{ row.countryName }}({{ row.countryEnName }})
          </template>
        </el-table-column>
        <!--     <el-table-column
          :label="$t('bike.model.activatedState')"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column> -->
        <el-table-column
          :label="$t('bike.model.createBy')"
          align="center"
          prop="createBy"
        />
        <el-table-column
          :label="$t('bike.model.createTime')"
          align="center"
          sortable
          prop="createTime"
        >
          <span
            slot-scope="scope"
            v-NoData="parseTime(scope.row.createTime)"
          ></span>
        </el-table-column>
        <el-table-column
          :label="$t('bike.model.operation')"
          align="center"
          width="170px"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              v-debounce-click="() => urlDownload(scope.row.link)"
            >
              {{ $t("system.computer.downLink") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { instrSearch, instrAuth } from "@/api/system/config";
export default {
  name: "modelDetail",
  data() {
    return {
      // 总条数
      dialogTableVisible: false,
      detailData: [],
      // 遮罩层
      loading: true
    };
  },

  methods: {
    /** 查询品牌列表 */
    getList(id) {
      this.loading = true;
      instrSearch({ modelId: id }).then(response => {
        this.detailData = response.data;
        this.loading = false;
      });
    },
    handleStatusChange(row) {
      let text =
        row.status === 1
          ? this.$t("bike.model.startUsing")
          : this.$t("bike.model.blockUp");
      this.$confirm(this.$t("bike.model.sure"), this.$t("bike.model.warn"), {
        confirmButtonText: this.$t("bike.model.confirm"),
        cancelButtonText: this.$t("bike.model.cancel"),
        type: this.$t("bike.model.warning")
      })
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = { id: row.id, status: row.status };
          data.push(authData);

          instrAuth(data).then(response => {
            this.msgSuccess(text + this.$t("bike.model.succeed"));
            this.loading = false;
          });
        })
        .catch(function() {
          row.status = row.status === 0 ? 1 : 0;
        });
    }
  }
};
</script>
<style lang="scss">
.bg-none {
  input {
    padding: 0;
    border-color: transparent !important;
    background: transparent !important;
  }
  .el-input,
  input {
    cursor: auto !important;
    color: #606266 !important;
  }
  .el-input__suffix {
    display: none;
  }
}
</style>
