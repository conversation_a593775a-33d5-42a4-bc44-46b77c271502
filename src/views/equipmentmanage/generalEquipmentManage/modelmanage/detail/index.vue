<template>
  <div class="app-container">
    <el-dialog
      :close-on-click-modal="false"
      :title="$t('bike.model.particulars')"
      :visible.sync="dialogTableVisible"
      width="880px"
    >
      
    </el-dialog>
  </div>
</template>

<script>
import { detailModel } from "@/api/bike/model";
export default {
  name: "modelDetail",
  data() {
    return {
      itemData: {},
      dialogTableVisible: false,
      detailData: {},
      // 遮罩层
      loading: true,
    };
  },

  methods: {
    /** 查询品牌列表 */
    getList(id, item) {
      this.loading = true;
      this.itemData = item;

      detailModel(id).then((response) => {
        this.detailData = response.data;
        this.dialogTableVisible = true;
        this.loading = false;
      });
    },

    spImgs(val) {
      if (val) {
        return val.split(",");
      }
      return [];
    },
    fileListSp(val) {
    return   this.spImgs(val).map((item) => {
        return { url: item };
      });
    }, 
    sImgs(val) {
      if (val) {
        return val.split(",")[0];
      }
      return "";
    },
  },
};
</script>
<style lang='scss'  >
.bg-none {
  input {
    padding: 0;
    border-color: transparent !important;
    background: transparent !important;
  }
  .el-input,
  input {
    cursor: auto !important;
    color: #606266 !important;
  }
  .el-input__suffix {
    display: none;
  }
}
</style>
