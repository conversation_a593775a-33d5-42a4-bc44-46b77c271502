<template>
  <div class="app-container">
    <el-dialog
      :title="$t('bike.computer.instructionBook')"
      :visible.sync="dialogTableVisible"
      :close-on-click-modal="false"
      center
      width="880px"
    >
      <el-table v-loading="loading" :height="500" :data="detailData">
        <el-table-column
          :label="$t('bike.computer.instructionBook')"
          align="center"
          prop="name"
        />
        <el-table-column
          :label="$t('acc.user.country')"
          align="center"
          prop="countryName"
        >
          <template slot-scope="{ row }">
            {{ row.countryName }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('bike.model.activatedState')"
          align="center"
        >
          <template v-slot="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="0"
              :inactive-value="1"
              @change="handleStatusChange(row, aFn, getList)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('bike.model.createBy')"
          align="center"
          prop="createBy"
        >
          <template v-slot="{ row }">
            {{ row.createBy || row.updateBy }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('bike.model.createTime')"
          align="center"
          sortable
          prop="createTime"
        >
          <template slot-scope="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('bike.model.operation')" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="urlDownload(scope.row.link)">
              {{ $t("system.computer.downLink") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { instrSearch, instrAuth } from "@/api/system/config";
import { commonJs } from "@/mixinFile/common";
export default {
  name: "modelDetail",
  mixins: [commonJs],
  data() {
    return {
      aFn: instrAuth,
      modelId: "",
      // 总条数
      dialogTableVisible: false,
      detailData: []
    };
  },

  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      instrSearch({ modelId: this.modelId }).then(response => {
        this.detailData = response.data;
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
