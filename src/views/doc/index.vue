<template>
  <div class="doc-container">
    <iframe 
      :src="documentUrl" 
      frameborder="0" 
      class="doc-iframe"
      ref="docIframe"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: "doc",
  props: {
    // 文档类型：完全由传入的配置决定
    docType: {
      type: String,
      default: 'privacy-policy'
    },
    // 语言版本：完全由传入的配置决定
    langVersion: {
      type: String,
      default: ''
    },
    // 是否显示标题和语言切换器
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 优先从URL参数中获取，如果没有则使用props
      currentType: this.$route.query.type || this.docType,
      currentLang: this.$route.query.lang || this.langVersion
    };
  },
  computed: {
    // 文档URL
    documentUrl() {
      // 完全由传入的配置决定
      let url = `/${this.currentType}`;
      if (this.currentLang) {
        url += `-${this.currentLang}`;
      }
      url += '.html';
      return url;
    },
    // 文档标题
    title() {
      // 如果没有明确的标题映射，则使用当前类型作为标题
      return this.currentType || 'Document';
    }
  },
  watch: {
    // 监听props变化
    docType(newVal) {
      this.currentType = newVal;
    },
    langVersion(newVal) {
      this.currentLang = newVal;
    },
    // 监听URL参数变化
    '$route.query.type'(newVal) {
      if (newVal) {
        this.currentType = newVal;
      }
    },
    '$route.query.lang'(newVal) {
      if (newVal !== undefined) {
        this.currentLang = newVal;
      }
    }
  },
  methods: {
    // 切换语言
    switchLanguage(lang) {
      // 根据您定义的语言版本命名规则切换
      const langVersionMap = {
        'zh': '',  // 可以根据您的需要修改
        'en': 'en' // 可以根据您的需要修改
      };
      
      const newLangVersion = langVersionMap[lang] || '';
      if (this.currentLang !== newLangVersion) {
        this.currentLang = newLangVersion;
        // 更新URL参数而不重新加载页面
        this.$router.push({
          query: { 
            ...this.$route.query,
            lang: newLangVersion || undefined // 如果为空字符串，则从URL中移除该参数
          }
        }).catch(err => {
          // 忽略导航到相同路由的错误
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
        this.$emit('language-change', newLangVersion);
      }
    }
  }
}
</script>

<style scoped>
.doc-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
 


.doc-iframe {
  flex: 1;
  width: 100%;
  border: none;
  height: calc(100vh - 100px);
}
</style>