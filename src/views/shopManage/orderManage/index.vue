<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('shopManage.searchTxt.order')" prop="orderNo">
        <el-input
          v-model.trim="queryParams.orderNo"
          :placeholder="$t('form.input') + $t('shopManage.searchTxt.order')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        :label="$t('shopManage.searchTxt.userIdOrName')"
        prop="user"
      >
        <el-input
          v-model.trim="queryParams.user"
          :placeholder="
            $t('form.input') + $t('shopManage.searchTxt.userIdOrName')
          "
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        :label="$t('shopManage.searchTxt.goodName')"
        prop="productName"
      >
        <el-input
          v-model.trim="queryParams.productName"
          :placeholder="$t('form.input') + $t('shopManage.searchTxt.goodName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('shopManage.searchTxt.status')" prop="state">
        <el-select
          v-model="queryParams.state"
          :placeholder="$t('form.select') + $t('shopManage.searchTxt.status')"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in stateList"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("acc.user.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("acc.user.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
    
    <el-table v-loading="loading" :height="tableHeight()" :data="userList">
      <el-table-column
        :label="$t('shopManage.searchTxt.order')"
        prop="orderNo"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.tableTxt.userId')"
        prop="userId"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.searchTxt.goodName')"
        prop="productName"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.tableTxt.payChannel')"
        prop="payChannel"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.payChannel === '10' ? 'primary' : 'danger'">
            {{ row.payChannel === "10" ? "GooglePay" : "ApplePay" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.amount')"
        prop="amount"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.searchTxt.status')"
        align="center"
        prop="state"
      >
        <template slot-scope="{ row }">
          <el-tag v-show="!is_Empty(row.state)" :type="isStateType(row.state)">
            {{ stateList[row.state] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.orderTime')"
        align="center"
        sortable
        prop="orderTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.orderTime) || '- - -' }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.payTime')"
        align="center"
        sortable
        prop="payTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.payTime) || '- - -'}}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.detail')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleDetail(scope.row)"
            hasPerminone="['acc:user:query']"
          >
            {{ $t("acc.user.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
    <CompDetail ref="compDetail" />
  </div>
</template>

<script>
import { listSysOrder } from "@/api/shopManage/index";
import CompDetail from "./detail/index";
import { commonJs } from "@/mixinFile/common";
export default {
  mixins: [commonJs],
  components: {
    CompDetail
  },
  data() {
    return {
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      stateList: {
        0: this.$t("shopManage.stateList.waitPay"),
        1: this.$t("shopManage.stateList.payed"),
        2: this.$t("shopManage.stateList.payError"),
        3: this.$t("shopManage.stateList.cancelPay")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        orderNo: null,
        user: null,
        productName: null,
        state: null
      }
    };
  },
  computed: {
    isStateType() {
      return state => {
        switch (state) {
          case 0:
            return "info";
          case 1:
            return "success";
          case 2:
            return "danger";
          case 3:
            return "warning";
        }
      };
    }
  },
  created() {
    if (this.$route.query.nickName) {
      this.queryParams.key = this.$route.query.nickName;
    }
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listSysOrder(this.queryParams).then(response => {
        this.userList = response.data.list;
        this.total = response.data.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleDetail(row) {
      this.$refs["compDetail"].dialogVisble = true;
      this.$refs["compDetail"].getList(row.id);
    }
  }
};
</script>
