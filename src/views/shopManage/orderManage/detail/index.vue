<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="dialogVisble"
    width="800px"
  >
    <el-descriptions
      class="margin-top"
      :title="$t('shopManage.searchTxt.orderInfo')"
      direction="vertical"
      :column="3"
      size="mini"
      border
      v-if="isSvsDetailObj"
    >
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.searchTxt.order") }}
        </template>
        {{ svsDetailObj.orderNo }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.userId") }}
        </template>
        {{ svsDetailObj.userId }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.searchTxt.goodName") }}
        </template>
        {{ svsDetailObj.productName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.payChannel") }}
        </template>
        <el-tag :type="svsDetailObj.payChannel === '10' ? 'primary' : 'danger'">
          {{ svsDetailObj.payChannel === "10" ? "GooglePay" : "ApplePay" }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.amount") }}
        </template>
        {{ svsDetailObj.amount }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.orderTime") }}
        </template>
        {{ parseTime(svsDetailObj.orderTime) }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.payTime") }}
        </template>
        {{ parseTime(svsDetailObj.payTime) }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.searchTxt.status") }}
        </template>
        <el-tag
          v-show="!$IS_Empty(svsDetailObj.state)"
          :type="isStateType(svsDetailObj.state)"
        >
          {{ stateList[svsDetailObj.state] }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      class="margin-top"
      :title="$t('shopManage.searchTxt.goodsSnapshot')"
      direction="vertical"
      :column="3"
      size="mini"
      border
      v-if="isSvsDetailObj"
    >
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.searchTxt.goodName") }}（ch）
        </template>
        {{ svsDetailObj.productSnapshot.name }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.searchTxt.goodName") }}（en）
        </template>
        {{ svsDetailObj.productSnapshot.nameEn }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.price") }}（ch）
        </template>
        {{ svsDetailObj.productSnapshot.price }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.price") }}（en）
        </template>
        {{ svsDetailObj.productSnapshot.priceEn }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          {{ $t("shopManage.tableTxt.name") }}
        </template>
        {{ svsDetailObj.productSnapshot.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
import { SysOrderDetail } from "@/api/shopManage/index";

export default {
  data() {
    return {
      dialogVisble: false,
      loading: false,
      svsDetailObj: {},
      stateList: {
        0: "待付款",
        1: "已付款",
        2: "交易失败",
        3: "取消付款",
      },
    };
  },
  computed: {
    isStateType() {
      return (state) => {
        switch (state) {
          case 0:
            return "info";
          case 1:
            return "success";
          case 2:
            return "danger";
          case 3:
            return "warning";
        }
      };
    },
    isSvsDetailObj() {
      return Object.keys(this.svsDetailObj).length > 0;
    },
  },
  mounted() {},
  methods: {
    /** 查询品牌列表 */
    getList(id) {
      this.loading = true;
      SysOrderDetail(id).then((response) => {
        this.svsDetailObj = response.data;
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss" scope>
.form-line {
  .el-form-item {
    border-bottom: 1px solid #ddd;
    margin-bottom: 0;
  }
}
</style>
