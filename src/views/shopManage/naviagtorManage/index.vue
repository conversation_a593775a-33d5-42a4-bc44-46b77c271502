<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item
          :label="$t('shopManage.searchTxt.userIdOrName')"
          prop="userInfo"
        >
          <el-input
            v-model.trim="queryParams.userInfo"
            :placeholder="
              $t('form.input') + $t('shopManage.searchTxt.userIdOrName')
            "
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          :label="$t('acc.user.bikeName')"
          prop="bikeName"
          v-show="isName === 'IOTEquity'"
        >
          <el-input
            v-model.trim="queryParams.bikeName"
            :placeholder="$t('form.input') + $t('acc.user.bikeName')"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("acc.user.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("acc.user.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="userList">
      <el-table-column
        :label="$t('navigatorManage.tableTxt.userName')"
        prop="userName"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.userName"></span>
      </el-table-column>
      <template v-if="isName === 'IOTEquity'">
        <el-table-column
          :label="$t('acc.user.bikeName')"
          prop="bikeName"
          align="center"
        />
        <el-table-column
          :label="$t('navigatorManage.tableTxt.IOTName')"
          prop="iotName"
          align="center"
        />
      </template>

      <el-table-column
        :label="$t('navigatorManage.tableTxt.payChannel')"
        prop="ProductSnapshot"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-link type="primary" @click="handleDetail(row.rightsProduct)">
            {{ $t("navigatorManage.tableTxt.ProductSnapshot") }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('navigatorManage.tableTxt.num')"
        prop="num"
        align="center"
      />
      <el-table-column
        :label="$t('system.notice.noticeType')"
        align="center"
        prop="type"
      >
        <template slot-scope="{ row }">
          <el-tag v-show="!is_Empty(row.type)" :type="isTagType(row.type)">
            {{ typeList[row.type] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.searchTxt.status')"
        align="center"
        prop="state"
      >
        <template slot-scope="{ row }">
          <el-tag v-show="!is_Empty(row.state)" :type="isTagType(row.state)">
            {{ stateList[row.state] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('navigatorManage.tableTxt.startTime')"
        align="center"
        sortable
        prop="startTime"
      >
        <span
          slot-scope="scope"
          v-NoData="parseTime(scope.row.startTime)"
        ></span>
      </el-table-column>
      <el-table-column
        :label="$t('navigatorManage.tableTxt.residueDate')"
        align="center"
        sortable
        prop="residueDate"
      >
        <span
          slot-scope="scope"
          v-NoData="parseTime(scope.row.residueDate)"
        ></span>
      </el-table-column>
      <el-table-column
        :label="$t('navigatorManage.tableTxt.opt')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            class="text-red"
            type="text"
            @click="handleUntie(row, 3)"
            hasPerminone="['acc:user:query']"
          >
            {{ $t("navigatorManage.optionTxt.freeze") }}
          </el-button>
          <el-button
            type="text"
            @click="handleUntie(row, 1)"
            hasPerminone="['acc:user:query']"
          >
            {{ $t("navigatorManage.optionTxt.recover") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
    <CompDetail ref="compDetail" />
  </div>
</template>

<script>
import { listSvsTights, svsTighAuth } from "@/api/shopManage/index";
import { tightsList, tightsAuth } from "@/api/iot/iot";
import CompDetail from "./detail/index";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    CompDetail
  },
  data() {
    return {
      aFn: null,
      aAuthFn: null,
      isName: null,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      stateList: {
        1: this.$t("navigatorManage.stateList.inEffect"),
        2: this.$t("navigatorManage.stateList.frozen"),
        3: this.$t("navigatorManage.stateList.expired")
      },
      typeList: {
        1: this.$t("navigatorManage.typeList.navigation"),
        2: this.$t("navigatorManage.typeList.skin"),
        3: this.$t("navigatorManage.typeList.other")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        userInfo: ""
      }
    };
  },
  computed: {
    isTagType() {
      return state => {
        switch (state) {
          case 1:
            return "success";
          case 2:
            return "danger";
          case 3:
            return "warning";
        }
      };
    }
  },
  watch: {
    $route: {
      handler({ name }) {
        this.isName = name;
        switch (name) {
          case "IOTEquity":
            this.aFn = tightsList;
            this.aAuthFn = tightsAuth;
            break;
          case "NaviagtorManage":
            this.aFn = listSvsTights;
            this.aAuthFn = svsTighAuth;
            break;
        }
      },
      immediate: true
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.aFn(this.queryParams)
        .then(response => {
          this.userList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    handleDetail(row) {
      this.$refs["compDetail"].dialogVisble = true;
      this.$refs["compDetail"].rowData = JSON.parse(row);
    },

    handleUntie({ id, residueDate }, status) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      }).then(() => {
        this.aAuthFn([{ id, status, residueDate }]).then(() => {
          const txt =
            status === 3
              ? this.$t("navigatorManage.optionTxt.freeze")
              : this.$t("navigatorManage.optionTxt.recover");
          this.msgSuccess(txt + this.$t("acc.user.succeed"));
          this.getList();
        });
      });
    }
  }
};
</script>
