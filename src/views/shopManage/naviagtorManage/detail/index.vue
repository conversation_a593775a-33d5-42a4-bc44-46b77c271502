<template>
	<el-dialog
	  :close-on-click-modal="false"
	  :visible.sync="dialogVisble"
	  width="800px"
	  title="商品快照详情"
	  center
	>
	  <el-descriptions
		class="margin-top"
		:column="5"
		direction="vertical"
		size="mini"
		border
	  >
		<el-descriptions-item
		  label-class-name="text-green"
		  :label="$t('shopManage.searchTxt.goodName') + `（ch）`"
		>
		  <span v-NoData="rowData.name"></span>
		</el-descriptions-item>
  
		<el-descriptions-item
		  label-class-name="text-green"
		  :label="$t('shopManage.searchTxt.goodName') + `（en）`"
		>
		  <span v-NoData="rowData.nameEn"></span>
		</el-descriptions-item>
  
		<el-descriptions-item
		  label-class-name="text-green"
		  :label="$t('shopManage.tableTxt.price') + `（ch）`"
		>
		  <span v-NoData="rowData.price"></span>
		</el-descriptions-item>
  
		<el-descriptions-item
		  label-class-name="text-green"
		  :label="$t('shopManage.tableTxt.price') + `（en）`"
		>
		  <span v-NoData="rowData.priceEn"></span>
		</el-descriptions-item>
  
		<el-descriptions-item
		  label-class-name="text-green"
		  :label="$t('shopManage.tableTxt.name')"
		>
		  <span v-NoData="rowData.remark"></span>
		</el-descriptions-item>
	  </el-descriptions>
	</el-dialog>
  </template>
  
  <script>
  export default {
	data() {
	  return {
		dialogVisble: false,
		loading: false,
		rowData: {}
	  };
	}
  };
  </script>