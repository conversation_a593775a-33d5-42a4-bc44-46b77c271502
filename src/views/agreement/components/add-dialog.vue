<template>
  <div class="bike_computer-add-dialog">
    <div class="el-dialog-body">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('agreement.language')" prop="language">
              <el-select
                v-model="form.language"
                :placeholder="$t('form.select') + $t('agreement.language')"
                clearable
                style="width: 100%"
                size="mini"
              >
                <el-option
                  v-for="(item, index) in countryList"
                  :key="index"
                  :label="item.dictLabel"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('system.notice.noticeType')" prop="type">
              <el-select
                v-model="form.type"
                clearable
                :placeholder="
                  $t('form.select') + $t('system.notice.noticeType')
                "
              >
                <el-option
                  v-for="dict in medalOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('bike.computer.createBy')" prop="createBy">
              <el-input
                v-model.trim="form.createBy"
                clearable
                :placeholder="$t('form.input') + $t('bike.computer.createBy')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.computer.downLink')" prop="link">
              <DrUpload
                v-model="form.link"
                :limit="1"
                :isOnePic="1"
                :css="{ width: '100%' }"
                class="flex-direction align-start"
              >
                <el-button size="small" type="primary">
                  {{ $t("queryParams.upload") }}
                </el-button>
              </DrUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="el-dialog__footer flex justify-center margin-top-xs">
      <el-button type="primary" :loading="isBtnLoading" @click="save">
        {{ $t("acc.msg.msg._confirm") }}
      </el-button>
      <el-button @click="close">
        {{ $t("acc.msg.msg._cancel") }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { logCreate, logEdit } from "@/api/agreement";
import { instrCountry } from "@/api/system/config";
export default {
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isBtnLoading: false,
      countryList: [],
      medalOptions: [
        {
          dictLabel: this.$t("agreement.blueTooth"),
          dictValue: 0
        },
        {
          dictLabel: this.$t("agreement.scan"),
          dictValue: 1
        },
        {
          dictLabel: this.$t("agreement.privacy"),
          dictValue: 2
        },
        {
          dictLabel: this.$t("agreement.user"),
          dictValue: 3
        }
      ],
      form: {},
      // 表单校验
      rules: {
        link: [
          {
            required: true,
            message: this.$t("agreement.uploadFile"),
            trigger: "change"
          }
        ],
        language: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("agreement.language"),
            trigger: "change"
          }
        ],
        type: [
          {
            required: true,
            message:
              this.$t("form.input") + this.$t("system.notice.noticeType"),
            trigger: "change"
          }
        ]
      }
    };
  },
  watch: {
    "form.link"(link) {
      if (link) {
        this.clearValidateItem("form", "link");
      }
    }
  },
  created() {
    this.getInstrCountry();
    // 编辑模式
    if (this.rowData.id) {
      this.rowData.language = +this.rowData.language;
      this.form = { ...this.rowData };
    }
  },
  methods: {
    getInstrCountry() {
      instrCountry().then(res => {
        this.countryList = res.data;
      });
    },
    close() {
      this.$emit("close");
      this.isBtnLoading = false;
    },
    save() {
      this.$refs["form"].validate(async valid => {
        if (valid) {
          this.isBtnLoading = true;
          let params = this.form;
          let res;
          // 修改
          if (this.rowData.id) {
            params.id = this.rowData.id;
            res = await logEdit(params);
            if (res.code === 200) {
              this.isBtnLoading = false;
              this.msgSuccess(this.$t("bike.computer.updateSucceed"));
              this.$emit("refresh");
              this.$emit("close");
            }
          } else {
            // 保存
            res = await logCreate(params);
            if (res.code === 200) {
              this.isBtnLoading = false;
              this.msgSuccess(this.$t("bike.computer.addSucceed"));
              this.$emit("refresh");
              this.$emit("close");
            }
          }
        }
      });
    }
  }
};
</script>

<style lang="scss">
.bike_computer-add-dialog {
  ::v-deep .el-col {
    .el-select {
      width: 100%;
    }
  }
}
.avatar-uploader-intro {
  .el-upload {
    width: auto;
    height: auto;
    line-height: 70px;
    display: flex;
  }
}
</style>
