<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('upgradeLog.documentType')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="$t('form.select') + $t('upgradeLog.documentType')"
          clearable
        >
          <el-option
            v-for="dict in medalOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("bike.computer.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("bike.computer.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("bike.computer.newAdd") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="computerList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        width="65"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('agreement.language')"
        prop="countryName"
        align="center"
      />
      <el-table-column
        :label="$t('upgradeLog.documentType')"
        prop="type"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ medalOptions[row.type].dictLabel }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('system.computer.downLink')"
        prop="link"
        align="center"
      >
        <template v-slot="{ row }">
          <el-image
            v-if="row.link"
            style="width: 30px; height: 30px; cursor: pointer"
            :src="require('@/assets/image/xiazai.png')"
            v-debounce-click="() => urlDownload(row.link)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.createBy')"
        align="center"
        prop="createBy"
      >
        <span slot-scope="scope" v-NoData="scope.row.createBy" />
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.createTime')"
        align="center"
        prop="createTime"
        sortable
      >
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)" />
      </el-table-column>
      <el-table-column :label="$t('bike.computer.operation')" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            hasPerminone="['bike:computer:edit']"
            @click="handleUpdate(scope.row)"
          >
            {{ $t("bike.computer.update") }}
          </el-button>
          <el-button type="text" class="text-red" @click="handleDel(scope.row)">
            {{ $t("queryParams.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      v-bind="dialogOption"
      :view.sync="dialogOption.view"
      :visible.sync="dialogOption.show"
      @close="closeDynamicDialog"
      append-to-body
      class="el-dialog-dynamic"
      :close-on-click-modal="false"
      center
    >
      <component
        :is="dialogOption.view"
        :rowData="currentRow"
        @refresh="getList"
        @close="closeDynamicDialog"
      >
      </component>
    </el-dialog>
  </div>
</template>

<script>
import AddDialog from "./components/add-dialog";
import { logList, logDel } from "@/api/agreement";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "BikeComputer",
  mixins: [commonJs],
  components: {
    AddDialog
  },
  data() {
    return {
      // 用户表格数据
      computerList: [],
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: ""
      },
      medalOptions: [
        {
          dictLabel: this.$t("agreement.blueTooth"),
          dictValue: "0"
        },
        {
          dictLabel: this.$t("agreement.scan"),
          dictValue: "1"
        },
        {
          dictLabel: this.$t("agreement.privacy"),
          dictValue: "2"
        },
        {
          dictLabel: this.$t("agreement.user"),
          dictValue: "3"
        }
      ],
      // 表单参数
      form: {},
      // 当前修改的数据
      currentRow: {},
      dialogOption: {
        width: "",
        title: "",
        show: false,
        view: ""
      }
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        logDel(row.id).then(() => {
          this.msgSuccess(
            this.$t("queryParams.delete") + this.$t("bike.computer.succeed")
          );
          this.loading = false;
          this.getList();
        });
      });
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      logList(this.queryParams).then(response => {
        this.computerList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /**
     * 新增
     */
    handleAdd() {
      this.currentRow = {};
      this.showDynamicDialog(
        "AddDialog",
        this.$t("upgradeLog.addUpgradeLog"),
        "600px"
      );
    },
    /**
     * 修改
     */
    handleUpdate(row) {
      this.currentRow = row;
      this.showDynamicDialog(
        "AddDialog",
        this.$t("upgradeLog.resetUpgradeLog"),
        "600px"
      );
    },
    showDynamicDialog(view, title, width = "1200px") {
      this.dialogOption.show = true;
      this.dialogOption.view = view;
      this.dialogOption.title = title;
      this.dialogOption.width = width;
    },
    closeDynamicDialog() {
      this.dialogOption.show = false;
      this.dialogOption.view = null;
    }
  }
};
</script>
