<template>
  <el-dialog
    :title="isBackLightTitle"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    center
  >
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:computer:add']"
        >
          {{ $t("bike.computer.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("tagsView.refresh") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :max-height="500" :data="list">
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.backLight.computerLight')"
        prop="computerGear"
        align="center"
      />
      <el-table-column
        :label="$t('bike.backLight.viewLight')"
        align="center"
        prop="viewGear"
      />
      <el-table-column :label="$t('bike.gear.def')" align="center" prop="def">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.def == 1" type="success" >{{ $t("bike.gear.yes") }}</el-tag>
          <el-tag v-else type="danger" >{{ $t("bike.gear.no") }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('bike.model.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('discover.posted.handle')"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row)">
            {{ $t("bike.computer.update") }}
          </el-button>
          <el-button type="text" class="text-red" @click="handleDel(scope.row)">
            {{ $t("bike.info.del") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          :label="$t('bike.backLight.computerLight')"
          prop="computerGear"
        >
          <el-input
            v-model.trim="form.computerGear"
            clearable
            :placeholder="
              $t('system.user.input') + $t('bike.backLight.computerLight')
            "
          />
        </el-form-item>

        <el-form-item :label="$t('bike.backLight.viewLight')" prop="viewGear">
          <el-input
            v-model.trim="form.viewGear"
            clearable
            :placeholder="
              $t('system.user.input') + $t('bike.backLight.viewLight')
            "
          />
        </el-form-item>
        <el-form-item :label="$t('bike.gear.def')" prop="def">
          <el-radio-group v-model="form.def">
            <el-radio :label="1">{{ $t("bike.gear.yes") }}</el-radio>
            <el-radio :label="0">{{ $t("bike.gear.no") }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.computer._confirm") }}
        </el-button>
        <el-button @click="open = false">
          {{ $t("bike.computer._cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  listGear,
  deleteGear,
  addGear,
  updateGear
} from "@/api/model/bikeLight";
export default {
  props: {
    backLightName: {
      type: String,
      default: "",
      required: true
    }
  },
  data() {
    return {
      isBtnLoading: false,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      dialogVisible: false,
      // 查询参数
      queryParams: {
        groupId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        computerGear: [
          {
            required: true,
            message:
              this.$t("system.user.input") +
              this.$t("bike.backLight.computerLight"),
            trigger: "blur"
          }
        ],
        viewGear: [
          {
            required: true,
            message:
              this.$t("system.user.input") + this.$t("bike.backLight.viewLight"),
            trigger: "blur"
          }
        ],
        def: [
          {
            required: true,
            message: this.$t("system.user.select") + this.$t("bike.gear.def"),
            trigger: "change"
          }
        ]
      }
    };
  },
  computed: {
    isBackLightTitle() {
      return `${this.backLightName} - ${this.$t("bike.backLight.lightSet")}`;
    }
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listGear(this.queryParams).then(response => {
        this.list = response.data;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.form.groupId = this.queryParams.groupId;
      this.title = this.$t("bike.configure.add");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("bike.computer.update");
      this.form = Object.assign({}, row);
    },

    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        deleteGear(row.id).then(() => {
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
          this.getList();
        }).finally(() => {
          this.loading = false;
        });
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            updateGear(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addGear(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
