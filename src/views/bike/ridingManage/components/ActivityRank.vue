<!--
活动排名组件

使用方法：
1. 在父组件中引入：
   import ActivityRank from './components/ActivityRank.vue'

2. 在父组件中注册：
   components: {
     ActivityRank
   }

3. 在模板中使用：
   <ActivityRank ref="activityRank" />

4. 在方法中调用：
   handleActivityRank(row) {
     this.$refs.activityRank.openDialog(row.id, row.titleName);
   }
-->

<template>
  <el-dialog 
    :title="dialogTitle" 
    :visible.sync="visible" 
    width="900" 
    top="5vh"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="activity-rank">
      <!-- 搜索表单 -->
      <!-- <el-form :model="queryParams" ref="queryForm" :inline="true" class="mb16">
        <el-form-item :label="$t('bike.ridingManage.rank.nickName')" prop="nickName">
          <el-input 
            v-model.trim="queryParams.nickName" 
            clearable 
            @keyup.enter.native="handleQuery"
            style="width: 200px" 
            :placeholder="$t('form.input') + $t('bike.ridingManage.rank.nickName')" 
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            {{ $t("queryParams.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">
            {{ $t("queryParams.reset") }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-alert
            :title="$t('bike.ridingManage.rank.statisticsNote')"
            type="info"
            :closable="false"
            show-icon
            style="margin-left: 20px;"
          />
        </el-form-item>
      </el-form> -->

      <!-- 排名统计卡片 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="6">
          <div class="rank-card">
            <div class="rank-card-header">
              <i class="el-icon-trophy rank-icon gold"></i>
              <span class="rank-title">{{ $t('bike.ridingManage.rank.totalParticipants') }}</span>
            </div>
            <div class="rank-number">{{ total }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="rank-card">
            <div class="rank-card-header">
              <i class="el-icon-time rank-icon blue"></i>
              <span class="rank-title">{{ $t('bike.ridingManage.rank.avgTime') }}</span>
            </div>
            <div class="rank-number">{{ avgTime }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="rank-card">
            <div class="rank-card-header">
              <i class="el-icon-medal rank-icon silver"></i>
              <span class="rank-title">{{ $t('bike.ridingManage.rank.fastestTime') }}</span>
            </div>
            <div class="rank-number">{{ fastestTime }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="rank-card">
            <div class="rank-card-header">
              <i class="el-icon-star-on rank-icon orange"></i>
              <span class="rank-title">{{ $t('bike.ridingManage.rank.slowestTime') }}</span>
            </div>
            <div class="rank-number">{{ slowestTime }}</div>
          </div>
        </el-col>
      </el-row>

      <!-- 排名列表 -->
      <el-table 
        v-loading="loading" 
        :data="rankList" 
        max-height="400"
        row-class-name="rank-table-row"
      >
        <el-table-column 
          :label="$t('bike.ridingManage.rank.ranking')" 
          width="80" 
          align="center"
        >
          <template slot-scope="scope">
            <div class="rank-position">
              <i v-if="scope.$index === 0" class="el-icon-trophy rank-medal gold"></i>
              <i v-else-if="scope.$index === 1" class="el-icon-medal rank-medal silver"></i>
              <i v-else-if="scope.$index === 2" class="el-icon-medal rank-medal bronze"></i>
              <span v-else class="rank-number-text">{{ scope.$index + 1 }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column 
          :label="$t('bike.ridingManage.rank.userInfo')" 
          prop="nickName" 
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar 
                :src="scope.row.headImg || scope.row.consumerImg" 
                :size="40"
                class="user-avatar"
              >
                <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
              </el-avatar>
              <div class="user-details">
                <div class="user-name" v-NoData="scope.row.nickName"></div>
                <div class="user-id">ID: {{ scope.row.userId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column 
          :label="$t('bike.ridingManage.rank.spendTime')" 
          prop="spendTime" 
          align="center"
          width="150"
        >
          <template slot-scope="scope">
            <div class="time-display">
              <i class="el-icon-time time-icon"></i>
              <span class="time-text">{{ formatTime(scope.row.spendTime) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column 
          :label="$t('bike.ridingManage.rank.performance')" 
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <el-tag 
              :type="getPerformanceType(scope.$index)" 
              class="performance-tag"
            >
              {{ getPerformanceText(scope.$index) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column 
          :label="$t('bike.ridingManage.operation')"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <el-button 
              type="text" 
              icon="el-icon-view"
              @click="handleViewDetail(scope.row)"
              class="operation-btn"
            >
              {{ $t('bike.ridingManage.viewDetail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination 
        v-show="total > 0" 
        :total="total" 
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize" 
        @pagination="getRankList" 
      />

      <!-- 用户详情对话框 -->
      <el-dialog 
        :title="$t('bike.ridingManage.rank.userDetail')" 
        :visible.sync="detailVisible" 
        width="600px"
        append-to-body
      >
        <div v-if="currentUser" class="user-detail">
          <div class="detail-section">
            <h4>{{ $t('bike.ridingManage.rank.basicInfo') }}</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <label>{{ $t('bike.ridingManage.rank.nickName') }}:</label>
                  <span>{{ currentUser.nickName }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <label>{{ $t('bike.ridingManage.rank.userId') }}:</label>
                  <span>{{ currentUser.userId }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <div class="detail-section">
            <h4>{{ $t('bike.ridingManage.rank.performanceInfo') }}</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <label>{{ $t('bike.ridingManage.rank.spendTime') }}:</label>
                  <span>{{ formatTime(currentUser.spendTime) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <label>{{ $t('bike.ridingManage.rank.ranking') }}:</label>
                  <span>{{ currentRanking }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailVisible = false">{{ $t("dialog.cancel") }}</el-button>
        </div>
      </el-dialog>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t("dialog.cancel") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { commonJs } from "@/mixinFile/common";
import { getActivityRank } from "@/api/bike/ridingManage";

export default {
  name: "ActivityRank",
  mixins: [commonJs],
  data() {
    return {
      visible: false,
      dialogTitle: '',
      currentActivityId: null,
      loading: false,
      rankList: [],
      total: 0,
      avgTime: '00:00:00',
      fastestTime: '00:00:00',
      slowestTime: '00:00:00',
      detailVisible: false,
      currentUser: null,
      currentRanking: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: null,
        nickName: null
      }
    };
  },
  methods: {
    // 打开排名弹窗
    openDialog(activityId, activityName) {
      this.visible = true;
      this.currentActivityId = activityId;
      this.dialogTitle = `${this.$t('bike.ridingManage.rank.title')} - ${activityName}`;
      this.queryParams.activityId = activityId;
      this.queryParams.pageNum = 1;
      this.queryParams.nickName = null;
      this.getRankList();
    },

    // 获取排名列表
    getRankList() {
      this.loading = true;
      const queryParams = { 
        p: this.queryParams.pageNum,
        l: this.queryParams.pageSize,
        activityId: this.currentActivityId,
        nickName: this.queryParams.nickName
      };
      
      getActivityRank(queryParams).then(response => {
        const { data } = response;
        this.rankList = data.list || [];
        this.total = data.total || 0;
        
        // 获取统计数据（基于全部数据）
        this.getStatisticsData();
        this.loading = false;
      }).catch(error => {
        console.error('获取排名数据失败:', error);
        this.rankList = [];
        this.total = 0;
        this.loading = false;
      });
    },

    // 获取统计数据（基于全部数据）
    getStatisticsData() {
      // 如果没有数据，直接返回
      if (this.total === 0) {
        this.avgTime = '00:00:00';
        this.fastestTime = '00:00:00';
        this.slowestTime = '00:00:00';
        return;
      }

      // 获取全部数据用于统计计算
      const allDataParams = {
        p: 1,
        l: this.total, // 获取全部数据
        activityId: this.currentActivityId,
        nickName: undefined // 统计时不使用搜索条件
      };

      console.log('获取实时统计数据，活动ID:', this.currentActivityId, '总数据量:', this.total);

      getActivityRank(allDataParams).then(response => {
        const { data } = response;
        const allRankList = data.list || [];
        this.calculateStatistics(allRankList);
        console.log('统计数据已更新 - 平均:', this.avgTime, '最快:', this.fastestTime, '最慢:', this.slowestTime);
      }).catch(error => {
        console.error('获取统计数据失败:', error);
        // 如果获取全部数据失败，基于当前页数据计算（虽然不准确，但至少有数据）
        this.calculateStatistics(this.rankList);
      });
    },

    // 计算统计数据
    calculateStatistics(dataList = []) {
      if (dataList.length === 0) {
        this.avgTime = '00:00:00';
        this.fastestTime = '00:00:00';
        this.slowestTime = '00:00:00';
        return;
      }

      const times = dataList.map(item => item.spendTime).filter(time => time && time > 0);
      if (times.length === 0) {
        this.avgTime = '00:00:00';
        this.fastestTime = '00:00:00';
        this.slowestTime = '00:00:00';
        return;
      }

      // 平均时间
      const avgSeconds = times.reduce((sum, time) => sum + time, 0) / times.length;
      this.avgTime = this.formatTime(Math.floor(avgSeconds));

      // 最快时间
      this.fastestTime = this.formatTime(Math.min(...times));

      // 最慢时间
      this.slowestTime = this.formatTime(Math.max(...times));
    },

    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || seconds === 0) return '00:00:00';
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    // 获取性能标签类型
    getPerformanceType(index) {
      if (index === 0) return 'success';
      if (index <= 2) return 'warning';
      if (index <= 9) return 'info';
      return 'info';
    },

    // 获取性能标签文本
    getPerformanceText(index) {
      if (index === 0) return this.$t('bike.ridingManage.rank.excellent');
      if (index <= 2) return this.$t('bike.ridingManage.rank.good');
      if (index <= 9) return this.$t('bike.ridingManage.rank.normal');
      return this.$t('bike.ridingManage.rank.average');
    },

    // 查看用户详情
    handleViewDetail(row) {
      this.currentUser = row;
      this.currentRanking = this.rankList.findIndex(item => item.userId === row.userId) + 1;
      this.detailVisible = true;
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getRankList();
    },

    // 重置搜索
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>

<style scoped>
.activity-rank {
  padding: 20px;
}

.mb16 {
  margin-bottom: 16px;
}

.mb20 {
  margin-bottom: 20px;
}

/* 统计卡片样式 */
.rank-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.rank-card:hover {
  transform: translateY(-5px);
}

.rank-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.rank-icon {
  font-size: 24px;
  margin-right: 8px;
}

.rank-icon.gold {
  color: #ffd700;
}

.rank-icon.silver {
  color: #c0c0c0;
}

.rank-icon.blue {
  color: #409eff;
}

.rank-icon.orange {
  color: #ff9500;
}

.rank-title {
  font-size: 14px;
  opacity: 0.9;
}

.rank-number {
  font-size: 28px;
  font-weight: bold;
  margin-top: 5px;
}

/* 表格样式 */
::v-deep .rank-table-row {
  transition: all 0.3s ease;
}

::v-deep .rank-table-row:hover {
  background-color: #f5f7fa !important;
  transform: translateX(2px);
}

/* 排名位置样式 */
.rank-position {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-medal {
  font-size: 20px;
}

.rank-medal.gold {
  color: #ffd700;
}

.rank-medal.silver {
  color: #c0c0c0;
}

.rank-medal.bronze {
  color: #cd7f32;
}

.rank-number-text {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  border: 2px solid #e4e7ed;
  transition: border-color 0.3s ease;
}

.user-avatar:hover {
  border-color: #409eff;
}

.user-details {
  text-align: left;
}

.user-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

/* 时间显示样式 */
.time-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.time-icon {
  color: #409eff;
  font-size: 16px;
}

.time-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: bold;
  color: #303133;
}

/* 性能标签样式 */
.performance-tag {
  font-weight: bold;
  border-radius: 20px;
  padding: 4px 12px;
}

/* 操作按钮样式 */
.operation-btn {
  color: #409eff;
  transition: all 0.3s ease;
}

.operation-btn:hover {
  color: #66b1ff;
  transform: scale(1.05);
}

/* 详情对话框样式 */
.user-detail {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.detail-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
  min-width: 80px;
  margin-right: 12px;
}

.detail-item span {
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rank-card {
    margin-bottom: 16px;
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .time-display {
    flex-direction: column;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rank-card, .el-table {
  animation: fadeInUp 0.6s ease-out;
}
</style> 