<!--
活动详情预览组件

使用方法：
1. 在父组件中引入：
   import ActivityDetail from './components/ActivityDetail.vue'

2. 在父组件中注册：
   components: {
     ActivityDetail
   }

3. 在模板中使用：
   <ActivityDetail ref="activityDetail" />

4. 在方法中调用：
   handleActivityDetail(row) {
     this.$refs.activityDetail.open(row.id);
   }

API 接口：
- 接口地址：/activity/detail/{id}
- 请求方法：GET
- 返回格式：{ code: 200, data: {...}, msg: 'success' }

返回数据结构：
{
  code: 200,
  data: {
    id: "活动ID",
    titleName: "活动标题",
    type: 1, // 1官方 2个人
    cyclingType: 1, // 1里程 2路线
    status: 0, // 0正常 1禁用
    activityStatus: 0, // 0报名中 1已报名 2活动进行中 3已结束
    startTime: 1234567890, // 开始时间戳
    endTime: 1234567890, // 结束时间戳
    createTime: 1234567890, // 创建时间戳
    countNum: 10, // 参与人数
    distance: 50.5, // 里程(公里)
    detail: "活动详情描述",
    firstPrize: 1000, // 第一名奖金
    secondPrize: 500, // 第二名奖金
    thirdPrize: 200, // 第三名奖金
    expandTime: 2, // 延长时长(小时)
    purview: 0, // 0公开 1仅你 2朋友
    userName: "创建者姓名",
    createBy: "创建者",
    groupId: "群组ID",
    cover: "封面图片URL",
    adImg: "广告图片URL",
    pointImg: "轨迹照片URL",
    adUrl: "广告链接",
    list: [ // 途径点列表(仅路线类型有)
      {
        id: "途径点ID",
        name: "途径点名称",
        latitude: 39.9042,
        longitude: 116.4074,
        sort: 0,
        activityId: "活动ID",
        createTime: 1234567890
      }
    ]
  },
  msg: "success"
}
-->

<template>
  <div class="activity-detail">
    <el-dialog :title="$t('bike.ridingManage.activityDetail')" :visible.sync="visible" width="900px"
      :close-on-click-modal="false" top="3vh" custom-class="activity-detail-dialog">
      <div v-loading="loading" class="detail-content">
        <!-- 活动封面和基本信息 -->
        <div class="hero-section" v-if="activityData.cover">
          <div class="cover-image">
            <preview-img :imgUrl="activityData.cover" width="100%" height="100%" fit="cover" class="hero-image" />
            <div class="cover-overlay">
              <div class="activity-title">{{ activityData.titleName || '-' }}</div>
              <div class="activity-subtitle">
                <el-tag :type="getActivityStatusType(activityData.activityStatus)" size="medium">
                  {{ getActivityStatusText(activityData.activityStatus) }}
                </el-tag>
                <el-tag :type="activityData.status === 0 ? 'success' : 'danger'" size="medium">
                  {{ activityData.status === 0 ? $t('bike.ridingManage.normal') : $t('bike.ridingManage.disabled') }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-info"></i>
              <span>{{ $t('bike.ridingManage.basicInfo') }}</span>
            </div>
            <div class="card-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.type') }}</div>
                    <div class="info-value">
                      <el-tag type="primary" size="small">{{ getTypeText(activityData.type) }}</el-tag>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.cyclingType') }}</div>
                    <div class="info-value">
                      <el-tag type="warning" size="small">{{ getCyclingTypeText(activityData.cyclingType) }}</el-tag>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.purview') }}</div>
                    <div class="info-value">{{ getPurviewText(activityData.purview) }}</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.countNum') }}</div>
                    <div class="info-value highlight">
                      <i class="el-icon-user"></i>
                      {{ activityData.countNum || 0 }} {{ $t('bike.ridingManage.people') }}
                    </div>
                  </div>
                </el-col>
                <el-col :span="12" v-if="activityData.cyclingType === 1">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.distance') }}</div>
                    <div class="info-value highlight">
                      <i class="el-icon-location"></i>
                      {{ activityData.distance || 0 }} {{ $t('bike.ridingManage.km') }}
                    </div>
                  </div>
                </el-col>
                <el-col :span="12" v-if="activityData.expandTime">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.expandTime') }}</div>
                    <div class="info-value">
                      <i class="el-icon-time"></i>
                      {{ activityData.expandTime }} {{ $t('bike.ridingManage.hours') }}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 时间信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-date"></i>
              <span>{{ $t('bike.ridingManage.timeInfo') }}</span>
            </div>
            <div class="card-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.startTime') }}</div>
                    <div class="info-value">
                      <i class="el-icon-time"></i>
                      {{ formatTime(activityData.startTime) }}
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.endTime') }}</div>
                    <div class="info-value">
                      <i class="el-icon-time"></i>
                      {{ formatTime(activityData.endTime) }}
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.createTime') }}</div>
                    <div class="info-value">
                      <i class="el-icon-date"></i>
                      {{ formatTime(activityData.createTime) }}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 活动详情卡片 -->
          <div class="info-card" v-if="activityData.detail">
            <div class="card-header">
              <i class="el-icon-document"></i>
              <span>{{ $t('bike.ridingManage.detail') }}</span>
            </div>
            <div class="card-content">
              <div class="detail-text">{{ activityData.detail }}</div>
            </div>
          </div>

          <!-- 奖金信息卡片 -->
          <div class="info-card" v-if="activityData.firstPrize || activityData.secondPrize || activityData.thirdPrize">
            <div class="card-header">
              <i class="el-icon-trophy"></i>
              <span>{{ $t('bike.ridingManage.prizeInfo') }}</span>
            </div>
            <div class="card-content">
              <div class="prize-grid">
                <div class="prize-item" v-if="activityData.firstPrize">
                  <div class="prize-medal first">
                    <i class="el-icon-trophy"></i>
                  </div>
                  <div class="prize-info">
                    <div class="prize-rank">{{ $t('bike.ridingManage.firstPrize') }}</div>
                    <div class="prize-amount">¥{{ activityData.firstPrize }}</div>
                  </div>
                </div>
                <div class="prize-item" v-if="activityData.secondPrize">
                  <div class="prize-medal second">
                    <i class="el-icon-medal"></i>
                  </div>
                  <div class="prize-info">
                    <div class="prize-rank">{{ $t('bike.ridingManage.secondPrize') }}</div>
                    <div class="prize-amount">¥{{ activityData.secondPrize }}</div>
                  </div>
                </div>
                <div class="prize-item" v-if="activityData.thirdPrize">
                  <div class="prize-medal third">
                    <i class="el-icon-medal"></i>
                  </div>
                  <div class="prize-info">
                    <div class="prize-rank">{{ $t('bike.ridingManage.thirdPrize') }}</div>
                    <div class="prize-amount">¥{{ activityData.thirdPrize }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 路线信息卡片 -->
          <div class="info-card"
            v-if="activityData.cyclingType === 2 && activityData.list && activityData.list.length > 0">
            <div class="card-header">
              <i class="el-icon-map-location"></i>
              <span>{{ $t('bike.ridingManage.routeInfo') }}</span>
            </div>
            <div class="card-content">
              <div class="waypoints-list">
                <div v-for="(waypoint, index) in activityData.list" :key="waypoint.id" class="waypoint-item">
                  <div class="waypoint-index">{{ index + 1 }}</div>
                  <div class="waypoint-info">
                    <div class="waypoint-name">{{ waypoint.name || $t('bike.ridingManage.waypoint') + (index + 1) }}
                    </div>
                    <div class="waypoint-coords">
                      <i class="el-icon-location"></i>
                      {{ waypoint.longitude }}, {{ waypoint.latitude }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 创建者信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-user"></i>
              <span>{{ $t('bike.ridingManage.creatorInfo') }}</span>
            </div>
            <div class="card-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.creator') }}</div>
                    <div class="info-value">
                      <i class="el-icon-user"></i>
                      {{ activityData.userName || activityData.createBy || '-' }}
                    </div>
                  </div>
                </el-col>
                <el-col :span="12" v-if="activityData.groupId">
                  <div class="info-item">
                    <div class="info-label">{{ $t('bike.ridingManage.groupId') }}</div>
                    <div class="info-value">
                      <i class="el-icon-s-grid"></i>
                      {{ activityData.groupId }}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 图片信息卡片 -->
          <div class="info-card" v-if="activityData.cover || activityData.adImg || activityData.pointImg">
            <div class="card-header">
              <i class="el-icon-picture"></i>
              <span>{{ $t('bike.ridingManage.imageInfo') }}</span>
            </div>
            <div class="card-content">
              <div class="image-grid">
                <div class="image-item" v-if="activityData.cover">
                  <div class="image-label">{{ $t('bike.ridingManage.cover') }}</div>
                  <preview-img :imgUrl="activityData.cover" class="preview-image" />
                </div>
                <div class="image-item" v-if="activityData.adImg">
                  <div class="image-label">{{ $t('bike.ridingManage.adImg') }}</div>
                  <preview-img :imgUrl="activityData.adImg" class="preview-image" />
                </div>
                <div class="image-item" v-if="activityData.pointImg">
                  <div class="image-label">{{ $t('bike.ridingManage.pointImg') }}</div>
                  <preview-img :imgUrl="activityData.pointImg" class="preview-image" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false" type="primary" plain>{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getActivityDetail } from '@/api/bike/ridingManage'

export default {
  name: 'ActivityDetail',
  props: {
    activityId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      activityData: {}
    }
  },
  methods: {
    // 打开详情弹窗
    open(activityId) {
      this.visible = true
      if (activityId) {
        this.fetchActivityDetail(activityId)
      }
    },

    // 获取活动详情
    async fetchActivityDetail(activityId) {
      this.loading = true
      try {
        const response = await getActivityDetail(activityId)
        if (response.code === 200 && response.data) {
          this.activityData = response.data
        } else {
          this.$message.error(response.msg || this.$t('common.fetchFailed'))
        }
      } catch (error) {
        console.error('获取活动详情失败:', error)
        this.$message.error(this.$t('common.fetchFailed'))
      } finally {
        this.loading = false
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '-'
      // 处理秒级时间戳转换为毫秒
      const msTimestamp = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
      const date = new Date(msTimestamp);
      // 格式化日期为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        1: this.$t('bike.ridingManage.official'),
        2: this.$t('bike.ridingManage.personal')
      }
      return typeMap[type] || '-'
    },

    // 获取骑行类型文本
    getCyclingTypeText(cyclingType) {
      const cyclingTypeMap = {
        1: this.$t('bike.ridingManage.mileage'),
        2: this.$t('bike.ridingManage.route')
      }
      return cyclingTypeMap[cyclingType] || '-'
    },

    // 获取活动状态文本
    getActivityStatusText(status) {
      const statusMap = {
        0: this.$t('bike.ridingManage.registrationOpen'),
        1: this.$t('bike.ridingManage.registered'),
        2: this.$t('bike.ridingManage.inProgress'),
        3: this.$t('bike.ridingManage.ended')
      }
      return statusMap[status] || '-'
    },

    // 获取活动状态类型
    getActivityStatusType(status) {
      const typeMap = {
        0: 'success',
        1: 'warning',
        2: 'primary',
        3: 'info'
      }
      return typeMap[status] || 'info'
    },

    // 获取权限文本
    getPurviewText(purview) {
      const purviewMap = {
        0: this.$t('bike.ridingManage.public'),
        1: this.$t('bike.ridingManage.forYou'),
        2: this.$t('bike.ridingManage.friends')
      }
      return purviewMap[purview] || '-'
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-detail {
  .activity-detail-dialog {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }

    ::v-deep .el-dialog__header {
      background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
      color: white;
      padding: 24px 30px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMDIwMjAiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRoLTJ2LTRoMnY0em0wLTZ2LTRoLTJ2NGgyem0tNiAwdi00aC0ydiR2NGgyem02LTEyVjE4aC0ydiR2NGgyem0tNiAwVjE4aC0ydiR2NGgyem0tNiAwVjE4aC0ydiR2NGgyem0xMiAwdjRoLTJ2LTRoMnptLTYgMnY0aC0ydi00aDJ6Ii8+PC9nPjwvZz48L3N2Zz4=');
        opacity: 0.3;
      }

      .el-dialog__title {
        color: white;
        font-size: 22px;
        font-weight: 600;
        position: relative;
        z-index: 1;
      }

      .el-dialog__headerbtn .el-dialog__close {
        color: white;
        font-size: 20px;
        position: relative;
        z-index: 1;


      }
    }

    ::v-deep .el-dialog__body {
      padding: 0;
    }

    ::v-deep .el-dialog__footer {
      padding: 20px 30px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      display: flex;
      justify-content: center;
    }
  }

  .detail-content {
    max-height: 70vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #409eff #f0f0f0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f0f0f0;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #409eff;
      border-radius: 20px;
    }
  }

  .hero-section {
    position: relative;
    height: 240px;
    overflow: hidden;

    .cover-image {
      position: relative;
      width: 100%;
      height: 100%;

      .hero-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;


      }

      .cover-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        padding: 60px 30px 30px;
        color: white;

        .activity-title {
          font-size: 28px;
          font-weight: 600;
          margin-bottom: 15px;
          text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
          transform: translateY(0);
          opacity: 1;
          animation: fadeInUp 0.5s ease-out;
        }

        .activity-subtitle {
          display: flex;
          gap: 12px;
          transform: translateY(0);
          opacity: 1;
          animation: fadeInUp 0.5s ease-out 0.1s;
          animation-fill-mode: both;
        }
      }
    }
  }

  .main-content {
    padding: 30px;
  }

  .info-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid #f5f5f5;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 18px 24px;
      display: flex;
      align-items: center;
      gap: 12px;
      font-weight: 600;
      color: #2c3e50;
      border-bottom: 1px solid #f0f0f0;

      i {
        font-size: 18px;
        color: #409eff;
        transition: transform 0.3s ease;
      }


    }

    .card-content {
      padding: 24px;
    }

    .info-item {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px dashed #f0f0f0;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .info-label {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          font-size: 14px;
          color: #adb5bd;
        }
      }

      .info-value {
        font-size: 15px;
        color: #343a40;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.2s ease;

        &:hover {
          background-color: #e9ecef;
        }

        &.highlight {
          color: #409eff;
          font-weight: 600;
          background-color: rgba(64, 158, 255, 0.05);
          border-left: 3px solid #409eff;
        }

        i {
          color: #868e96;
          font-size: 16px;
        }
      }
    }

    .detail-text {
      color: #495057;
      line-height: 1.8;
      white-space: pre-wrap;
      background: #f8f9fa;
      padding: 20px;
      border-radius: 12px;
      border-left: 4px solid #409eff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      font-size: 15px;
    }

    .waypoints-list {
      .waypoint-item {
        display: flex;
        align-items: center;
        padding: 18px;
        margin-bottom: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: #409eff;
          transform: scaleY(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          background: #e6f7ff;
          border-color: #91d5ff;


        }

        &:last-child {
          margin-bottom: 0;
        }

        .waypoint-index {
          width: 36px;
          height: 36px;
          background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 14px;
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
          z-index: 1;
          margin-right: 16px;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .waypoint-info {
          flex: 1;

          .waypoint-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 6px;
            font-size: 14px;
          }

          .waypoint-coords {
            font-size: 12px;
            color: #909399;
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }
    }

    .prize-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .prize-item {
        display: flex;
        align-items: center;
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .prize-medal {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 20px;
          color: white;

          &.first {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
          }

          &.second {
            background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
            box-shadow: 0 4px 15px rgba(192, 192, 192, 0.4);
          }

          &.third {
            background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
            box-shadow: 0 4px 15px rgba(205, 127, 50, 0.4);
          }
        }

        .prize-info {
          flex: 1;

          .prize-rank {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
            font-weight: 500;
          }

          .prize-amount {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;

      .image-item {
        text-align: center;

        .image-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .preview-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-detail {
    .activity-detail-dialog {
      width: 95% !important;
      margin: 2vh auto;
    }

    .hero-section {
      height: 150px;

      .cover-overlay {
        padding: 20px 16px 16px;

        .activity-title {
          font-size: 18px;
        }
      }
    }

    .main-content {
      padding: 16px;
    }

    .info-card {
      .card-content {
        padding: 16px;
      }

      .prize-grid {
        grid-template-columns: 1fr;
      }

      .image-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

// 滚动条样式
.detail-content {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>