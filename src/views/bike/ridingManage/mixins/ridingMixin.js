import { parseTime } from "@/utils";

/**
 * 骑行管理公共混入
 * 提供通用的数据处理和业务逻辑
 */
export default {
  data() {
    return {
      // 表格高度计算
      tableHeight: 'calc(100vh - 280px)',
      
      // 通用loading状态
      loading: false,
      
      // 导出loading状态
      exportLoading: false
    };
  },
  
  methods: {
    /**
     * 时间格式化
     * @param {Date|String} time - 时间对象或字符串
     * @param {String} pattern - 格式化模式
     * @returns {String} 格式化后的时间字符串
     */
    parseTime(time, pattern = '{y}-{m}-{d} {h}:{i}:{s}') {
      return parseTime(time, pattern);
    },
    
         /**
      * 确认删除对话框
      * @param {String} message - 提示信息
      * @param {Function} callback - 确认回调
      */
     confirmDelete(message, callback) {
       this.$confirm(message, this.$t('bike.ridingManage.warning'), {
         confirmButtonText: this.$t('bike.ridingManage.confirm'),
         cancelButtonText: this.$t('bike.ridingManage.cancel'),
         type: "warning"
       }).then(callback).catch(() => {
         console.log('用户取消删除');
       });
     },
    
         /**
      * 导出确认对话框
      * @param {Function} exportFn - 导出函数
      */
     handleExportConfirm(exportFn) {
       this.$confirm(this.$t('bike.ridingManage.exportConfirm'), this.$t('bike.ridingManage.warning'), {
         confirmButtonText: this.$t('bike.ridingManage.confirm'),
         cancelButtonText: this.$t('bike.ridingManage.cancel'),
         type: "warning"
       }).then(() => {
         this.exportLoading = true;
         return exportFn();
       }).then(response => {
         this.download(response.msg);
         this.exportLoading = false;
       }).catch(() => {
         this.exportLoading = false;
       });
     },
    
    /**
     * 表单重置
     * @param {String} formRef - 表单ref名称
     */
    resetForm(formRef) {
      if (this.$refs[formRef]) {
        this.$refs[formRef].resetFields();
      }
    },
    
    /**
     * 获取选中的ID数组
     * @param {Array} selection - 选中的数据
     * @returns {Array} ID数组
     */
    getSelectedIds(selection) {
      return selection.map(item => item.id);
    },
    
    /**
     * 显示成功消息
     * @param {String} message - 消息内容
     */
    showSuccess(message) {
      this.$message({
        message: message,
        type: 'success'
      });
    },
    
    /**
     * 显示错误消息
     * @param {String} message - 消息内容
     */
    showError(message) {
      this.$message({
        message: message,
        type: 'error'
      });
    },
    
    /**
     * 显示警告消息
     * @param {String} message - 消息内容
     */
    showWarning(message) {
      this.$message({
        message: message,
        type: 'warning'
      });
    }
  },
  
  filters: {
    /**
     * 状态过滤器
     * @param {Number} status - 状态值
     * @param {Object} statusMap - 状态映射
     */
    statusFilter(status, statusMap) {
      return statusMap[status] || '未知';
    }
  }
}; 