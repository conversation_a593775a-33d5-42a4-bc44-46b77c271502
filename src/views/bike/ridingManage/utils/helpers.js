import { 
  ACTIVITY_STATUS_MAP, 
  ACTIVITY_STATUS_TAG,
  ANNOUNCEMENT_TYPE_MAP,
  ANNOUNCEMENT_TYPE_TAG,
  ANNOUNCEMENT_STATUS_MAP,
  ANNOUNCEMENT_STATUS_TAG
} from '../constants';

/**
 * 骑行管理工具函数
 */

/**
 * 获取活动状态标签
 * @param {Number} status - 状态值
 * @returns {Object} { text, type }
 */
export function getActivityStatusTag(status) {
  return {
    text: ACTIVITY_STATUS_MAP[status] || '未知',
    type: ACTIVITY_STATUS_TAG[status] || 'info'
  };
}

/**
 * 获取公告类型标签
 * @param {Number} type - 类型值
 * @returns {Object} { text, type }
 */
export function getAnnouncementTypeTag(type) {
  return {
    text: ANNOUNCEMENT_TYPE_MAP[type] || '未知',
    type: ANNOUNCEMENT_TYPE_TAG[type] || 'info'
  };
}

/**
 * 获取公告状态标签
 * @param {Number} status - 状态值
 * @returns {Object} { text, type }
 */
export function getAnnouncementStatusTag(status) {
  return {
    text: ANNOUNCEMENT_STATUS_MAP[status] || '未知',
    type: ANNOUNCEMENT_STATUS_TAG[status] || 'info'
  };
}

/**
 * 验证时间范围
 * @param {Date} startTime - 开始时间
 * @param {Date} endTime - 结束时间
 * @returns {Boolean} 是否有效
 */
export function validateTimeRange(startTime, endTime) {
  if (!startTime || !endTime) return true; // 空值由required规则处理
  return new Date(startTime) < new Date(endTime);
}

/**
 * 格式化参与人数显示
 * @param {Number} current - 当前参与人数
 * @param {Number} max - 最大参与人数
 * @returns {String} 格式化后的字符串
 */
export function formatParticipants(current, max) {
  return `${current || 0}/${max || 0}`;
}

/**
 * 检查活动是否可以编辑
 * @param {Object} activity - 活动对象
 * @returns {Boolean} 是否可编辑
 */
export function canEditActivity(activity) {
  // 已结束的活动不可编辑
  return activity.status !== 0;
}

/**
 * 检查公告是否可以编辑
 * @param {Object} announcement - 公告对象
 * @returns {Boolean} 是否可编辑
 */
export function canEditAnnouncement(announcement) {
  // 所有公告都可以编辑
  return true;
}

/**
 * 生成搜索参数
 * @param {Object} queryParams - 查询参数
 * @returns {Object} 处理后的查询参数
 */
export function buildSearchParams(queryParams) {
  const params = {};
  
  Object.keys(queryParams).forEach(key => {
    const value = queryParams[key];
    if (value !== null && value !== undefined && value !== '') {
      params[key] = value;
    }
  });
  
  return params;
}

/**
 * 处理表格分页参数
 * @param {Object} pagination - 分页对象
 * @returns {Object} 标准化的分页参数
 */
export function handlePagination(pagination) {
  return {
    p: pagination.page || 1,
    l: pagination.limit || 10
  };
}

/**
 * 深拷贝对象
 * @param {Object} obj - 源对象
 * @returns {Object} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj);
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  
  const cloned = {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
}

/**
 * 防抖函数
 * @param {Function} func - 执行函数
 * @param {Number} delay - 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} func - 执行函数
 * @param {Number} delay - 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let timer = null;
  return function(...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args);
        timer = null;
      }, delay);
    }
  };
}

/**
 * 获取文件扩展名
 * @param {String} filename - 文件名
 * @returns {String} 扩展名
 */
export function getFileExtension(filename) {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * 格式化文件大小
 * @param {Number} bytes - 字节数
 * @returns {String} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 