/**
 * 骑行管理模块常量定义
 */

// 组队活动状态
export const ACTIVITY_STATUS = {
  ENDED: 0,      // 已结束
  ONGOING: 1,    // 进行中
  PENDING: 2     // 待开始
};

// 组队活动状态映射 (动态获取国际化文本)
export const getActivityStatusMap = (t) => ({
  [ACTIVITY_STATUS.ENDED]: t('bike.ridingManage.activityStatus.ended'),
  [ACTIVITY_STATUS.ONGOING]: t('bike.ridingManage.activityStatus.ongoing'),
  [ACTIVITY_STATUS.PENDING]: t('bike.ridingManage.activityStatus.pending')
});

// 组队活动状态标签类型
export const ACTIVITY_STATUS_TAG = {
  [ACTIVITY_STATUS.ENDED]: 'danger',
  [ACTIVITY_STATUS.ONGOING]: 'success',
  [ACTIVITY_STATUS.PENDING]: 'warning'
};

// 公告类型
export const ANNOUNCEMENT_TYPE = {
  STATEMENT: 1,  // 骑行声明
  NOTICE: 2      // 公告通知
};

// 公告类型映射
export const ANNOUNCEMENT_TYPE_MAP = {
  [ANNOUNCEMENT_TYPE.STATEMENT]: '骑行声明',
  [ANNOUNCEMENT_TYPE.NOTICE]: '公告通知'
};

// 公告类型标签类型
export const ANNOUNCEMENT_TYPE_TAG = {
  [ANNOUNCEMENT_TYPE.STATEMENT]: '',
  [ANNOUNCEMENT_TYPE.NOTICE]: 'warning'
};

// 公告状态
export const ANNOUNCEMENT_STATUS = {
  DRAFT: 0,      // 草稿
  PUBLISHED: 1   // 已发布
};

// 公告状态映射
export const ANNOUNCEMENT_STATUS_MAP = {
  [ANNOUNCEMENT_STATUS.DRAFT]: '草稿',
  [ANNOUNCEMENT_STATUS.PUBLISHED]: '已发布'
};

// 公告状态标签类型
export const ANNOUNCEMENT_STATUS_TAG = {
  [ANNOUNCEMENT_STATUS.DRAFT]: 'info',
  [ANNOUNCEMENT_STATUS.PUBLISHED]: 'success'
};

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  PAGE_SIZES: [10, 20, 50, 100]
};

// 表单验证规则
export const FORM_RULES = {
  // 组队活动表单规则
  ACTIVITY: {
    activityName: [
      { required: true, message: '活动名称不能为空', trigger: 'blur' },
      { min: 2, max: 50, message: '活动名称长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    organizer: [
      { required: true, message: '组织者不能为空', trigger: 'blur' },
      { min: 2, max: 20, message: '组织者长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    startTime: [
      { required: true, message: '开始时间不能为空', trigger: 'change' }
    ],
    endTime: [
      { required: true, message: '结束时间不能为空', trigger: 'change' }
    ],
    maxParticipants: [
      { required: true, message: '最大参与人数不能为空', trigger: 'blur' },
      { type: 'number', min: 1, max: 1000, message: '参与人数必须在 1 到 1000 之间', trigger: 'blur' }
    ]
  },
  
  // 公告表单规则
  ANNOUNCEMENT: {
    title: [
      { required: true, message: '公告标题不能为空', trigger: 'blur' },
      { min: 2, max: 100, message: '公告标题长度在 2 到 100 个字符', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '公告类型不能为空', trigger: 'change' }
    ],
    content: [
      { required: true, message: '公告内容不能为空', trigger: 'blur' },
      { min: 10, max: 2000, message: '公告内容长度在 10 到 2000 个字符', trigger: 'blur' }
    ],
    publisher: [
      { required: true, message: '发布人不能为空', trigger: 'blur' },
      { min: 2, max: 20, message: '发布人长度在 2 到 20 个字符', trigger: 'blur' }
    ]
  }
};

// API 接口权限配置
export const PERMISSIONS = {
  // 组队活动权限
  ACTIVITY: {
    LIST: 'bike:activity:list',
    ADD: 'bike:activity:add',
    EDIT: 'bike:activity:edit',
    DELETE: 'bike:activity:remove',
    EXPORT: 'bike:activity:export'
  },
  
  // 公告权限
  ANNOUNCEMENT: {
    LIST: 'bike:announcement:list',
    ADD: 'bike:announcement:add',
    EDIT: 'bike:announcement:edit',
    DELETE: 'bike:announcement:remove',
    EXPORT: 'bike:announcement:export'
  }
};

// 默认表单数据
export const DEFAULT_FORM_DATA = {
  // 组队活动默认数据
  ACTIVITY: {
    id: null,
    activityName: '',
    organizer: '',
    description: '',
    startTime: null,
    endTime: null,
    maxParticipants: 10,
    participants: 0,
    status: ACTIVITY_STATUS.PENDING
  },
  
  // 公告默认数据
  ANNOUNCEMENT: {
    id: null,
    title: '',
    type: ANNOUNCEMENT_TYPE.STATEMENT,
    content: '',
    publisher: '',
    publishTime: null,
    status: ANNOUNCEMENT_STATUS.DRAFT
  }
}; 