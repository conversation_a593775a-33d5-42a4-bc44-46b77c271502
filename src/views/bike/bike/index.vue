<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('bike.bike.modelName')" prop="modelKey">
        <el-autocomplete
          v-model.trim="queryParams.modelKey"
          clearable
          :fetch-suggestions="querySearchAsync"
          @select="handleQuery"
          style="width: 140px"
          :placeholder="$t('form.input') + $t('bike.bike.modelName')"
        />
      </el-form-item>
      <el-form-item :label="$t('bike.bike.nickName')" prop="nickName">
        <el-input
          v-model.trim="queryParams.nickName"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 160px"
          :placeholder="$t('form.input') + $t('bike.bike.nickName')"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.riding.have.email')" prop="email">
        <el-input
          v-model="queryParams.email"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
          :placeholder="$t('form.input') + $t('acc.riding.have.email')"
        />
      </el-form-item>
      <el-form-item :label="$t('bike.bike.bluetooth')" prop="bluetooth">
        <el-input
          v-model.trim="queryParams.bluetooth"
          clearable
          @input="filterBluetoothValue"
          @keyup.enter.native="handleQuery"
          style="width: 140px"
          :placeholder="$t('form.input') + $t('bike.bike.bluetooth')"
        />
      </el-form-item>
      <el-form-item :label="$t('bike.bike.bindState')" prop="bindStatus">
        <el-select
          v-model="queryParams.bindStatus"
          clearable
          @change="handleQuery"
          style="width: 140px"
          :placeholder="$t('form.input') + $t('bike.bike.bindState')"
        >
          <el-option
            v-for="dict in bindStatusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("bike.bike.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("bike.bike.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :height="tableHeight()" :data="bikeList">
      <el-table-column
        type="index"
        width="65"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.bikeName')"
        prop="carName"
        align="center"
      />
      <el-table-column
        align="center"
        :label="$t('bike.bike.modelName')"
        prop="modelName"
      >
        <template slot-scope="{ row }">
          {{ row.modelName }}
          <!-- <el-link
            @click="
              toJumpPagePath({
                path: '/bike/OwnEquipment',
                query: { modelName: row.modelName }
              })
            "
          >
            {{ row.modelName }}
          </el-link> -->
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="nickName"
        :label="$t('bike.bike.nickName')"
      >
        <span slot-scope="scope" v-NoData="scope.row.nickName"></span>
      </el-table-column>
      <el-table-column
        align="center"
        prop="nickName"
        :label="$t('system.user.email')"
      >
        <span slot-scope="scope" v-NoData="scope.row.email"></span>
      </el-table-column>
      <el-table-column
        align="center"
        prop="bluetooth"
        :label="$t('bike.bike.bluetooth')"
      />
      <el-table-column
        prop="computerName"
        align="center"
        :label="$t('bike.bike.computerName')"
      >
        <span slot-scope="scope" v-NoData="scope.row.computerName"></span>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.bindState')" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.bindTime">{{ $t("bike.bike.bind") }}</el-tag>
          <el-tag type="danger" v-else>{{ $t("bike.bike.notBind") }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        sortable
        prop="registerTime"
        :label="$t('bike.bike.bindTime')"
      >
        <span
          slot-scope="scope"
          v-NoData="parseTime(scope.row.bindTime)"
        ></span>
      </el-table-column>
      <el-table-column
        class-name="small-padding fixed-width"
        header-align="center"
        :label="$t('bike.bike.operation')"
      >
        <div class="flex justify-center" slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)">
            {{ $t("bike.bike.particulars") }}
          </el-button>
          <el-button
            v-if="scope.row.bindTime"
            type="text"
            class="text-red"
            @click="handleUntie(scope.row)"
          >
            {{ $t("bike.bike.untie") }}
          </el-button>
          <!-- <el-button type="text" @click="toSearchGps(scope.row.id)">
            查询位置
          </el-button> -->
        </div>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :title="$t('bike.bike.particulars')"
      :visible.sync="dialogTableVisible"
      :close-on-click-modal="false"
      center
      width="600px"
    >
      <el-descriptions direction="vertical" :column="4" border>
        <el-descriptions-item :label="$t('acc.user.bikeName')">
          <span v-NoData="detailData.carName"></span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('bike.bike.modelName')">
          <span v-NoData="detailData.modelName"></span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('bike.bike.nickName')">
          <span v-NoData="detailData.nickName"></span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('bike.bike.computerName')">
          <span v-NoData="detailData.computerName"></span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('bike.bike.bluetooth')">
          <span v-NoData="parseTime(detailData.bluetooth)"></span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('bike.bike.registerTime')">
          <span v-NoData="parseTime(detailData.registerTime)"></span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('bike.bike.bindTime')">
          <span v-NoData="parseTime(detailData.bindTime)"></span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listBike, detailBike, computerUnbind } from "@/api/bike/bike";
import { modelFuzzy } from "@/api/bike/model";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      bikeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      bindStatusOptions: [
        { dictValue: "0", dictLabel: this.$t("bike.bike.bind") },
        { dictValue: "1", dictLabel: this.$t("bike.bike.notBind") }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 50,
        bindStatus: undefined,
        modelKey: undefined,
        nickName: undefined,
        email: undefined,
        bluetooth: undefined
      },
      // 表单校验
      detailData: {},
      dialogTableVisible: false
    };
  },
  created() {
    if (this.$route.query.carName) {
      this.queryParams.carName = this.$route.query.carName;
    }
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      listBike(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.bikeList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.queryParams.carName = "";
      this.getList();
    },
    querySearchAsync(queryString, cb) {
      modelFuzzy({ key: queryString || "" }).then(res => {
        let results = res.data.map(item => {
          return { value: item.key };
        });
        cb(results);
      });
    },
    handleDetail(row) {
      this.dialogTableVisible = true;
      detailBike(row.id).then(response => {
        this.detailData = Object.assign(row, response.data);
      });
    },
    filterBluetoothValue(val) {
      if (!this.$IS_Empty(val)) {
        val = val.replace(/:/gi, "");
        let reg = new RegExp("\\w{1," + 2 + "}", "g");
        let ma = val.match(reg);
        ma = ma.join(":");
        this.queryParams.bluetooth = ma.toUpperCase();
      }
    },
    handleUntie(row) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      }).then(() => {
        computerUnbind(row.id).then(() => {
          this.msgSuccess(this.$t("acc.user.succeed"));
          this.getList();
        });
      });
    }
  }
};
</script>
