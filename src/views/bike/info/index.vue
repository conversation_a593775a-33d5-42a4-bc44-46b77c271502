<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="$t('route.Info')"
    :visible.sync="dialogVisible"
    width="50%"
    append-to-body
  >
    <el-row class="mb8 flex align-center justify-between">
      <el-col>
        <span> {{ $t("bike.model.carName") }}: {{ brandInfo.name }} </span>
        <span class="margin-left">
          {{ $t("bike.model.code") }}: {{ brandInfo.code }}
        </span>
      </el-col>
      <el-col class="flex justify-end">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          hasPerminone="['bike:computer:add']"
        >
          {{ $t("bike.computer.newAdd") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="computerList"
      height="500px"
      :default-sort="{ prop: 'sort' }"
    >
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.info.cnKey ')"
        prop="cnKey"
        align="center"
      />

      <el-table-column
        :label="$t('bike.info.cnValue')"
        align="center"
        prop="cnValue"
      />
      <el-table-column
        :label="$t('bike.info.enKey')"
        align="center"
        prop="enKey"
      />
      <el-table-column
        :label="$t('bike.info.enValue')"
        align="center"
        prop="enValue"
      />
      <el-table-column
        :label="$t('system.menu.orderNum')"
        sortable
        align="center"
        prop="sort"
      />
      <el-table-column
        :label="$t('discover.posted.handle')"
        align="center"
        class-name="small-padding fixed-width"
        width="150px"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >
            {{ $t("bike.computer.update") }}
          </el-button>
          <el-button
            class="text-red"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(scope.row)"
          >
            {{ $t("bike.info.del") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-form-item :label="$t('bike.info.cnKey')" prop="cnKey">
          <el-select
            v-model="form.cnKey"
            clearable
            :placeholder="$t('form.select') + $t('bike.info.cnKey')"
            style="width: 100%"
          >
            <el-option
              v-for="item in cnOptions"
              :key="item.dictCode"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('bike.info.cnValue')" prop="cnValue">
          <el-input
            v-model="form.cnValue"
            clearable
            :placeholder="$t('form.input') + $t('bike.info.cnValue')"
          />
        </el-form-item>
        <el-form-item :label="$t('bike.info.enKey')" prop="enKey">
          <el-select
            v-model="form.enKey"
            clearable
            :placeholder="$t('form.select') + $t('bike.info.enKey')"
            style="width: 100%"
          >
            <el-option
              v-for="item in enOptions"
              :key="item.dictCode"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('bike.info.enValue')" prop="enValue">
          <el-input
            v-model="form.enValue"
            clearable
            :placeholder="$t('form.input') + $t('bike.info.enValue')"
          />
        </el-form-item>

        <el-form-item :label="$t('system.menu.orderNum')" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            precision="0"
            style="width: 50%;"
            :placeholder="$t('form.input') + $t('system.menu.orderNum')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.computer._confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("bike.computer._cancel") }}</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { infoList, addInfo, updateInfo, infoDel } from "@/api/model/info";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      brandInfo: {},
      // 遮罩层
      loading: true,
      // 用户表格数据
      computerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      dialogVisible: false,
      open: false,
      cnOptions: [],
      enOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        modelId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        cnKey: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.info.cnKey"),
            trigger: "change"
          }
        ],
        cnValue: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("bike.info.cnValue"),
            trigger: "blur"
          }
        ],
        enKey: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.info.enKey"),
            trigger: "change"
          }
        ],
        enValue: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("bike.info.enValue"),
            trigger: "blur"
          }
        ],
        modelId: [
          {
            required: true,
            message: this.$t("bike.info.select"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getDicts("bike_model_info_cn").then(res => {
      this.cnOptions = res.data;
    });
    this.getDicts("bike_model_info_en").then(res => {
      this.enOptions = res.data;
    });
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      infoList({ ...this.queryParams, modelId: this.brandInfo.brandId })
        .then(response => {
          this.computerList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.form.modelId = this.brandInfo.brandId;
      this.title = this.$t("bike.configure.add");
    },
    handleUpdate(row) {
      this.reset();
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = this.$t("bike.computer.update");
    },

    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        infoDel(row.id)
          .then(() => {
            this.msgSuccess(
              this.$t("queryParams.delete") + this.$t("bike.computer.succeed")
            );
            this.getList();
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;

          if (this.form.id !== undefined) {
            updateInfo(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addInfo(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
