<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        inline
        @submit.native.prevent
        v-show="showSearch"
      >
        <el-form-item :label="$t('bike.model.carNameOrCode')" prop="modelKey">
          <el-input
            v-model.trim="queryParams.modelKey"
            :placeholder="$t('bike.model.carNameOrCodeInput')"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("bike.model.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.model.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:model:add']"
        >
          {{ $t("bike.model.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['bike:model:auth']"
        >
          {{ $t("bike.model.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['bike:model:auth']"
        >
          {{ $t("bike.model.forbidden") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      ref="multipleTableRef"
      row-key="id"
      v-loading="loading"
      :data="brandList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.carName')"
        prop="name"
        align="center"
      />
      <el-table-column
        :label="$t('bike.model.code')"
        prop="code"
        align="center"
      />
      <el-table-column
        :label="$t('bike.model.protocolVersion')"
        prop="versionName"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.versionName"></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.carTopImage')"
        prop="topImg"
        align="center"
      >
        <template v-slot="{ row }">
          <preview-img :imgUrl="row.topImg" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('bike.model.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.operation')" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['bike:computer:edit']"
          >
            {{ $t("bike.model.update") }}
          </el-button>
          <el-button type="text" @click="handleDetail(scope.row)">
            {{ $t("bike.model.particulars") }}
          </el-button>
          <el-button type="text" @click="handleInfo(scope.row)">
            {{ $t("route.Info") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="top"
      >
        <el-row :gutter="20" type="flex" class="flex-wrap">
          <!-- 车型名称 -->
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.carName')" prop="name">
              <el-input
                v-model.trim="form.name"
                clearable
                :placeholder="$t('bike.model.carNameInput')"
              />
            </el-form-item>
          </el-col>

          <!-- 车型编码 -->
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.code')" prop="code">
              <el-input
                v-model.trim="form.code"
                clearable
                :placeholder="$t('bike.model.codeInput')"
              >
                <el-button
                  type="primary"
                  slot="append"
                  icon="el-icon-search"
                  v-debounce-click="getModelRandom"
                />
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 最大速度 -->
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.maxSpeed')" prop="maxSpeed">
              <el-input-number
                v-model="form.maxSpeed"
                :min="0"
                :precision="0"
                style="width: 100%;"
                :placeholder="$t('form.input') + $t('bike.model.maxSpeed')"
              />
            </el-form-item>
          </el-col>

          <!-- 最小速度 -->
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.minSpeed')" prop="minSpeed">
              <el-input-number
                v-model="form.minSpeed"
                :min="0"
                :max="+form.maxSpeed || 0"
                :precision="0"
                style="width: 100%;"
                :placeholder="$t('form.input') + $t('bike.model.minSpeed')"
              />
            </el-form-item>
          </el-col>

          <!-- 单位 -->
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.unitType')" prop="unitType">
              <el-select
                v-model="form.unitType"
                clearable
                :placeholder="$t('form.select') + $t('bike.model.unitType')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in unitTypeOptions"
                  :key="item.key"
                  :label="item.key"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 车型简介 -->
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.carDesc')" prop="desc">
              <el-input
                v-model="form.desc"
                type="textarea"
                :autosize="{ minRows: 1 }"
                :placeholder="$t('bike.model.carDescInput')"
              />
            </el-form-item>
          </el-col>

          <!-- 设备功能配置 -->
          <el-col :span="8">
            <el-form-item :label="$t('route.Configure')" prop="modelConfList">
              <el-select
                v-model="form.modelConfList"
                multiple
                collapse-tags
                :placeholder="$t('route.Configure')"
                @change="changeModelConf"
                style="width: 100%"
              >
                <el-option
                  v-for="item in confOptions"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 档位配置 -->
          <el-col :span="8" v-if="showGroup">
            <el-form-item
              :label="$t('bike.model.gearGroupId')"
              prop="gearGroupId"
            >
              <el-select
                v-model="form.gearGroupId"
                style="width: 100%"
                clearable
                :placeholder="
                  $t('bike.info.select') + $t('bike.model.gearGroupId')
                "
              >
                <el-option
                  v-for="item in groupOptions"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 背光亮度配置 -->
          <el-col :span="8" v-if="showBackLight">
            <el-form-item
              :label="$t('bike.bike.backLightSet')"
              prop="lightGroupId"
            >
              <el-select
                v-model="form.lightGroupId"
                style="width: 100%"
                clearable
                :placeholder="
                  $t('bike.info.select') + $t('bike.bike.backLightSet')
                "
              >
                <el-option
                  v-for="item in lightOptions"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item :label="$t('bike.model.OtherSet')">
              <el-checkbox v-model="form.isNavi" border>
                {{ $t("bike.model.isNavi") }}
              </el-checkbox>
              <el-checkbox
                v-model="form.isIot"
                :true-label="1"
                :false-label="0"
                border
              >
                {{ $t("bike.bike.isIot") }}
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('bike.model.protocolVersion')">
              <el-select
                v-model="form.version"
                style="width: 100%"
                clearable
                :placeholder="
                  $t('bike.info.select') + $t('bike.model.protocolVersion')
                "
              >
                <el-option
                  v-for="item in versionList"
                  :key="item.dictCode"
                  :label="item.dictLabel"
                  :value="String(item.dictCode)"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('bike.model.carTopImage')"
              prop="topImg"
              style="width: 100%"
            >
              <el-upload-sortable
                v-model="form.topImg"
                :imgW="98"
                :imgH="98"
                :isLimit="1"
                :max="1"
              />
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item
              :label="$t('bike.model.elseImg')"
              prop="imgs"
              style="width: 100%"
            >
              <el-upload-sortable v-model="form.imgs" :imgW="98" :imgH="98" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.model._confirm") }}
        </el-button>
        <el-button @click="cancel">
          {{ $t("bike.model._cancel") }}
        </el-button>
      </div>
    </el-dialog>

    <Detail ref="detailRef" />
    <ModelInfo ref="modelInfo" />
  </div>
</template>

<script>
import {
  listModel,
  authModel,
  addModel,
  editModel,
  modelRandom
} from "@/api/bike/model";
import { groupDict } from "@/api/model/gear";
import { lightGroupDict } from "@/api/model/bikeLight";
import { confDict } from "@/api/bike/conf";
import Detail from "./detail";
import ModelInfo from "../info";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    Detail,
    ModelInfo
  },
  data() {
    return {
      aFn: authModel,
      // 用户表格数据
      brandList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      showGroup: false,
      groupOptions: [],
      lightOptions: [],
      wheelOptions: [],
      confOptions: [],
      // 错误代码协议版本
      versionList: [],
      unitTypeOptions: [{ key: "km", value: 1 }, { key: "miles", value: 2 }],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        modelKey: undefined
      },
      // 表单参数
      form: {
        minSpeed: "",
        maxSpeed: "",
        battery: "",
        brandId: "",
        code: "",
        isIot: 0,
        desc: "",
        imgs: "",
        motor: "",
        name: "",
        topImg: "",
        wheelSizeId: "",
        agreementVer: "",
        modelConfList: []
      },
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("bike.model.nameNotNull"),
            trigger: "blur"
          }
        ],
        battery: [
          {
            required: true,
            message: this.$t("bike.model.batteryNotNull"),
            trigger: "blur"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("bike.model.codeNotNull"),
            trigger: "change"
          }
        ],
        desc: [
          {
            required: false,
            message: this.$t("bike.model.descNotNull"),
            trigger: "blur"
          }
        ],
        motor: [
          {
            required: true,
            message: this.$t("bike.model.motorNotNull"),
            trigger: "blur"
          }
        ],
        wheelSizeId: [
          {
            required: true,
            message: this.$t("bike.model.wheelSizeIdNotNull"),
            trigger: "blur"
          }
        ],
        brandId: [
          {
            required: true,
            message: this.$t("bike.model.brandIdNotNull"),
            trigger: "change"
          }
        ],
        isIot: [
          {
            required: true,
            trigger: "change"
          }
        ],
        gearGroupId: [
          {
            required: true,
            message:
              this.$t("bike.info.select") + this.$t("bike.model.gearGroupId"),
            trigger: "change"
          }
        ],
        lightGroupId: [
          {
            required: true,
            message:
              this.$t("bike.info.select") + this.$t("bike.bike.backLightSet"),
            trigger: "change"
          }
        ],
        topImg: [
          {
            required: true,
            message: this.$t("bike.model.carTopImage"),
            trigger: "change"
          }
        ]
      }
    };
  },
  computed: {
    showBackLight() {
      return this.form.modelConfList.includes("7");
    }
  },
  watch: {
    open(isOpen) {
      if (isOpen) {
        this.getDicts("agreement_version").then(response => {
          this.versionList = response.data;
        });
      }
    },
    "form.topImg"(topImg) {
      if (topImg) {
        this.clearValidateItem("form", "topImg");
      }
    }
  },
  created() {
    this.getDicts("base_bike_wheelsize").then(response => {
      this.wheelOptions = response.data;
    });

    // 设备功能配置
    confDict({ type: 1 }).then(res => {
      this.confOptions = res.data;
    });

    groupDict().then(res => {
      this.groupOptions = res.data;
    });

    lightGroupDict().then(res => {
      this.lightOptions = res.data;
    });
    this.getList();
  },
  methods: {
    getModelRandom() {
      modelRandom().then(res => {
        this.form.code = res.data;
      });
    },
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      listModel(this.queryParams).then(response => {
        this.brandList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    handleInfo(row) {
      const { brandId, name, code } = row;
      this.$refs.modelInfo.dialogVisible = true;
      this.$refs.modelInfo.brandInfo = { brandId, name, code };
      this.$refs.modelInfo.getList();
    },
    reset() {
      this.form = {
        modelConfList: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.form = {
        minSpeed: "",
        maxSpeed: "",
        battery: "",
        brandId: "",
        code: "",
        isIot: 0,
        desc: "",
        imgs: "",
        motor: "",
        name: "",
        topImg: "",
        wheelSizeId: "",
        agreementVer: "",
        modelConfList: []
      };
      this.open = true;
      this.title = this.$t("bike.model.addCarModel");
      this.resetForm("form");
      this.changeModelConf([]);
    },
    handleDetail(item) {
      this.$refs.detailRef.getList(item.id, item);
    },
    changeModelConf(val) {
      if (val) {
        this.showGroup = val.indexOf("5") > -1;
      }
    },
    handleUpdate(row) {
      this.resetForm("form");
      this.form = Object.assign({}, row, { isNavi: row.isNavi === 1 });
      this.open = true;
      this.title = this.$t("bike.model.updateCarModel");
      this.changeModelConf(this.form.modelConfList);
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          let params = Object.assign({}, this.form);
          params.isNavi = this.form.isNavi ? 1 : 0;

          if (params.id !== undefined) {
            delete params.createTime;
            delete params.updateTime;
            delete params.updateBy;
            delete params.updateTime;
            editModel(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.model.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            params.agreementVer = "2";
            addModel(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.model.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
