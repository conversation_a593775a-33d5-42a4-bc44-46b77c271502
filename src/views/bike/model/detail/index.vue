<template>
  <div class="app-container">
    <el-dialog
      :visible.sync="dialogTableVisible"
      :title="$t('bike.model.particulars')"
      :close-on-click-modal="false"
      width="880px"
      center
    >
      <el-card shadow="never">
        <el-form ref="queryForm" label-position="left">
          <el-row>
            <el-col :span="24">
              <el-form-item
                :label="$t('bike.model.carTopImage') + ':'"
                prop="modelKey"
              >
                <preview-img :imgUrl="detailData.topImg" isHover />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="detailData.imgs">
              <el-form-item
                :label="$t('bike.model.elseImg') + ':'"
                prop="modelKey"
              >
                <preview-img :imgUrl="detailData.imgs" isHover />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :label="$t('bike.model.carNameOrCode') + ':'"
                prop="modelKey"
              >
                {{ detailData.name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :label="$t('bike.model.code') + ':'"
                prop="modelKey"
              >
                {{ detailData.code }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.model.createBy') + ':'">
                {{ itemData.createBy }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('acc.msg.msg.activatedState') + ':'">
                {{
                  detailData.status == 0
                    ? $t("base.exception.startUsing")
                    : $t("base.exception.forbidden")
                }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.model.protocolVersion') + ':'">
                <span v-NoData="itemData.versionName"></span>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="detailData.desc !== ''">
              <el-form-item
                :label="$t('bike.model.carDesc') + ':'"
                prop="modelKey"
              >
                <el-card shadow="never" style="min-height: 230px;">
                  {{ detailData.desc }}
                </el-card>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import { detailModel } from "@/api/bike/model";
export default {
  name: "modelDetail",
  data() {
    return {
      itemData: {},
      dialogTableVisible: false,
      detailData: {},
      // 遮罩层
      loading: true
    };
  },

  methods: {
    /** 查询品牌列表 */
    getList(id, item) {
      this.loading = true;
      this.itemData = item;

      detailModel(id).then(response => {
        this.detailData = response.data;
        this.dialogTableVisible = true;
        this.loading = false;
      });
    }
  }
};
</script>
