<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <!-- 品类 -->
        <el-form-item
          :label="$t('bike.computer.queryTable.productClass')"
          prop="productClass"
        >
          <el-select
            v-model="queryParams.productClass"
            remote
            filterable
            clearable
            :placeholder="$t('bike.computer.placeholder.productClass')"
            :remote-method="productClassRemoteMethod"
            @change="handleProductClassSelect"
          >
            <el-option
              v-for="item in productClassList"
              :key="item.key"
              :label="item.value"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 型号 -->
        <el-form-item
          :label="$t('bike.computer.queryTable.productModel')"
          prop="productModel"
        >
          <el-select
            v-model="queryParams.productModel"
            filterable
            clearable
            @change="handleProductModelSelect"
            :placeholder="
              $t('form.select') + $t('bike.computer.queryTable.productModel')
            "
          >
            <el-option
              v-for="item in productModelList"
              :key="item.key"
              :label="item.value"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 编码 -->
        <el-form-item :label="$t('bike.computer.queryTable.code')" prop="code">
          <el-select
            v-model="queryParams.code"
            filterable
            clearable
            :placeholder="
              $t('form.select') + $t('bike.computer.queryTable.code')
            "
          >
            <el-option
              v-for="item in productCodeList"
              :key="item.key"
              :label="item.value"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("bike.computer.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.computer.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:computer:add']"
        >
          {{ $t("bike.computer.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['bike:computer:auth']"
        >
          {{ $t("bike.computer.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['bike:computer:auth']"
        >
          {{ $t("bike.computer.forbidden") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      ref="multipleTableRef"
      row-key="id"
      v-loading="loading"
      :data="computerList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.queryTable.productClass')"
        prop="productClass"
        align="center"
      />
      <el-table-column
        :label="$t('bike.computer.queryTable.productModel')"
        prop="productModel"
        align="center"
      />
      <el-table-column
        :label="$t('bike.computer.queryTable.code')"
        prop="code"
        align="center"
      />
      <el-table-column
        :label="$t('bike.computer.queryTable.desc')"
        prop="desc"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.desc"></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.activatedState')"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('bike.computer.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['bike:computer:edit']"
          >
            {{ $t("bike.computer.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      v-bind="dialogOption"
      :view.sync="dialogOption.view"
      :visible.sync="dialogOption.show"
      @close="closeDynamicDialog"
      append-to-body
      :close-on-click-modal="false"
      class="el-dialog-dynamic"
      center
    >
      <component
        :is="dialogOption.view"
        :rowData="currentRow"
        @refresh="getList"
        @close="closeDynamicDialog"
      >
      </component>
    </el-dialog>
  </div>
</template>

<script>
import AddDialog from "./components/add-dialog";
import {
  listComputer,
  authComputer,
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  name: "BikeComputer",
  components: {
    AddDialog
  },
  data() {
    return {
      aFn: authComputer,
      // 用户表格数据
      computerList: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        productClass: undefined,
        productModel: undefined,
        code: undefined
      },
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productClass: [
          {
            required: true,
            message: this.$t("bike.computer.rules.productClass"),
            trigger: "blur"
          }
        ],
        productModel: [
          {
            required: true,
            message: this.$t("bike.computer.rules.productModel"),
            trigger: "change"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("bike.computer.rules.code"),
            trigger: "change"
          }
        ]
      },
      // 当前修改的数据
      currentRow: {},
      dialogOption: {
        width: "",
        title: "",
        show: false,
        view: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      this.productModelList = [];
      this.productCodeList = [];
      this.queryParams.productModel = "";
      this.queryParams.code = "";

      if (val) {
        // TODO: 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      this.productCodeList = [];
      this.queryParams.code = "";

      if (val) {
        // TODO: 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.queryParams.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      }
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listComputer(this.queryParams)
        .then(response => {
          this.computerList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.productModelList = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /**
     * 新增
     */
    handleAdd() {
      this.currentRow = {};
      this.showDynamicDialog(
        "AddDialog",
        this.$t("bike.computer.addInstrument"),
        "500px"
      );
    },
    /**
     * 修改
     */
    handleUpdate(row) {
      this.currentRow = row;
      this.showDynamicDialog(
        "AddDialog",
        this.$t("bike.computer.updateInstrument"),
        "500px"
      );
    },
    showDynamicDialog(view, title, width = "1200px") {
      this.dialogOption.show = true;
      this.dialogOption.view = view;
      this.dialogOption.title = title;
      this.dialogOption.width = width;
    },
    closeDynamicDialog() {
      this.dialogOption.show = false;
      this.dialogOption.view = null;
    }
  }
};
</script>
