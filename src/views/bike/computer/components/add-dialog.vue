<template>
  <div class="bike_computer-add-dialog">
    <div class="el-dialog-body">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-row>
          <!-- 品类 -->
          <el-col>
            <el-form-item
              :label="$t('bike.computer.queryTable.productClass')"
              prop="productClass"
            >
              <el-input
                v-model="formData.productClass"
                clearable
                :placeholder="$t('bike.computer.placeholder.productClass')"
              />
            </el-form-item>
          </el-col>
          <!-- 型号 -->
          <el-col>
            <el-form-item
              :label="$t('bike.computer.queryTable.productModel')"
              prop="productModel"
            >
              <el-input
                v-model="formData.productModel"
                clearable
                :placeholder="$t('bike.computer.placeholder.productModel')"
              />
            </el-form-item>
          </el-col>
          <!-- 编码 -->
          <el-col>
            <el-form-item
              :label="$t('bike.computer.queryTable.code')"
              prop="code"
            >
              <el-input
                v-model="formData.code"
                clearable
                :placeholder="$t('bike.computer.placeholder.code')"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  v-debounce-click="getModelRandom"
                />
              </el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('system.computer.desc')" prop="desc">
              <el-input
                v-model="formData.desc"
                type="textarea"
                :placeholder="$t('acc.msg.push.pleaseInputDescribe')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="text-center margin-bottom-sm">
      <el-button type="primary" :loading="isBtnLoading" @click="save">
        {{ $t("acc.msg.msg._confirm") }}
      </el-button>
      <el-button @click="$emit('close')">
        {{ $t("acc.msg.msg._cancel") }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { addComputer, editComputer, modelRandom } from "@/api/bike/computer";

export default {
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isBtnLoading: false,
      formData: {
        code: ""
      },
      // 表单校验
      rules: {
        productClass: [
          {
            required: true,
            message: this.$t("bike.computer.rules.productClass"),
            trigger: "change"
          }
        ],
        productModel: [
          {
            required: true,
            message: this.$t("bike.computer.rules.productModel"),
            trigger: "change"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("bike.computer.rules.code"),
            trigger: "change"
          }
        ]
      }
    };
  },
  created() {
    // 编辑模式
    if (this.rowData.id) {
      const { productClass, productModel, code, desc } = this.rowData;
      this.formData = {
        productClass,
        productModel,
        code,
        desc
      };
    }
  },
  methods: {
    getModelRandom() {
      modelRandom().then(res => {
        this.formData.code = res.data;
      });
    },
    save() {
      this.$refs["form"].validate(async valid => {
        if (valid) {
          this.isBtnLoading = true;
          let params = this.formData;
          let res;
          // 修改
          if (this.rowData.id) {
            params.id = this.rowData.id;
            res = await editComputer(params);
            if (res.code === 200) {
              this.msgSuccess(this.$t("bike.computer.updateSucceed"));
              this.isBtnLoading = true;
              this.$emit("refresh");
              this.$emit("close");
            } else {
              this.isBtnLoading = false;
            }
          } else {
            // 保存
            res = await addComputer(params);
            if (res.code === 200) {
              this.msgSuccess(this.$t("bike.computer.addSucceed"));
              this.isBtnLoading = true;
              this.$emit("refresh");
              this.$emit("close");
            } else {
              this.isBtnLoading = false;
            }
          }
        }
      });
    }
  }
};
</script>

