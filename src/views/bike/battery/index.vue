<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('bike.battery.customerId')" prop="customerId">
        <el-select
          v-model="queryParams.customerId"
          clearable
          :placeholder="$t('form.select') + $t('bike.battery.customerId')"
        >
          <el-option
            v-for="dict in customerOptions"
            :key="dict.key"
            :label="dict.value"
            :value="dict.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('bike.model.carName')" prop="bikeType">
        <el-input
          v-model.trim="queryParams.bikeType"
          :placeholder="$t('form.input') + $t('bike.model.carName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("bike.bike.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("bike.bike.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="bikeList"
      :height="tableHeight()"
    >
      <el-table-column
        type="index"
        width="65"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.battery.customerId')"
        prop="customerId"
        align="center"
        :formatter="fnCustomerName"
      />
      <el-table-column
        :label="$t('bike.model.carName')"
        prop="bikeType"
        align="center"
      />
      <el-table-column
        :label="$t('bike.battery.nickName')"
        prop="nickName"
        align="center"
      />
      <el-table-column
        :label="$t('bike.battery.sn')"
        prop="sn"
        align="center"
      />
      <el-table-column
        align="center"
        :label="$t('bike.battery.capacity')"
        prop="capacity"
      />
      <el-table-column
        align="center"
        :label="$t('bike.battery.residueCapacity')"
        prop="residueCapacity"
      />
      <el-table-column
        align="center"
        :label="$t('bike.battery.useNum')"
        prop="useNum"
      />

      <el-table-column
        align="center"
        :label="$t('bike.battery.useRate')"
        prop="useRate"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { batteryList } from "@/api/bike/conf";
import { listDictCustomer } from "@/api/base/dict";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      bikeList: [],
      customerOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        customerId: undefined,
        bikeType: undefined
      }
    };
  },
  created() {
    listDictCustomer().then(res => {
      this.customerOptions = res.data;
      this.getList();
    });
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      batteryList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.bikeList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    fnCustomerName(row, c, val) {
      let index = this.customerOptions.findIndex(item => item.key == val);
      if (index > -1) {
        return this.customerOptions[index].value;
      } else {
        return "未知";
      }
    }
  }
};
</script>
