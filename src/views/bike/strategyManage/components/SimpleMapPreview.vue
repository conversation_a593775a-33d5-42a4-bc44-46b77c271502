<template>
  <div class="simple-map-container">
    <div :id="mapContainerId" class="map-wrapper"></div>
  </div>
</template>

<script>
export default {
  name: 'SimpleMapPreview',
  props: {
    waypoints: {
      type: Array,
      default: () => []
    },
    center: {
      type: Object,
      default: () => ({ lng: 116.3974, lat: 39.9093 })
    },
    accessToken: {
      type: String,
      default: 'pk.eyJ1IjoiYWRkbW90b3IiLCJhIjoiY2x6dGltaDFzMmgybzJtb2NtNmsxYTIxaCJ9.b3yBd9nkM3EDNzebk_gVDA'
    },
    translate: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      map: null,
      mapContainerId: 'simple-map-' + Math.random().toString(36).substr(2, 9),
      markers: []
    }
  },
  mounted() {
    this.loadMapboxAndInit()
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove()
    }
  },
  watch: {
    waypoints: {
      handler(newWaypoints, oldWaypoints) {
        console.log('Waypoints changed:', newWaypoints)
        if (this.map && newWaypoints !== oldWaypoints) {
          this.updateMapData()
        }
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    loadMapboxAndInit() {
      // 检查是否已经加载
      if (window.mapboxgl) {
        this.initMap()
        return
      }

      // 加载 CSS
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
      document.head.appendChild(link)

      // 加载 JS
      const script = document.createElement('script')
      script.src = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'
      script.onload = () => {
        this.initMap()
      }
      script.onerror = () => {
        console.error('Map library failed to load')
      }
      document.head.appendChild(script)
    },

    initMap() {
      console.log('Initializing simple map...')

      window.mapboxgl.accessToken = this.accessToken

      this.map = new window.mapboxgl.Map({
        container: this.mapContainerId,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [this.center.lng, this.center.lat],
        zoom: 10, // 降低初始缩放级别，显示更大范围
        pitch: 0,
        bearing: 0,
        antialias: true,
        attributionControl: false,
        logoPosition: 'bottom-right'
      })

      this.map.on('load', () => {
        console.log('Simple map loaded successfully')
        this.addMapControls()
        this.addMarkers()
        this.addRoute()
      })
    },

    addMapControls() {
      // 添加导航控件 (缩放按钮 + 指南针)
      const nav = new window.mapboxgl.NavigationControl({
        showCompass: true,
        showZoom: true,
        visualizePitch: true
      })
      this.map.addControl(nav, 'top-right')

      // 添加全屏控件
      const fullscreen = new window.mapboxgl.FullscreenControl()
      this.map.addControl(fullscreen, 'top-right')

      // 添加比例尺控件
      const scale = new window.mapboxgl.ScaleControl({
        maxWidth: 100,
        unit: 'metric'
      })
      this.map.addControl(scale, 'bottom-left')

      // 添加地理定位控件
      const geolocate = new window.mapboxgl.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true,
        showUserHeading: true
      })
      this.map.addControl(geolocate, 'top-right')

      console.log('Map controls added successfully')
    },

    updateMapData() {
      console.log('Updating map data with new waypoints')

      // 清理旧的标记
      this.clearMarkers()

      // 清理旧的路线
      this.clearRoute()

      // 添加新的标记
      this.addMarkers()

      // 添加新的路线
      this.addRoute()
    },

    clearMarkers() {
      // 移除所有标记
      this.markers.forEach(marker => {
        marker.remove()
      })
      this.markers = []
      console.log('Cleared all markers')
    },

    clearRoute() {
      // 移除路线图层和数据源
      if (this.map.getLayer('route')) {
        this.map.removeLayer('route')
      }
      if (this.map.getSource('route')) {
        this.map.removeSource('route')
      }
      console.log('Cleared route layer and source')
    },

    // 外部控制方法
    zoomIn() {
      if (this.map) {
        this.map.zoomIn({ duration: 300 })
      }
    },

    zoomOut() {
      if (this.map) {
        this.map.zoomOut({ duration: 300 })
      }
    },

    fitToWaypoints() {
      if (this.waypoints.length > 0) {
        this.fitToMarkers()
      }
    },

    // 强制刷新地图数据（外部调用）
    forceUpdate() {
      console.log('Force updating map with current waypoints:', this.waypoints)
      if (this.map && this.waypoints.length > 0) {
        this.updateMapData()
      }
    },

    addMarkers() {
      console.log('Adding markers to simple map, waypoints count:', this.waypoints.length)

      this.waypoints.forEach((waypoint, index) => {
        const isStart = index === 0
        const isEnd = index === this.waypoints.length - 1 && this.waypoints.length > 1

        // 创建标记元素
        const el = document.createElement('div')
        el.className = 'simple-marker'

        if (isStart) {
          el.style.cssText = `
            width: 40px;
            height: 40px;
            background: #52c41a;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            cursor: pointer;
          `
          el.textContent = this.translate ? this.translate('map.start') : '起'
        } else if (isEnd) {
          el.style.cssText = `
            width: 40px;
            height: 40px;
            background: #f5222d;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            cursor: pointer;
          `
          el.textContent = this.translate ? this.translate('map.end') : '终'
        } else {
          el.style.cssText = `
            width: 35px;
            height: 35px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            cursor: pointer;
          `
          el.textContent = index + 1
        }

        // 创建标记
        const marker = new window.mapboxgl.Marker(el)
          .setLngLat([waypoint.longitude, waypoint.latitude])
          .addTo(this.map)

        this.markers.push(marker)

        console.log(`Marker ${index + 1} added:`, {
          type: isStart ? 'Start' : isEnd ? 'End' : 'Waypoint',
          position: [waypoint.longitude, waypoint.latitude],
          element: el
        })
      })

      // 自适应视图
      if (this.waypoints.length > 0) {
        this.fitToMarkers()
      }
    },

    fitToMarkers() {
      if (this.waypoints.length === 1) {
        this.map.flyTo({
          center: [this.waypoints[0].longitude, this.waypoints[0].latitude],
          zoom: 12
        })
        return
      }

      const bounds = new window.mapboxgl.LngLatBounds()
      this.waypoints.forEach(waypoint => {
        bounds.extend([waypoint.longitude, waypoint.latitude])
      })

      // 计算bounds的扩展，确保路线完整可见
      const center = bounds.getCenter()
      const sw = bounds.getSouthWest()
      const ne = bounds.getNorthEast()

      // 增加边界范围，确保路线两端有足够空间
      const latDiff = ne.lat - sw.lat
      const lngDiff = ne.lng - sw.lng
      const expansion = Math.max(latDiff, lngDiff) * 0.3 // 扩展30%

      bounds.extend([sw.lng - expansion, sw.lat - expansion])
      bounds.extend([ne.lng + expansion, ne.lat + expansion])

      this.map.fitBounds(bounds, {
        padding: {
          top: 80,
          bottom: 80,
          left: 80,
          right: 80
        },
        maxZoom: 14,
        duration: 1000
      })
    },

    addRoute() {
      if (this.waypoints.length < 2) {
        console.log('Less than 2 waypoints, cannot draw route')
        return
      }

      console.log('Starting to draw cycling route...')

      // 构建Mapbox Directions API请求
      const coordinates = this.waypoints.map(point =>
        `${point.longitude},${point.latitude}`
      ).join(';')

      const directionsUrl = `https://api.mapbox.com/directions/v5/mapbox/cycling/${coordinates}?geometries=geojson&access_token=${this.accessToken}`

      fetch(directionsUrl)
        .then(response => response.json())
        .then(data => {
          if (data.routes && data.routes.length > 0) {
            this.drawRoute(data.routes[0].geometry)
            console.log('Cycling route drawn successfully')

            // 路线绘制完成后，延迟调整视图以确保完整路线可见
            setTimeout(() => {
              this.fitToMarkers()
            }, 300)
          } else {
            console.error('Unable to get route data:', data)
          }
        })
        .catch(error => {
          console.error('Route request failed:', error)
        })
    },

    drawRoute(geometry) {
      // 添加路线数据源
      if (this.map.getSource('route')) {
        this.map.getSource('route').setData({
          type: 'Feature',
          properties: {},
          geometry: geometry
        })
      } else {
        this.map.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: geometry
          }
        })
      }

      // 添加路线图层
      if (!this.map.getLayer('route')) {
        this.map.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#3b82f6',
            'line-width': 4,
            'line-opacity': 0.8
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-map-container {
  width: 100%;
  height: 75vh;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  position: relative;

  .map-wrapper {
    width: 100%;
    height: 100%;
  }

  // 自定义地图控件样式
  :deep(.mapboxgl-ctrl-group) {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);

    button {
      border: none;
      background: white;
      color: #666;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
        color: #333;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }

  // 全屏控件样式
  :deep(.mapboxgl-ctrl-fullscreen) {
    .mapboxgl-ctrl-icon {
      background-size: 18px 18px;
    }
  }

  // 地理定位控件样式
  :deep(.mapboxgl-ctrl-geolocate) {
    .mapboxgl-ctrl-icon {
      background-size: 18px 18px;
    }
  }

  // 比例尺样式
  :deep(.mapboxgl-ctrl-scale) {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 11px;
    color: #666;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  // 导航控件样式
  :deep(.mapboxgl-ctrl-compass) {
    .mapboxgl-ctrl-icon {
      background-size: 18px 18px;
    }
  }
}
</style>