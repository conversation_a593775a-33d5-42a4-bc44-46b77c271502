import Cookies from 'js-cookie'
import store from '@/store'

let currentI18n = null
let currentLang = 'zh'

export async function createI18nManager(i18nInstance) {
  currentI18n = i18nInstance
  currentLang = getCurrentLanguage()
  
  // 等待国际化文件加载完成
  await loadI18nMessages()
  
  return {
    getText: (key) => {
      try {
        const moduleKey = 'strategyManage'
        const fullKey = `${moduleKey}.${key}`
        
        if (currentI18n && currentI18n.te && currentI18n.te(fullKey)) {
          return currentI18n.t(fullKey)
        }
        
        // 如果没有找到翻译，尝试使用默认的中文文本
        const fallbackText = getFallbackText(key)
        if (fallbackText) {
          return fallbackText
        }
        
        // 最后返回 key 本身作为 fallback
        console.warn(`Translation not found for key: ${fullKey}`)
        return key
      } catch (error) {
        console.error('Error getting translation:', error)
        return key
      }
    },
    
    changeLanguage: async (lang) => {
      currentLang = lang
      await loadI18nMessages()
    }
  }
}

function getCurrentLanguage() {
  try {
    return Cookies.get('language') || (store && store.getters && store.getters.language) || 'zh'
  } catch (error) {
    console.warn('Failed to get current language, using default:', error)
    return 'zh'
  }
}

// 提供一些基本的 fallback 文本
function getFallbackText(key) {
  const fallbackTexts = {
    'common.search': '搜索',
    'common.reset': '重置',
    'common.add': '添加',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.confirm': '确定',
    'common.close': '关闭',
    'actions.add': '添加攻略',
    'actions.edit': '编辑',
    'actions.delete': '删除',
    'tabs.guidebook': '骑游攻略',
    'tabs.theme': '主题管理',
    'table.actions': '操作',
    'validation.titleRequired': '攻略标题不能为空',
    'validation.contentRequired': '攻略内容不能为空',
    'validation.themeIdRequired': '主题不能为空',
    'validation.mileageRequired': '里程不能为空',
    'validation.themeNameRequired': '主题名称不能为空',
    'validation.themeImgRequired': '主题图片不能为空',
    'search.timeRange': '创建时间',
    'search.to': '至',
    'search.startTime': '开始时间',
    'search.endTime': '结束时间'
  }
  
  return fallbackTexts[key] || null
}

async function loadI18nMessages() {
  try {
    let messages
    
    // 动态导入对应语言的文件
    if (currentLang === 'en') {
      messages = await import('./en.js')
    } else {
      messages = await import('./zh.js')
    }
    
    const moduleKey = 'strategyManage'
    
    if (currentI18n && currentI18n.mergeLocaleMessage) {
      currentI18n.mergeLocaleMessage(currentLang, {
        [moduleKey]: messages.default
      })
      console.log(`Successfully loaded ${currentLang} messages for ${moduleKey}`)
    }
  } catch (error) {
    console.error(`Failed to load i18n messages for ${currentLang}:`, error)
    
    // Fallback to Chinese if other language fails
    if (currentLang !== 'zh') {
      try {
        const fallbackMessages = await import('./zh.js')
        const moduleKey = 'strategyManage'
        
        if (currentI18n && currentI18n.mergeLocaleMessage) {
          currentI18n.mergeLocaleMessage(currentLang, {
            [moduleKey]: fallbackMessages.default
          })
          console.log(`Loaded fallback zh messages for ${moduleKey}`)
        }
      } catch (fallbackError) {
        console.error('Failed to load fallback messages:', fallbackError)
      }
    }
  }
} 