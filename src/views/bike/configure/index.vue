<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item :label="$t('bike.computer.name')" prop="key">
          <el-input
            v-model.trim="queryParams.key"
            :placeholder="$t('bike.computer.nameInput')"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item :label="$t('acc.log.deviceModel')" prop="type">
          <el-select
            v-model="queryParams.type"
            clearable
            :placeholder="$t('form.select') + $t('acc.log.deviceModel')"
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="(value, key) in deviceTypeList"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("bike.computer.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.computer.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:computer:add']"
        >
          {{ $t("bike.computer.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['bike:computer:auth']"
        >
          {{ $t("bike.computer.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['bike:computer:auth']"
        >
          {{ $t("bike.computer.forbidden") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      ref="multipleTableRef"
      row-key="id"
      v-loading="loading"
      :height="tableHeight()"
      :data="computerList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        :label="$t('bike.computer.name')"
        prop="name"
        align="center"
      />
      <!-- <el-table-column
        :label="$t('acc.log.deviceModel')"
        prop="type"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ deviceTypeList[row.type] }}
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('bike.configure.code')"
        prop="code"
        align="center"
      />
      <el-table-column
        :label="$t('bike.computer.activatedState')"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('bike.model.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['bike:computer:edit']"
          >
            {{ $t("bike.computer.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item :label="$t('bike.computer.name')" prop="name">
          <el-input
            v-model.trim="form.name"
            clearable
            :placeholder="$t('bike.configure.nameInput')"
          />
        </el-form-item>
        <!-- <el-form-item :label="$t('acc.log.deviceModel')" prop="type">
          <el-select
            v-model="form.type"
            clearable
            :placeholder="$t('form.select') + $t('acc.log.deviceModel')"
            style="width: 100%"
          >
            <el-option
              v-for="(value, key) in deviceTypeList"
              :key="key"
              :label="value"
              :value="+key"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item :label="$t('bike.configure.code')" prop="code">
          <el-input
            v-model.trim="form.code"
            clearable
            :placeholder="$t('bike.info.input') + $t('bike.configure.code')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.computer._confirm") }}
        </el-button>
        <el-button @click="open = false">
          {{ $t("bike.computer._cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { confList, confAdd, confUpdate, confAuth } from "@/api/bike/conf";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      aFn: confAuth,
      // 用户表格数据
      computerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: "",
        type: ""
      },
      // 表单参数
      form: {},
      deviceTypeList: {
        1: this.$t("bike.bike.modelName"),
        2: this.$t("deviceType.IOTModel"),
        3: this.$t("deviceType.helmetModel"),
        4: this.$t("deviceType.radarModel"),
        5: this.$t("deviceType.sportsCameraModel"),
        6: this.$t("deviceType.keyModel"),
        7: this.$t("deviceType.electronicLockModel")
      },
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("bike.configure.nameInput"),
            trigger: "blur"
          }
        ],
        code: [
          {
            required: true,
            message:
              this.$t("bike.info.input") + this.$t("bike.configure.code"),
            trigger: "blur"
          }
        ],
        type: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("acc.log.deviceModel"),
            trigger: "change"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      confList(this.queryParams)
        .then(response => {
          this.computerList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("bike.configure.add");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("bike.computer.update");
      this.form = Object.assign({}, row);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            confUpdate(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            this.form.status = 0;
            confAdd(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
