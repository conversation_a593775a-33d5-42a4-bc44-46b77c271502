<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:computer:add']"
        >
          {{ $t("bike.computer.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("tagsView.refresh") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="list">
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.gear.groupName')"
        prop="groupName"
        align="center"
      />
      <el-table-column
        :label="$t('system.computer.desc')"
        prop="desc"
        align="center"
      />
      <el-table-column
        :label="$t('bike.model.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('bike.model.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('discover.posted.handle')"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleInfo(scope.row)">
            {{ $t("bike.computer.gearSet") }}
          </el-button>
          <el-button type="text" @click="handleUpdate(scope.row)">
            {{ $t("bike.computer.update") }}
          </el-button>
          <el-button type="text" class="text-red" @click="handleDel(scope.row)">
            {{ $t("bike.info.del") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-position="top"
        label-width="110px"
        @submit.native.prevent
      >
        <el-form-item :label="$t('bike.gear.groupName')" prop="groupName">
          <el-input
            v-model="form.groupName"
            clearable
            :placeholder="$t('system.user.input') + $t('bike.gear.groupName')"
          />
        </el-form-item>
        <el-form-item :label="$t('system.computer.desc')" prop="desc">
          <el-input
            type="textarea"
            v-model="form.desc"
            :autosize="{ minRows: 4 }"
            :placeholder="$t('system.user.input') + $t('system.computer.desc')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.computer._confirm") }}
        </el-button>
        <el-button @click="open = false">
          {{ $t("bike.computer._cancel") }}
        </el-button>
      </div>
    </el-dialog>
    <CompInfo ref="compInfo" :infoGroupName="infoGroupName" />
  </div>
</template>

<script>
import {
  listgroupGear,
  addGroup,
  updateGroup,
  deletGroup
} from "@/api/model/gear";
import CompInfo from "./components/info";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    CompInfo
  },
  data() {
    return {
      infoGroupName: "",
      // 用户表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        groupName: [
          {
            required: true,
            message:
              this.$t("system.user.input") + this.$t("bike.gear.groupName"),
            trigger: "blur"
          }
        ],
        modelId: [
          {
            required: true,
            message:
              this.$t("system.user.input") + this.$t("bike.gear.modelId"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listgroupGear(this.queryParams)
        .then(response => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("bike.configure.add");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("bike.computer.update");
      this.form = Object.assign({}, row);
    },
    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        deletGroup(row.id).then(() => {
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
          this.loading = false;
          this.getList();
        });
      });
    },
    handleInfo(row) {
      this.infoGroupName = row.groupName;
      this.$refs.compInfo.queryParams.groupId = row.id;
      this.$refs.compInfo.dialogVisible = true;
      this.$refs.compInfo.getList();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            updateGroup(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addGroup(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.computer.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
