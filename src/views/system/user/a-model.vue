<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2023-09-21 11:10:55
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2023-09-21 14:26:05
 * @FilePath: \bikewisePro4.0\src\views\system\user\a-model.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    class="a-dialog"
    :title="title"
    :visible="visible"
    :show-close="showClose"
    :width="width"
    :center="center"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    append-to-body
    v-bind="$attrs"
    v-on="$listeners"
    @close="handleCancel"
  >
    <slot></slot>
    {{ doc }}
    <async-component />
    <slot name="footer"></slot>
  </el-dialog>
</template>

<script>

export default {
    props: {
        title: {
            type: String,
            default: "标题"
        },
        visible: {
            type: Boolean,
            default: false
        },
        center: {
            type: Boolean,
            default: false
        },
        showClose: {
            type: Boolean,
            default: true
        },
        closeOnClickModal: {
            type: Boolean,
            default: true
        },
        closeOnPressEscape: {
            type: Boolean,
            default: true
        },
        width: {
            type: String,
            default: "30%"
        },
        handleClose: {
            type: Function,
            default: () => {}
        },
        doc: {
            type: Object,
            default: () => {}
        }
    },
    methods: {
        close() {
            this.$emit("update:visible", false);
        },
        handleCancel() {
            this.close();
        }
    }
}
</script>

<style lang="scss">
    .a-dialog {
        .dialog-footer {
            display: inline-block;
            width: 100%;
            text-align: center;
        }
    }
</style>