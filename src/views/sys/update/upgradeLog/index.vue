<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('upgradeLog.upgradeType')" prop="type">
          <el-select
            v-model="queryParams.type"
            clearable
            :placeholder="$t('form.input') + $t('upgradeLog.upgradeType')"
            @change="handleQuery"
          >
            <el-option
              v-for="(item, index) in typeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("bike.computer.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.computer.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="computerList" :height="tableHeight()">
      <el-table-column
        width="65"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('upgradeLog.newVersion')"
        prop="newVersion"
        align="center"
      >
        <span slot-scope="{ row }" v-NoData="row.newVersion"></span>
      </el-table-column>
      <el-table-column
        :label="$t('upgradeLog.newVersionNumber')"
        prop="newNumber"
        align="center"
      >
        <span slot-scope="{ row }" v-NoData="row.newNumber"></span>
      </el-table-column>
      <el-table-column
        :label="$t('upgradeLog.oldVersion')"
        prop="oldVersion"
        align="center"
      >
        <span slot-scope="{ row }" v-NoData="row.oldVersion"></span>
      </el-table-column>
      <el-table-column
        :label="$t('upgradeLog.oldVersionNumber')"
        prop="oldNumber"
        align="center"
      >
        <span slot-scope="{ row }" v-NoData="row.oldNumber"></span>
      </el-table-column>
      <el-table-column :label="$t('upgradeLog.des')" prop="des" align="center">
        <span slot-scope="scope" v-NoData="scope.row.des"></span>
      </el-table-column>
      <el-table-column
        :label="$t('upgradeLog.result')"
        prop="result"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.result"></span>
      </el-table-column>
      <el-table-column
        :label="$t('upgradeLog.resultDesc')"
        prop="resultDesc"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.resultDesc"></span>
      </el-table-column>
      <el-table-column :label="$t('upgradeLog.upgradeType')" align="center">
        <template slot-scope="{ row }">
          <el-tag type="success" v-show="row.type == 1">
            {{ $t("route.Computer") }}
          </el-tag>
          <el-tag v-show="row.type == 2">{{ $t("route.Controls") }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.createBy')"
        align="center"
        prop="createBy"
      >
        <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { logList } from "@/api/upgradeLog";
export default {
  name: "UpgradeLog",
  data() {
    return {
      showSearch: true,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      computerList: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: ""
      },
      typeList: [
        {
          label: this.$t("route.Computer"),
          value: 1
        },
        {
          label: this.$t("route.Controls"),
          value: 2
        }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      logList(this.queryParams)
        .then(response => {
          this.computerList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>
