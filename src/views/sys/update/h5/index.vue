<template>
    <div class="app-container">
      <transition name="slide-fade">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item :label="$t('system.app.appSystem')" prop="key">
            <el-select
              v-model="queryParams.key"
              clearable
              :placeholder="$t('form.select') + $t('system.app.appSystem')"
              @change="handleQuery"
            >
              <el-option
                v-for="item in appSystemList"
                :key="item.value"
                :label="item.value"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              v-debounce-click="handleQuery"
            >
              {{ $t("discover.posted.search") }}
            </el-button>
            <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
              {{ $t("discover.posted.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </transition>
  
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
            hasPerminone="['sys:h5Version:add']"
          >
            {{ $t("queryParams.add") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-check"
            v-debounce-click="() => handleAuth(0, aFn, getList)"
            hasPerminone="['sys:h5Version:auth']"
          >
            {{ $t("system.computer.auth") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            v-debounce-click="() => handleAuth(1, aFn, getList)"
            hasPerminone="['sys:h5Version:auth']"
          >
            {{ $t("system.computer.disabled") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleBatchDelete"
            hasPerminone="['sys:h5Version:delete']"
          >
            {{ $t("queryParams.delete") }}
          </el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
      </el-row>
  
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        row-key="id"
        :height="tableHeight()"
        :data="versionList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" reserve-selection align="center" />
        <el-table-column
          type="index"
          align="center"
          :label="$t('base.carexception.customerName')"
        >
          <template slot-scope="scope">
            {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('system.app.versionNum')"
          prop="version"
          align="center"
        />
        <el-table-column
          :label="$t('system.app.versionSn')"
          prop="versionNumber"
          align="center"
        />
        <el-table-column
          :label="$t('system.app.desc')"
          prop="des"
          align="center"
          :show-overflow-tooltip="true"
        >
          <span slot-scope="scope" v-NoData="scope.row.des"></span>
        </el-table-column>
        <el-table-column
          :label="$t('system.computer.downLink')"
          prop="link"
          align="center"
        >
          <template v-slot="{ row }">
            <el-image
              v-if="row.link"
              style="width: 30px; height: 30px; cursor: pointer"
              :src="require('@/assets/image/xiazai.png')"
              v-debounce-click="() => urlDownload(row.link)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('system.app.forceUpdate')"
          prop="forceUpdate"
          align="center"
        >
          <template slot-scope="scope">
            {{
              scope.row.forceUpdate === 1
                ? $t("system.app.yes")
                : $t("system.app.no")
            }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('system.app.createTime')"
          align="center"
          sortable
          prop="createTime"
        >
          <template v-slot="{ row }">{{ parseTime(row.createTime) }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('system.app.handler')"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleUpdate(scope.row)"
              hasPerminone="['sys:h5Version:edit']"
            >
              {{ $t("queryParams.update") }}
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              hasPerminone="['sys:h5Version:delete']"
            >
              {{ $t("queryParams.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.p"
        :limit.sync="queryParams.l"
        @pagination="getList"
      />
  
      <!-- 添加或修改角色配置对话框 -->
      <el-dialog
        :close-on-click-modal="false"
        :title="title"
        :visible.sync="open"
        width="600px"
        append-to-body
        center
      >
        <el-form ref="form" :model="form" :rules="rules" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                :label="$t('system.app.versionNum')"
                prop="version"
              >
                <el-input
                  v-model.trim="form.version"
                  clearable
                  :placeholder="
                    $t('system.app.input') + $t('system.app.versionNum')
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$t('system.app.versionSn')"
                prop="versionNumber"
              >
                <el-input
                  v-model.number="form.versionNumber"
                  clearable
                  oninput="value=value.replace(/[^\d]/g,'')"
                  :placeholder="
                    $t('system.app.input') + $t('system.app.versionSn')
                  "
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                :label="$t('acc.msg.msg.linkAddress')"
                prop="link"
              >
                <!-- <el-input
                  v-model.trim="form.link"
                  clearable
                  :placeholder="$t('form.input') + $t('acc.msg.msg.linkAddress')"
                /> -->
                <FileUpload v-if="open" v-model="form.link" mode="drag" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
            {{ $t("dialog.confirm") }}
          </el-button>
          <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import {
    listH5Version,
    authH5Version,
    addH5Version,
    editH5Version,
    deleteH5Version
  } from "@/api/system/h5Version";
  import { commonJs } from "@/mixinFile/common";
  import FileUpload from "@/components/FileUpload/index.vue";
  export default {
    mixins: [commonJs],
    name: "SysUpdateApp",
    components: {
      FileUpload
    },
    data() {
      const validateVersionName = (rule, value, callback) => {
        console.log("🚀 ~ file: index.vue:273 ~ value:", value)
        const reg = /^(\d+\.)(\d+\.)(\d+)$|^(\d+\.)(\d+)$|^(\d+)$/;
        if (!value) {
          callback(new Error(this.$t("system.app.rules.versionNum")));
        } else if (!reg.test(value)) {
          callback();
          // callback(new Error(this.$t("mointerObj.correctVerionNumber")));
        } else {
          callback();
        }
      };
      return {
        aFn: authH5Version,
        deviceOptions: [
          {
            key: "Android",
            value: 1
          },
          {
            key: "ios",
            value: 2
          }
        ],
        // 版本表格数据
        versionList: [],
        // 选中数组
        ids: [],
        // 非多选禁用
        multiple: true,
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 是否显示弹出层（数据权限）
        openDataScope: false,
        genderOptions: [],
        appSystemList: [
          {
            value: "IOS"
          },
          {
            value: "Android"
          }
        ],
        // 查询参数
        queryParams: {
          p: 1,
          l: 20,
          key: ""
        },
        // 表单参数
        form: {
          id: undefined,
          type: 1,
          forceUpdate: 0
        },
  
        // 表单校验
        rules: {
          type: [
            {
              required: true,
              message: this.$t("system.notice.noticeType"),
              trigger: "change"
            }
          ],
          version: [
            {
              required: true,
              validator: validateVersionName,
              trigger: ["blur", "change"]
            }
          ],
          versionNumber: [
            {
              required: true,
              message: this.$t("system.app.rules.versionSn"),
              trigger: "blur"
            }
          ],
          desc: [
            {
              required: false,
              message: "",
              trigger: "blur"
            }
          ],
          link: [
            {
              required: true,
              message:
                this.$t("system.app.rules.downLink") +
                this.$t("system.app.downLinkUp"),
              trigger: "blur"
            }
          ],
          mdCode: [
            {
              required: false,
              message: this.$t("system.app.rules.mdCode"),
              trigger: "blur"
            }
          ]
        }
      };
    },
    mounted() {
      this.getList();
    },
    watch: {
      "form.type"(newVal) {
        if (newVal === 1) {
          this.rules.link[0].message =
            this.$t("system.app.rules.downLink") +
            this.$t("system.app.downLinkUp");
        } else {
          this.rules.link[0].required = false;
        }
      },
      "form.link"(link) {
        if (link) {
          this.clearValidateItem("form", "link");
        }
      }
    },
    methods: {
      /** 查询客户列表 */
      getList() {
        this.loading = true;
        listH5Version(this.queryParams)
          .then(response => {
            this.versionList = response.data.list;
            this.total = response.data.total;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.multiple = !this.ids.length;
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = [row.id];
        this.$confirm(this.$t('dialog.confirmDelete'), this.$t('dialog.operating'), {
          confirmButtonText: this.$t('dialog.confirm'),
          cancelButtonText: this.$t('dialog.cancel'),
          type: 'warning'
        }).then(() => {
          deleteH5Version({ "ids": ids }).then(response => {
            if (response.code === 200) {
              this.msgSuccess(this.$t('dialog.deleteSuccess'));
              this.getList();
            }
          });
        });
      },
      /** 批量删除操作 */
      handleBatchDelete() {
        const ids = this.ids;
        if (ids.length === 0) {
          this.$message.warning(this.$t('system.role.selectRole'));
          return;
        }
        this.$confirm(this.$t('dialog.confirmDelete'), this.$t('dialog.operating'), {
          confirmButtonText: this.$t('dialog.confirm'),
          cancelButtonText: this.$t('dialog.cancel'),
          type: 'warning'
        }).then(() => {
          deleteH5Version({ ids: ids }).then(response => {
            if (response.code === 200) {
              this.msgSuccess(this.$t('dialog.deleteSuccess'));
              this.getList();
            }
          });
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        if (this.$refs.menu != undefined) {
          this.$refs.menu.setCheckedKeys([]);
        }
        this.form = {
          forceUpdate: 0,
          link: "",
          type: 1
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.p = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = this.$t("system.computer.handleAdd");
      },
      handleUpdate(row) {
        this.reset();
        this.form = Object.assign({}, row);
        this.open = true;
        this.title = this.$t("system.app.handleUpdate");
      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.isBtnLoading = true;
            if (this.form.id !== undefined) {
              editH5Version(this.form)
                .then(response => {
                  if (response.code === 200) {
                    this.msgSuccess(this.$t("dialog.updateSuccess"));
                    this.open = false;
                    this.getList();
                  }
                })
                .finally(() => {
                  this.isBtnLoading = false;
                });
            } else {
              addH5Version(this.form)
                .then(response => {
                  if (response.code === 200) {
                    this.msgSuccess(this.$t("dialog.addSuccess"));
                    this.open = false;
                    this.getList();
                  }
                })
                .finally(() => {
                  this.isBtnLoading = false;
                });
            }
          }
        });
      }
    }
  };
  </script>
  