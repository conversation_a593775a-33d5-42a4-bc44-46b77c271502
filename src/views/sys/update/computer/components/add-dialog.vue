<template>
  <div class="bike_computer-add-dialog">
    <div class="el-dialog-body">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-position="top"
      >
        <el-row :gutter="20">
          <!-- 品类 -->
          <el-col :span="8">
            <el-form-item
              :label="$t('bike.computer.queryTable.productClass')"
              prop="productClass"
            >
              <el-select
                v-model="formData.productClass"
                remote
                filterable
                clearable
                :disabled="edit"
                :placeholder="$t('bike.computer.placeholder.productClass')"
                :remote-method="productClassRemoteMethod"
                @change="handleProductClassSelect"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in productClassList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 型号 -->
          <el-col :span="8">
            <el-form-item
              :label="$t('bike.computer.queryTable.productModel')"
              prop="productModel"
            >
              <el-select
                v-model="formData.productModel"
                filterable
                clearable
                :disabled="edit"
                @change="handleProductModelSelect"
                :placeholder="
                  $t('form.select') +
                    $t('bike.computer.queryTable.productModel')
                "
                style="width: 100%;"
              >
                <el-option
                  v-for="item in productModelList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 编码 -->
          <el-col :span="8">
            <el-form-item
              :label="$t('bike.computer.queryTable.code')"
              prop="code"
            >
              <el-select
                v-model="formData.code"
                filterable
                clearable
                :disabled="edit"
                :placeholder="
                  $t('form.select') + $t('bike.computer.queryTable.code')
                "
                style="width: 100%;"
              >
                <el-option
                  v-for="item in productCodeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              :label="$t('system.app.versionNum')"
              prop="versionName"
            >
              <el-input
                v-model.trim="formData.versionName"
                :placeholder="$t('form.input') + $t('system.app.versionNum')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              :label="$t('system.app.versionSn')"
              prop="versionCode"
            >
              <el-input-number
                v-model="formData.versionCode"
                :min="0"
                :precision="0"
                controls-position="right"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('system.computer.desc')" prop="desc">
              <el-input
                v-model="formData.desc"
                type="textarea"
                :placeholder="$t('form.input') + $t('system.computer.desc')"
              />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('system.computer.descEn')" prop="descEn">
              <el-input
                v-model="formData.descEn"
                type="textarea"
                :placeholder="$t('form.input') + $t('system.computer.descEn')"
              />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item
              :label="$t('system.computer.downLink2')"
              prop="downLink"
            >
              <DrUpload
                v-model="formData.downLink"
                :limit="1"
                :isOnePic="1"
                accept=".bin"
                :css="{ width: '100%' }"
                class="flex-direction align-start"
              >
                <el-button size="small" type="primary">
                  {{ $t("queryParams.upload") }}
                </el-button>
              </DrUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="text-center margin-top-sm margin-bottom-sm">
      <el-button type="primary" v-debounce-click="save">
        {{ $t("acc.msg.msg._confirm") }}
      </el-button>
      <el-button @click="$emit('close')">
        {{ $t("acc.msg.msg._cancel") }}
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";
import { addSoftUpdate, editSoftUpdate } from "@/api/system/update";
import { checkVersionName } from "@/utils/validate";

export default {
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        productClass: undefined,
        productModel: undefined,
        code: undefined,
        versionName: undefined,
        versionCode: undefined,
        productClass: undefined,
        desc: undefined,
        descEn: undefined,
        downLink: undefined
      },
      edit: false,
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      // 表单校验
      rules: {
        productClass: [
          {
            required: true,
            message: this.$t("bike.computer.rules.productClass"),
            trigger: "change"
          }
        ],
        productModel: [
          {
            required: true,
            message: this.$t("bike.computer.rules.productModel"),
            trigger: "change"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("bike.computer.rules.code"),
            trigger: "change"
          }
        ],
        versionName: [
          {
            required: true,
            validator: checkVersionName,
            trigger: "blur"
          }
        ],
        versionCode: [
          {
            required: true,
            message: this.$t("system.computer.rules.versionCode"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    if (this.rowData.id) {
      this.edit = true;
      const {
        productClass,
        productModel,
        computerModel,
        versionName,
        versionCode,
        desc,
        descEn,
        downLink
      } = this.rowData;

      this.formData = {
        productClass,
        productModel,
        code: computerModel,
        versionName,
        versionCode,
        desc,
        descEn,
        downLink
      };
    }
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      this.productModelList = [];
      this.productCodeList = [];
      this.formData.productModel = "";
      this.formData.code = "";

      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      this.productCodeList = [];
      this.formData.code = "";

      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.formData.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      }
    },
    save() {
      this.$refs["form"].validate(async valid => {
        if (valid) {
          let params = {
            versionCode: this.formData.versionCode,
            versionName: this.formData.versionName,
            desc: this.formData.desc,
            descEn: this.formData.descEn,
            downLink: this.formData.downLink,
            computerModel: this.formData.code
          };
          let res;
          // 编辑模式
          if (this.rowData.id) {
            params.id = this.rowData.id;
            res = await editSoftUpdate(params);
            this.msgSuccess(this.$t("dialog.updateSuccess"));
            this.$emit("refresh");
            this.$emit("close");
          } else {
            // 新增
            res = await addSoftUpdate(params);
            this.msgSuccess(this.$t("dialog.addSuccess"));
            this.$emit("refresh");
            this.$emit("close");
          }
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input-number {
  .el-input {
    .el-input__inner {
      text-align: left;
    }
  }
}
</style>
