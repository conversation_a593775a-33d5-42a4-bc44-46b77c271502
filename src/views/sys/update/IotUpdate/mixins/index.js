import { typeList } from "@/api/regulation";
import { listComputer } from "@/api/bike/computer";
export default {
  data() {
    return {
      form: {},
      modelFn: null,
      typeList: {
        0: this.$t("bike.model.Meter"),
        9: "IOT"
      },
      modelData: {
        data: [],
        page: 1,
        more: true
      },
      // 表单校验
      rules: {
        type: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.model.DeviceKind"),
            trigger: "change"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.computer.model"),
            trigger: "change"
          }
        ],
        versionName: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("system.app.versionNum"),
            trigger: "blur"
          }
        ],
        versionCode: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("system.app.versionSn"),
            trigger: "blur"
          }
        ],
        crc: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("bike.model.crcCode"),
            trigger: "blur"
          }
        ],
        downLink: [
          {
            required: true,
            message:
              this.$t("form.upload") + this.$t("system.computer.downLink2"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    "form.type"(type) {
      switch (type) {
        case "9": // IOT
          this.modelFn = typeList;
          break;
        case "0": // 仪表
          this.modelFn = listComputer;
          break;
      }
    },
    "form.downLink"(downLink) {
        if(downLink) {
            this.clearValidateItem("form", "downLink");
        }
    }
  },
  methods: {
    // 设备型号
    getModelList({ page = 1, more = false, keyword = "" } = {}) {
      return new Promise((resolve, reject) => {
        this.modelFn({ p: page, key: keyword }).then(res => {
          const { list, total, pageNum, pageSize } = res.data;
          if (more) {
            this.modelData.data = [...this.modelData.data, ...list];
          } else {
            this.modelData.data = list;
          }
          this.modelData.more = pageNum * pageSize < total;
          this.modelData.page = pageNum;
          resolve();
        });
      });
    },
    getModelInfo(info) {
      if (!info) {
        this.form.deviceTypeId = "";
        this.form.code = "";
        return;
      }
      const { id, code } = JSON.parse(info);
      this.form.deviceTypeId = id;
      this.form.code = code;
    },
    clearModelCode() {
      this.modelData.data = [];
      this.$set(this.form, "code", "");
    }
  }
};
