<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="$t('system.computer.history')"
    :visible.sync="historyOpen"
    width="80%"
    center
  >
    <div>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            v-if="checkRolesAdd('dev')"
            @click="handleAdd"
          >
            {{ $t("queryParams.add") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0, aFn, getList)">
            {{ $t("system.computer.auth") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(1, aFn, getList)"">
            {{ $t("system.computer.disabled") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-refresh"
            v-debounce-click="handleQuery"
          >
            {{ $t("system.computer.refresh") }}
          </el-button>
        </el-col>
      </el-row>

      <el-table
        ref="multipleTableRef"
        row-key="id"
        v-loading="loading"
        :data="softUpdateList"
        :height="400"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" reserve-selection  align="center" />
        <el-table-column
          type="index"
          align="center"
          :label="$t('base.carexception.customerName')"
        >
          <template slot-scope="scope">
            {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('bike.model.DeviceKind')"
          prop="type"
          align="center"
        >
          <template slot-scope="{ row }">
            {{ typeList[row.type] }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('bike.computer.model')" align="center">
          {{ updateObj.code }}
        </el-table-column>
        <el-table-column
          :label="$t('system.app.versionNum')"
          prop="versionName"
          align="center"
        />
        <el-table-column
          :label="$t('system.app.versionSn')"
          prop="versionCode"
          align="center"
        />
        <el-table-column
          :label="$t('system.computer.desc')"
          prop="description"
          align="center"
          show-overflow-tooltip
        >
          <span slot-scope="scope" v-NoData="scope.row.description"></span>
        </el-table-column>
        <el-table-column
          :label="$t('system.computer.downLink')"
          prop="downLink"
          align="center"
        >
          <template v-slot="{ row }">
            <el-image
              v-if="row.downLink"
              style="width: 30px; height: 30px; cursor: pointer"
              :src="require('@/assets/image/xiazai.png')"
              v-debounce-click="() => urlDownload(row.downLink)"
            />
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.computer.status')" align="center">
          <template v-slot="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(row, aFn, getList)"
          />
        </template>
        </el-table-column>
        <el-table-column
          :label="$t('system.computer.auditStatus')"
          align="center"
          prop="state"
        >
          <template slot-scope="scope">
            {{ setAuditStatus(scope.row.state) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('system.computer.createBy')"
          align="center"
          prop="createBy"
        >
          <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
        </el-table-column>
        <el-table-column
          :label="$t('system.computer.createTime')"
          align="center"
          sortable
          prop="createTime"
          width="140"
        >
          <template slot-scope="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('system.computer.handle')"
          align="center"
          class-name="small-padding fixed-width"
          width="130"
        >
          <template v-slot="{ row }">
            <el-button type="text" @click="handleUpdate(row, 1)">
              {{ $t("bike.computer.update") }}
            </el-button>
            <el-button
              type="text"
              v-if="auditAuth(row)"
              @click="handleAudit(row)"
            >
              {{ $t("system.computer.audit") }}
            </el-button>
            <el-button
              v-if="reviewAuth(row)"
              type="text"
              @click="handleAudit(row)"
            >
              {{ $t("system.computer.release") }}
            </el-button>
            <el-button
              type="text"
              class="text-red"
              @click="handleDel([row.id], delFn, getList)"
            >
              {{ $t("bike.info.del") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.p"
        :limit.sync="queryParams.l"
        @pagination="getList"
      />

      <el-dialog
        :title="title"
        :visible.sync="open"
        width="700px"
        append-to-body
        center
        :close-on-click-modal="false"
      >
        <el-form ref="form" :model="form" :rules="rules" label-position="top">
          <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('bike.model.DeviceKind')" prop="type">
              <el-select
                v-model="form.type"
                clearable
                style="width: 100%"
                :placeholder="$t('form.select') + $t('bike.model.DeviceKind')"
                @change="clearModelCode"
              >
                <el-option
                  v-for="(value, key) in typeList"
                  :key="key"
                  :label="value"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('bike.computer.model')" prop="code">
              <selectLoadMore
                v-model="form.code"
                :data="modelData.data"
                :page="modelData.page"
                :hasMore="modelData.more"
                :request="getModelList"
                dictLabel="code"
                :moreParams="true"
                @getChange="getModelInfo"
                :placeholder="$t('form.select') + $t('bike.computer.model')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('system.app.versionNum')"
              prop="versionName"
            >
              <el-input
                v-model.trim="form.versionName"
                clearable
                :placeholder="$t('form.input') + $t('system.app.versionNum')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('system.app.versionSn')"
              prop="versionCode"
            >
              <el-input
                v-model.trim="form.versionCode"
                clearable
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="$t('form.input') + $t('system.app.versionSn')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('bike.model.crcCode')" prop="crc">
              <el-input
                v-model.trim="form.crc"
                clearable
                oninput="value=value.replace(/[^\d]/, '')"
                :placeholder="
                  $t('form.input') + $t('bike.model.crcCode')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.computer.desc')" prop="description">
              <el-input
                v-model.trim="form.description"
                type="textarea"
                clearable
                :rows="3"
                :minlength="0"
                :maxlength="100"
                :placeholder="
                  $t('system.computer.input') + $t('system.computer.desc')
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('system.computer.downLink2')" prop="downLink">
          <DrUpload
            v-model="form.downLink"
            :limit="1"
            :isOnePic="1"
            accept=".bin"
            :css="{ width: '100%' }"
            class="flex-direction align-start"
          >
            <el-button size="small" type="primary">
              {{ $t("system.computer.update") }}
            </el-button>
          </DrUpload>
        </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            :loading="isBtnLoading"
            @click="submitForm"
          >
            {{ $t("dialog.confirm") }}
          </el-button>
          <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
        </div>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script>
import {
  iotListDeviceGroup,
  iotUpdateSave,
  editIotDevice,
  iotAuthDevice,
  iotDeviceDelete,
  changeIotDeviceState
} from "@/api/system/control";
import {
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";
import { commonJs } from "@/mixinFile/common";
import deviceUpdateJs from "../mixins";

export default {
  mixins: [commonJs, deviceUpdateJs],
  props: ["content", "updateObj"],
  data() {
    return {
      aFn: iotAuthDevice,
      delFn: iotDeviceDelete,
      historyOpen: false,
      // 车型
      modelNameList: [],
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      edit: false,
      modelOptions: [],
      brandOptions: [],
      deviceOptions: [
        {
          key: this.$t("system.computer.stopwatch"),
          value: 1
        }
      ],
      isAdd: false,
      // 用户表格数据
      softUpdateList: [],
      // 码表类型
      computerMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      genderOptions: [],
      type: null,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: 1
      }
    };
  },
  watch: {
    historyOpen: {
      handler(bool) {
        if (bool) {
          this.getList();
        } else {
          this.$parent.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productModelList = [];
        this.productCodeList = [];
        this.form.productModel = "";
        this.form.code = "";
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.form.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productCodeList = [];
        this.form.code = "";
      }
    },
    /**
     * 审核状态回写
     */
    setAuditStatus(stata) {
      const maps = {
        0: this.$t("system.computer.toAudit"),
        1: this.$t("system.computer.toReview"),
        2: this.$t("system.computer.done")
      };
      return maps[stata];
    },
    /**
     * 审核
     */
    async handleAudit(row) {
      await changeIotDeviceState({
        id: row.id,
        state: Number(row.state) + 1 // 当前状态 + 1 到下个状态
      });
      this.getList();
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      iotListDeviceGroup({
        code: this.updateObj.code
      }).then(response => {
        this.softUpdateList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.form.type = this.updateObj.type;
      this.form.modelId = this.updateObj.modelId;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.isAdd = true;
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
    },
    handleUpdate(row, type) {
      this.reset();
      this.type = type;
      this.isAdd = false;
      this.handleChange(row.modelId);
      this.form = Object.assign({}, row);
      this.form.modelId = this.updateObj.modelId;
      this.open = true;
      this.title = this.$t("system.computer.handleUpdate");
    },
    handleChange(val) {
      for (let i = 0; i < this.modelOptions.length; i++) {
        if (this.modelOptions[i].id == val) {
          this.brandOptions = this.modelOptions[i].modelList;
        }
      }
    },
    /**
     * 审核权限
     */
    auditAuth(row) {
      if (row.state === 0) {
        return this.checkRoles("test");
      }
      return false;
    },
    /**
     * 复核权限
     */
    reviewAuth(row) {
      if (row.state === 1) {
        return this.checkRoles("dev_manager");
      }
      return false;
    },
    /**
     * 新增权限判断
     */
    checkRolesAdd(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      return this.$store.getters.roles.includes(role);
    },
    /**
     * 角色判断
     * admin 默认有所有权限
     */
    checkRoles(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      let res = false;
      if (Array.isArray(role)) {
        for (var i = 0; i < role.length; i++) {
          let item = role[i];
          let flag = this.$store.getters.roles.includes(item);
          if (flag) {
            res = true;
            break;
          }
        }
      } else {
        res = this.$store.getters.roles.includes(role);
      }
      return res;
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.selectVal && this.form.selectVal.length) {
            this.form.brandId = this.form.selectVal[0];
            this.form.modelId = this.form.selectVal[1];
          }
          let params = Object.assign({}, this.form);
          params.computerModel = params.computerModel == "default" ? "" : params.computerModel;
          if (!this.isAdd) {
            editIotDevice(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.updateSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            iotUpdateSave(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.addSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
