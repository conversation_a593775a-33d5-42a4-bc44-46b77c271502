<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <!-- <el-form-item :label="$t('bike.model.DeviceKind')" prop="type">
          <el-select
            v-model="queryParams.type"
            clearable
            style="width: 100%"
            :placeholder="$t('form.select') + $t('bike.model.DeviceKind')"
          >
            <el-option
              v-for="(value, key) in typeList"
              :key="key"
              :label="value"
              :value="+key"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('acc.log.deviceModel')" prop="modelId">
          <el-select
            v-model="queryParams.modelId"
            clearable
            :placeholder="$t('form.select') + $t('acc.log.deviceModel')"
            @change="handleQuery"
          >
            <el-option
              v-for="(item, index) in deviceTypeList"
              :key="index"
              :label="item.model"
              :value="item.key"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item :label="$t('bike.computer.model')" prop="code">
          <el-input
            v-model="queryParams.code"
            clearable
            style="width: 100%"
            :placeholder="$t('form.input') + $t('bike.computer.model')"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("discover.posted.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("discover.posted.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          v-if="checkRolesAdd('dev')"
          @click="handleAdd"
        >
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="softUpdateList"
      :height="tableHeight()"
    >
      <el-table-column
        type="index"
        align="center"
        :label="$t('base.carexception.customerName')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.DeviceKind')"
        prop="type"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ typeList[row.type] }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.computer.model')"
        prop="code"
        align="center"
      />
      <el-table-column
        :label="$t('bike.model.crcCode')"
        prop="crc"
        align="center"
      />
      <!-- <el-table-column
        :label="$t('acc.log.deviceModel')"
        prop="modelName"
        align="center"
      /> -->
      <el-table-column
        :label="$t('system.app.versionNum')"
        prop="versionName"
        align="center"
      />
      <el-table-column
        :label="$t('system.app.versionSn')"
        prop="versionCode"
        align="center"
      />
      <el-table-column
        :label="$t('system.computer.desc')"
        prop="description"
        align="center"
        show-overflow-tooltip
      >
        <span slot-scope="scope" v-NoData="scope.row.description"></span>
      </el-table-column>
      <el-table-column
        :label="$t('system.computer.createBy')"
        align="center"
        prop="createBy"
      >
        <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
      </el-table-column>
      <el-table-column
        :label="$t('system.computer.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.handle')" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleHistory(scope.row)">
            {{ $t("base.help.lookOver") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :title="title"
      :visible.sync="open"
      width="700px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('bike.model.DeviceKind')" prop="type">
              <el-select
                v-model="form.type"
                clearable
                style="width: 100%"
                :placeholder="$t('form.select') + $t('bike.model.DeviceKind')"
                @change="clearModelCode"
              >
                <el-option
                  v-for="(value, key) in typeList"
                  :key="key"
                  :label="value"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('bike.computer.model')" prop="code">
              <selectLoadMore
                v-model="form.code"
                :data="modelData.data"
                :page="modelData.page"
                :hasMore="modelData.more"
                :request="getModelList"
                dictLabel="code"
                :moreParams="true"
                @getChange="getModelInfo"
                :placeholder="$t('form.select') + $t('bike.computer.model')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('system.app.versionNum')"
              prop="versionName"
            >
              <el-input
                v-model.trim="form.versionName"
                clearable
                :placeholder="$t('form.input') + $t('system.app.versionNum')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('system.app.versionSn')"
              prop="versionCode"
            >
              <el-input
                v-model.trim="form.versionCode"
                clearable
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="$t('form.input') + $t('system.app.versionSn')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('bike.model.crcCode')" prop="crc">
              <el-input
                v-model.trim="form.crc"
                clearable
                oninput="value=value.replace(/[^\d]/, '')"
                :placeholder="
                  $t('form.input') + $t('bike.model.crcCode')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.computer.desc')" prop="description">
              <el-input
                v-model.trim="form.description"
                type="textarea"
                clearable
                :rows="3"
                :minlength="0"
                :maxlength="100"
                :placeholder="
                  $t('system.computer.input') + $t('system.computer.desc')
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('system.computer.downLink2')" prop="downLink">
          <DrUpload
            v-model="form.downLink"
            :limit="1"
            :isOnePic="1"
            accept=".bin"
            :css="{ width: '100%' }"
            class="flex-direction align-start"
          >
            <el-button size="small" type="primary">
              {{ $t("system.computer.update") }}
            </el-button>
          </DrUpload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>

    <List ref="listRef" :updateObj="updateObj" />
  </div>
</template>

<script>
import {
  iotDeviceList,
  iotUpdateSave,
  editIotDevice,
  iotDeviceDelete
} from "@/api/system/control";
import {
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";

import { listDictModel } from "@/api/base/dict";
import List from "./components/list";
import deviceUpdateJs from "./mixins";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [deviceUpdateJs, commonJs],
  components: {
    List,
    selectLoadMore: () => import("@/components/selectLoadMore")
  },
  data() {
    return {
      aFn: iotDeviceDelete,
      isBtnLoading: false,
      // 车型
      modelNameList: [],
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      updateObj: {},
      modelData: {
        data: [],
        page: 1,
        more: true
      },
      edit: false,
      historyItem: {},
      historyOpen: false,
      modelOptions: [],
      brandOptions: [],
      deviceOptions: [
        {
          key: this.$t("system.computer.stopwatch"),
          value: 1
        }
      ],
      // 用户表格数据
      softUpdateList: [],
      // 码表类型
      computerOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        code: ""
      },
      // 表单校验
      rules: {
        type: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.model.DeviceKind"),
            trigger: "change"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('form.select') + this.$t('bike.computer.model'),
            trigger: "change"
          }
        ],
        versionName: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("system.app.versionNum"),
            trigger: "blur"
          }
        ],
        versionCode: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("system.app.versionSn"),
            trigger: "blur"
          }
        ],
        crc: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("bike.model.crcCode"),
            trigger: "blur"
          }
        ],
        downLink: [
          {
            required: true,
            message:
              this.$t("form.upload") + this.$t("system.computer.downLink2"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  computed: {
    deviceTypeList() {
      return this.computerOptions.filter(
        item => item.type === this.queryParams.type
      );
    }
  },
  created() {
    listDictModel().then(response => {
      this.computerOptions = response.data;
      this.getList();
    });
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productModelList = [];
        this.productCodeList = [];
        this.form.productModel = "";
        this.form.code = "";
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.form.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productCodeList = [];
        this.form.code = "";
      }
    },
    /**
     * 新增权限判断
     */
    checkRolesAdd(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      return this.$store.getters.roles.includes(role);
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      iotDeviceList(this.queryParams).then(response => {
        this.softUpdateList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.selectVal && this.form.selectVal.length) {
            this.form.brandId = this.form.selectVal[0];
            this.form.modelId = this.form.selectVal[1];
          }
          this.isBtnLoading = true;
          let params = Object.assign({}, this.form);
          params.computerModel =
            params.computerModel == "default" ? "" : params.computerModel;
          if (params.id !== undefined) {
            editIotDevice(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.updateSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            iotUpdateSave(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.addSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    },
    handleHistory(row) {
      this.$refs.listRef.historyOpen = true;
      this.updateObj = {
        code: row.code,
        type: row.type
      };
    }
  }
};
</script>
