<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('system.app.appSystem')" prop="key">
          <el-select
            v-model="queryParams.key"
            clearable
            :placeholder="$t('form.select') + $t('system.app.appSystem')"
            @change="handleQuery"
          >
            <el-option
              v-for="item in appSystemList"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("discover.posted.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("discover.posted.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['sys:update:add']"
        >
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['sys:update:auth']"
        >
          {{ $t("system.computer.auth") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['sys:update:auth']"
        >
          {{ $t("system.computer.disabled") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      row-key="id"
      :height="tableHeight()"
      :data="softUpdateList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        type="index"
        align="center"
        :label="$t('base.carexception.customerName')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('system.app.versionNum')"
        prop="versionName"
        align="center"
      />
      <el-table-column
        :label="$t('system.app.versionSn')"
        prop="versionCode"
        align="center"
      />
      <el-table-column
        :label="$t('system.app.appSystem')"
        prop="appSystem"
        align="center"
      />
      <el-table-column
        :label="$t('system.app.desc')"
        prop="des"
        align="center"
        :show-overflow-tooltip="true"
      >
        <span slot-scope="scope" v-NoData="scope.row.des"></span>
      </el-table-column>
      <el-table-column
        :label="$t('system.computer.downLink')"
        prop="link"
        align="center"
      >
        <template v-slot="{ row }">
          <el-image
            v-if="row.link"
            style="width: 30px; height: 30px; cursor: pointer"
            :src="require('@/assets/image/xiazai.png')"
            v-debounce-click="() => urlDownload(row.link)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('system.app.forceUpdate')"
        prop="forceUpdate"
        align="center"
      >
        <template slot-scope="scope">
          {{
            scope.row.forceUpdate === 1
              ? $t("system.app.yes")
              : $t("system.app.no")
          }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.app.status')" align="center">
        <template v-slot="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('system.app.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template v-slot="{ row }">{{ parseTime(row.createTime) }}</template>
      </el-table-column>
      <el-table-column
        :label="$t('system.app.handler')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['sys:update:edit']"
          >
            {{ $t("queryParams.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="600px"
      append-to-body
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              :label="$t('system.app.versionNum')"
              prop="versionName"
            >
              <el-input
                v-model.trim="form.versionName"
                clearable
                :placeholder="
                  $t('system.app.input') + $t('system.app.versionNum')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('system.app.versionSn')"
              prop="versionCode"
            >
              <el-input
                v-model.number="form.versionCode"
                clearable
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="
                  $t('system.app.input') + $t('system.app.versionSn')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.notice.noticeType')" prop="type">
              <el-select
                v-model="form.type"
                :placeholder="$t('system.notice.noticeType')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in deviceOptions"
                  :key="dict.key"
                  :label="dict.key"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('system.app.forceUpdate')"
              prop="forceUpdate"
            >
              <el-select
                v-model="form.forceUpdate"
                :placeholder="
                  $t('system.app.input') + $t('system.app.forceUpdate')
                "
                clearable
                style="width: 100%"
              >
                <el-option :label="$t('system.app.no')" :value="0"></el-option>
                <el-option :label="$t('system.app.yes')" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item :label="$t('system.app.desc')" prop="des">
              <el-input
                v-model.trim="form.des"
                type="textarea"
                rows="4"
                resize="none"
                :placeholder="$t('system.app.input') + $t('system.app.desc')"
              />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item
              v-if="form.type == 1"
              :label="$t('system.app.downLinkUp')"
              prop="link"
            >
              <DrUpload
                v-model="form.link"
                :limit="1"
                :isOnePic="1"
                :css="{ width: '100%' }"
                class="flex-direction align-start"
              >
                <el-button type="primary">
                  {{ $t("queryParams.upload") }}
                </el-button>
              </DrUpload>
            </el-form-item>
            <el-form-item
              :label="$t('acc.msg.msg.linkAddress')"
              prop="link"
              v-else
            >
              <el-input
                v-model.trim="form.link"
                clearable
                :placeholder="$t('form.input') + $t('acc.msg.msg.linkAddress')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSoftUpdate,
  authSoftUpdate,
  addSoftUpdate,
  editSoftUpdate
} from "@/api/system/regulation";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  name: "SysUpdateApp",
  data() {
    const validateVersionName = (rule, value, callback) => {
      const reg = /^(\d+\.)(\d+\.)(\d+)$|^(\d+\.)(\d+)$|^(\d+)$/;
      if (value === "") {
        callback(new Error(this.$t("system.app.rules.versionNum")));
      } else if (!reg.test(value)) {
        callback(new Error(this.$t("mointerObj.correctVerionNumber")));
      } else {
        callback();
      }
    };
    return {
      aFn: authSoftUpdate,
      deviceOptions: [
        {
          key: "Android",
          value: 1
        },
        {
          key: "ios",
          value: 2
        }
      ],
      // 用户表格数据
      softUpdateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      genderOptions: [],
      appSystemList: [
        {
          value: "IOS"
        },
        {
          value: "Android"
        }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: ""
      },
      // 表单参数
      form: {
        id: undefined,
        type: 1,
        forceUpdate: 0
      },

      // 表单校验
      rules: {
        type: [
          {
            required: true,
            message: this.$t("system.notice.noticeType"),
            trigger: "change"
          }
        ],
        versionName: [
          {
            required: true,
            validator: validateVersionName,
            trigger: ["blur", "change"]
          }
        ],
        versionCode: [
          {
            required: true,
            message: this.$t("system.app.rules.versionSn"),
            trigger: "blur"
          }
        ],
        desc: [
          {
            required: false,
            message: "",
            trigger: "blur"
          }
        ],
        link: [
          {
            required: true,
            message:
              this.$t("system.app.rules.downLink") +
              this.$t("system.app.downLinkUp"),
            trigger: "blur"
          }
        ],
        mdCode: [
          {
            required: false,
            message: this.$t("system.app.rules.mdCode"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  mounted() {
    this.getList();
  },
  watch: {
    "form.type"(newVal) {
      if (newVal === 1) {
        this.rules.link[0].message =
          this.$t("system.app.rules.downLink") +
          this.$t("system.app.downLinkUp");
      } else {
        this.rules.link[0].required = false;
      }
    },
    "form.link"(link) {
      if (link) {
        this.clearValidateItem("form", "link");
      }
    }
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listSoftUpdate(this.queryParams)
        .then(response => {
          this.softUpdateList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        forceUpdate: 0,
        link: "",
        type: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
    },
    handleUpdate(row) {
      this.reset();
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = this.$t("system.app.handleUpdate");
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            editSoftUpdate(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.updateSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addSoftUpdate(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.addSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
