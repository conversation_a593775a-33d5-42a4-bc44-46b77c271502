<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],

  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "307px"
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {},
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(content) {
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        title: {
          text: this.$t('home.userChart'),
          left: "left",
          top: "0"
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        legend: {
          left: "center",
          bottom: "10"
        },
        series: [
          {
            name: this.$t('home.userChart'),
            type: "pie",
            radius: [15, 85],
         
            center: ["50%", "48%"],
            data: [
              {
                value: content.activeUserNum,
                name: `活跃用户数${content.activeUserNum}`
              },
              {
                value: content.sumUserNum,
                name: `总用户数${content.sumUserNum-content.activeUserNum}`
              }
            ],
            color: ["#FF33FF", "#00CCFF"]
          }
        ]
      });
    }
  }
};
</script>
