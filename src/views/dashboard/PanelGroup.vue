<template>
  <div>
    <el-row :gutter="15" class="panel-group">
      <el-col :span="6" :xs="24" :sm="12" :md="12" :lg="6">
        <div class="total elColBottom" v-if="data">
          <div class="statistical_total">
            <div class="tottal_title">
              <span></span>
              <span>{{ $t("home.totalUsers") }}</span>
            </div>
            <div class="count">
              <count-to :start-val="0" :end-val="data.user.sum" :duration="1" />
            </div>
          </div>
          <div class="increase">
            <div class="increase_total">
              {{ $t("home.increaseDay") }}
              <span>
                <count-to
                  :end-val="data.user.increaseDay"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseWeek") }}
              <span>
                <count-to
                  :end-val="data.user.increaseWeek"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseMonth") }}
              <span>
                <count-to
                  :end-val="data.user.increaseMonth"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6" :xs="24" :sm="12" :md="12" :lg="6">
        <div class="total elColBottom" v-if="data">
          <div class="statistical_total">
            <div class="tottal_title">
              <span></span>
              <span>{{ $t("home.totalBike") }}</span>
            </div>
            <div class="count">
              <count-to
                :end-val="data.bike.sum"
                :duration="3000"
                class="card-panel-num"
              />
            </div>
          </div>
          <div class="increase">
            <div class="increase_total">
              {{ $t("home.increaseDay") }}
              <span>
                <count-to
                  :end-val="data.bike.increaseDay"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseWeek") }}
              <span>
                <count-to
                  :end-val="data.bike.increaseWeek"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseMonth") }}
              <span>
                <count-to
                  :end-val="data.bike.increaseMonth"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6" :xs="24" :sm="12" :md="12" :lg="6" v-if="data">
        <div class="total">
          <div class="statistical_total">
            <div class="tottal_title">
              <span></span>
              <span>{{ $t("home.bindBike") }}</span>
            </div>
            <div class="count">
              <count-to
                :end-val="data.bind.sum"
                :duration="3000"
                class="card-panel-num"
              />
            </div>
          </div>
          <div class="increase">
            <div class="increase_total">
              {{ $t("home.increaseDay") }}
              <span>
                <count-to
                  :end-val="data.bind.increaseDay"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseWeek") }}
              <span>
                <count-to
                  :end-val="data.bind.increaseWeek"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseMonth") }}
              <span>
                <count-to
                  :end-val="data.bind.increaseMonth"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6" :xs="24" :sm="12" :md="12" :lg="6" v-if="data">
        <div class="total">
          <div class="statistical_total">
            <div class="tottal_title">
              <span></span>
              <span>{{ $t("home.faultRecordQuantity") }}</span>
            </div>
            <div class="count">
              <count-to
                :end-val="data.fault.sum"
                :duration="3000"
                class="card-panel-num"
              />
            </div>
          </div>
          <div class="increase">
            <div class="increase_total">
              {{ $t("home.increaseDay") }}
              <span>
                <count-to
                  :end-val="data.fault.increaseDay"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseWeek") }}
              <span>
                <count-to
                  :end-val="data.fault.increaseWeek"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
            <div class="increase_total">
              {{ $t("home.increaseMonth") }}
              <span>
                <count-to
                  :end-val="data.fault.increaseMonth"
                  :duration="3000"
                  class="card-panel-num"
                />
              </span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CountTo from "vue-count-to";

export default {
  props: {
    data: {
      default: null
    }
  },
  components: {
    CountTo
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  padding-bottom: 15px;
}
.total {
  background: #fff;
  min-height: 114.51px;
  @media screen and (max-width: 992px) {
    .elColBottom {
      margin-bottom: 30px;
    }
  }
  .statistical_total {
    box-sizing: border-box;
    border-bottom: 1px solid #eee;
  }
  .tottal_title {
    display: flex;
    padding-top: 10px;
    padding-left: 15px;
    span {
      display: block;
      width: 3px;
      background: #1890ff;
    }
  }
}
.tottal_title span:nth-of-type(2) {
  width: auto;
  background: none;
  padding-left: 7px;
}
.count {
  display: flex;
  font-size: 26px;
  line-height: 50px;
  padding-left: 15px;
}
.increase {
  display: flex;
  font-size: 13px;
  line-height: 35px;
  padding: 0 15px;
  box-sizing: border-box;
  .increase_total {
    flex: 1;
    span {
      color: #1890ff;
      padding-left: 5px;
    }
  }
}
</style>
