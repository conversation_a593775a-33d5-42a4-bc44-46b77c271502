<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "400px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(content) {
      this.chart = echarts.init(this.$el, "macarons");
      this.chart.setOption({
        title: {
          text: this.$t("home.errChart"),
          left: "left",
          top: "0",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          top: 40,
          left: "8%",
          right: "2%",
          bottom: 40,
          containLabel: true,
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 1,
            end: 100,
          },
          {
            type: "inside",
            realtime: true,
            start: 1,
            end: 100,
          },
        ],
        xAxis: [
          {
            type: "category",
            data: content.map((item) =>
              this.parseTime(item.time, "{y}-{m}-{d}")
            ),
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "数量",
            type: "bar",
            stack: "vistors",
            barWidth: "10%",
            data: content.map((item) => item.num),
            animationDuration,
            color: ["#f4516c"],
          },
        ],
      });
    },
  },
};
</script>
