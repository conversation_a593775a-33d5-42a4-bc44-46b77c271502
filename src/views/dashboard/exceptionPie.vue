<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],

  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "400px"
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {},
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(content) {
      this.chart = echarts.init(this.$el, "macarons");
      for (let key of content) {
        key.value = key.num;
        key.name = key.time;
      }
      this.chart.setOption({
        title: {
          text:this.$t('home.errChart'),
          left: "left",
          top: "0"
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        legend: {
          left: "center",
          bottom: "10"
        },

        series: [
          {
            name: "故障统计",
            type: "pie",
            roseType: "radius",
            radius: [15, 95],
            center: ["50%", "40%"],
            data: content,
            animationEasing: "cubicInOut"
          }
        ]
      });
    }
  }
};
</script>
