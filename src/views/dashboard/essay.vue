<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "300px"
    },
    essayData: {}
  },
  data() {
    return {
      chart: null,
      xDate: [], // x轴的值
      yDateOne: [],
      yDateTwo: []
    };
  },
  watch: {
    essayData(val) {
      if (val) {
        this.initChart(val);
      }
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(content) {
      this.xDate = [];
      this.yDateOne = [];
      this.yDateTwo = [];
      if (content.type1 == "1") {
        content.dataOne.map(item => {
          this.xDate.push(this.parseTime(item.date, "{y}-{m}-{d}"));
          this.yDateOne.push(item.num);
        });
        content.dataTwo.map(item => this.yDateTwo.push(item.num));
      } else if (content.type1 == "2") {
        content.dataOne.map(item => {
          this.xDate.push(item.date);
          this.yDateOne.push(item.total);
        });
        content.dataTwo.map(item => this.yDateTwo.push(item.total));
      } else if (content.type1 == "3") {
        content.dataOne.map(item => {
          this.xDate.push(item.date);
          this.yDateOne.push(item.total);
        });
        content.dataTwo.map(item => {
          this.yDateTwo.push(item.total);
        });
      }
      this.chart = echarts.init(this.$el, "macarons");

      if (
        !this.xDate.length &&
        !this.yDateOne.length &&
        !this.yDateTwo.length
      ) {
        this.chart.setOption({
          title: {
            text: "暂无数据",
            x: "center",
            y: "center",
            textStyle: {
              fontSize: 16,
              fontWeight: "bold"
            }
          }
        });
      } else {
        this.chart.setOption({
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
            }
          },
          grid: {
            top: 30,
            left: 20,
            right: 20,
            bottom: 20,
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: this.xDate,
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              name: this.$t("home.posted.comment"),
              type: "line",
              smooth: true, // 拐点是否平滑
              showSymbol: true, // 是否默认展示圆点
              data: this.yDateOne,
              color: ["#1890ff"]
            },
            {
              name: this.$t("home.posted.essay"),
              type: "line",
              smooth: true, // 拐点是否平滑
              showSymbol: true, // 是否默认展示圆点
              data: this.yDateTwo,
              color: ["#f98b3f"]
            }
          ]
        });
      }
    }
  }
};
</script>
