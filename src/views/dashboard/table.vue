<template>
  <div class="list-cont">
    <div class="marquee-wrap">
      <el-row class="table-header">
        <el-col :span="4">{{ $t("acc.riding.not.nickName") }}</el-col>
        <el-col :span="4">{{ $t("acc.riding.not.ridingTime") }}</el-col>
        <el-col :span="4">{{ $t("acc.riding.not.email") }}</el-col>
        <el-col :span="4">{{ $t("acc.riding.not.mileage") }}</el-col>
        <el-col :span="4">{{ $t("acc.riding.not.maxSpeed") }}</el-col>
        <el-col :span="4">{{ $t("acc.riding.not.syncTime") }}</el-col>
      </el-row>
      <div class="list-wrap">
        <ul
          class="marquee-list"
          :class="{ 'animate-up': animateUp }"
          @mouseover="mouseover"
          @mouseleave="mouseleave"
        >
          <el-row class="li" v-for="(item, index) in list" :key="index">
            <el-col :span="4">{{ item.nickName }}</el-col>
            <el-col :span="4">{{ changetime(item.ridingTime) }}</el-col>
            <el-col :span="4">{{ item.email }}</el-col>
            <el-col :span="4">
              <span>{{ (item.mileage*0.1).toFixed(1)}}km</span></el-col
            >
            <el-col :span="4">
              <span>{{ (item.maxSpeed / 10).toFixed(1) }}km/h</span></el-col
            >
            <el-col :span="4">
              <span>{{ parseTime(item.syncTime) }}</span></el-col
            >
          </el-row>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      animateUp: false,

      timer: null,
    };
  },
  mounted() {
    clearInterval(this.timer);
    this.timer = setInterval(this.scrollAnimate, 2000);
  },
  methods: {
    scrollAnimate() {
      this.animateUp = true;
      setTimeout(() => {
        this.list.push(this.list[0]);
        this.list.shift();
        this.animateUp = false;
      }, 1000);
    },
    mouseover() {
      clearInterval(this.timer);
    },
    mouseleave() {
      this.timer = setInterval(this.scrollAnimate, 2000);
    },
    changetime(value) {
      var secondTime = parseInt(value); // 秒
      var minuteTime = "00"; // 分
      var hourTime = "00"; // 小时
      if (secondTime > 60) {
        //如果秒数大于60，将秒数转换成整数
        //获取分钟，除以60取整数，得到整数分钟
        minuteTime = parseInt(secondTime / 60);
        //获取秒数，秒数取佘，得到整数秒数
        secondTime = parseInt(secondTime % 60);
        //如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          //获取小时，获取分钟除以60，得到整数小时
          hourTime = parseInt(minuteTime / 60);
          //获取小时后取佘的分，获取分钟除以60取佘的分
          minuteTime = parseInt(minuteTime % 60);
        }
      }
      let h = parseInt(hourTime);
      let m = parseInt(minuteTime);
      let s = parseInt(secondTime);
      h = h > 10 ? h : "0" + h;
      m = m > 10 ? m : "0" + m;
      s = s > 10 ? s : "0" + s;
      let time = h + ":" + m + ":" + s;

      return time;
    },
  },
  destroyed() {
    clearInterval(this.timerNum);
    clearInterval(this.timer);
  },
};
</script>

<style lang='scss'  >
.list-cont {
  height: 522px;
  border: 1px solid #dcdfe6;
}
.marquee-wrap {
  margin: 0 auto;
  overflow: hidden;
  .table-header {
    display: flex;
    border-bottom: 1px solid #dcdfe6;
    > div {
      flex: 1;
      height: 49px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1px solid #dcdfe6;
      color: #666;
      &:last-child {
        border-right: none;
      }
    }
  }
  .list-wrap {
    height: 470px;
    overflow: hidden;
    .marquee-list {
      margin: 0;
      padding: 0;
      .li {
        list-style: none;
        color: #666;
        height: 48px;
        display: flex;
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        > div {
          border-bottom: 1px solid #dcdfe6;
          border-right: 1px solid #dcdfe6;
          display: inline-flex;
          flex-flow: row wrap;
          align-items: center;
          justify-content: center;
          word-break: break-all;
          padding: 0 10px;
          &:last-child {
            border-right: none;
          }
        }
      }
    }
  }
  .animate-up {
    transition: all 1s ease-in-out;
    transform: translateY(-48px);
  }
}
</style>
