<template>
  <div :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "270px"
    },
    circleData: {}
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    circleData(val) {
      if (val) {
        this.initChart(val);
      }
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(content) {
      const data = content.map(item => {
        return { value: item.num, name: item.brandName };
      });
      this.chart = echarts.init(this.$el, "macarons");
      if (!data.length) {
        this.chart.setOption({
          title: {
            text: "暂无数据",
            x: "center",
            y: "center",
            textStyle: {
              fontSize: 16,
              fontWeight: "bold"
            }
          }
        });
      } else {
        this.chart.setOption({
          tooltip: {
            trigger: "item",
            formatter: "{b} : {c} ({d}%)"
          },
          color: ["#1890ff", "#f98b3f", "#91cc75", "#ee6666"],
          series: [
            {
              name: "Radius Mode",
              type: "pie",
              radius: ["50%", "80%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#fff",
                borderWidth: 2
              },
              label: {
                show: false,
                position: "center",
                formatter: "{b}" + "\n\r" + "{d}%"
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: "16",
                  fontWeight: "bold"
                }
              },
              labelLine: {
                show: false
              },
              data
            }
          ]
        });
      }
    }
  }
};
</script>
