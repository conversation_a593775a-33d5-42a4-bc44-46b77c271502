<template>
  <el-table
    :data="list"
    style="width: 100%"
    height="461px"
    border
    class="style-pad"
  >
    <el-table-column
      width="65"
      type="index"
      :label="$t('home.Rank')"
      align="center"
    ></el-table-column>
    <el-table-column prop="nickName" :label="$t('home.user')" align="center">
      <template slot-scope="scope" align="center">
        <div class="row-item padding-lr-xs">
          <el-avatar
            style="height: 25px; width: 25px"
            :src="scope.row.headName"
            class="mr5"
          ></el-avatar>
          <span>{{ scope.row.nickName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      prop="mileage"
      :label="$t('home.percentage')"
      align="center"
    >
      <template slot-scope="scope">
        <div class="row-item flex justify-center" :gutter="20">
          {{ scope.row.mileage }}{{ $t("home.percentage") }}
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    list: {
      default: [],
    },
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 0) {
        return "Danger-row";
      } else if (rowIndex === 1) {
        return "Warning-row";
      } else if (rowIndex === 2) {
        return "Info-row";
      }
      return "";
    },
  },
};
</script>

<style lang='scss'>
.style-pad.el-table th.is-leaf,
.el-table td {
  padding: 8px;
}
</style>