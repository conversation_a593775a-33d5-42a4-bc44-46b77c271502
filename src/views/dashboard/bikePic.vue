<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "530px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(content) {
      this.chart = echarts.init(this.$el, "macarons");
      let bArr = [];
      let dataShadow = [];
      let data = content.map((item) => item.num);
      let yMax = Math.max(...data) + (Math.max(...data) % 2) + 1;

      for (var i = 0; i < data.length; i++) {
        dataShadow.push(yMax);
      }
      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          top: 30,
          left: 20,
          right: 20,
          bottom: 20,
          containLabel: true,
        },
        dataZoom: [
          {
            show: false,
            realtime: true,
            start: 1,
            end: 100,
          },
          {
            type: "inside",
            realtime: true,
            start: 1,
            end: 100,
          },
        ],
        xAxis: [
          {
            type: "category",
            data: content.map((item) =>
              this.parseTime(item.date, "{y}-{m}-{d}")
            ),
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            splitNumber: 12,
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          // {
          //   // For shadow
          //   type: "bar",
          //   itemStyle: {
          //     color: "rgba(0,0,0,0.05)",
          //   },
          //   barGap: "-100%",
          //   barCategoryGap: "20%",
          //   data: dataShadow,
          //   animation: false,
          // },
          {
            type: "bar",
            stack: "vistors",
            barWidth: 20,
            data: data,
            animationDuration,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#83bff6" },
                { offset: 0.5, color: "#188df0" },
                { offset: 1, color: "#188df0" },
              ]),
            },
            // color: ["#f4516c"],
          },
        ],
      });
    },
  },
};
</script>
