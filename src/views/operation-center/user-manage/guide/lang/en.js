export default {
  title: 'Home Guide Management',
  description: 'Manage home guide content, including add, edit, delete and status control',
  
  // Search Form
  search: {
    title: 'Title',
    titlePlaceholder: 'Please enter guide title',
    status: 'Status',
    statusPlaceholder: 'Please select status'
  },
  
  // Table Fields
  table: {
    id: 'ID',
    title: 'Title',
    detail: 'Description',
    img: 'Image',
    sort: 'Sort',
    status: 'Status',
    createTime: 'Create Time',
    actions: 'Actions'
  },
  
  // Status
  status: {
    enabled: 'Enabled',
    disabled: 'Disabled',
    all: 'All'
  },
  
  // Actions
  actions: {
    add: 'Add Guide',
    edit: 'Edit',
    delete: 'Delete',
    batchDelete: 'Batch Delete',
    enable: 'Enable',
    disable: 'Disable',
    batchEnable: 'Batch Enable',
    batchDisable: 'Batch Disable',
    viewImage: 'View Image',
    confirmDelete: 'Are you sure to delete selected guides?',
    confirmEnable: 'Are you sure to enable selected guides?',
    confirmDisable: 'Are you sure to disable selected guides?',
    selectRows: 'Please select data to operate'
  },
  
  // Form
  form: {
    title: 'Title',
    titlePlaceholder: 'Please enter guide title',
    detail: 'Description',
    detailPlaceholder: 'Please enter guide description',
    img: 'Image',
    imgPlaceholder: 'Please enter image URL or upload image',
    sort: 'Sort',
    sortPlaceholder: 'Please enter sort value (smaller number comes first)',
    status: 'Status',
    statusOptions: {
      enabled: 'Enabled',
      disabled: 'Disabled'
    }
  },
  
  // Validation
  validation: {
    titleRequired: 'Title is required',
    detailRequired: 'Description is required',
    imgRequired: 'Image is required',
    sortRequired: 'Sort is required',
    sortNumber: 'Sort must be a number'
  },
  
  // Messages
  messages: {
    addSuccess: 'Add successfully',
    updateSuccess: 'Update successfully',
    deleteSuccess: 'Delete successfully',
    enableSuccess: 'Enable successfully',
    disableSuccess: 'Disable successfully',
    batchDeleteSuccess: 'Batch delete successfully',
    batchEnableSuccess: 'Batch enable successfully',
    batchDisableSuccess: 'Batch disable successfully',
    loadError: 'Data load failed',
    operationError: 'Operation failed',
    noImage: 'No image available'
  },
  
  // Dialog Titles
  dialog: {
    add: 'Add Guide',
    edit: 'Edit Guide',
    imagePreview: 'Image Preview'
  },
  
  // Common
  common: {
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    selectAll: 'Select All',
    pleaseSelect: 'Please Select'
  }
}
