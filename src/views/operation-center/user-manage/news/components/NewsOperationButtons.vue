<template>
  <el-row :gutter="10" class="mb8">
    <el-col :span="1.5">
      <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd">{{
        t("operationButtons.add")
      }}</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button type="danger" plain icon="el-icon-delete" :disabled="multiple" @click="handleDelete">{{
        t("operationButtons.delete")
      }}</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button type="info" plain icon="el-icon-search" @click="toggleSearch">{{
        showSearch
          ? t("operationButtons.hideSearch")
          : t("operationButtons.showSearch")
      }}</el-button>
    </el-col>
  </el-row>
</template>

<script>
import { createI18nManager } from "../lang";

export default {
  name: "NewsOperationButtons",
  props: {
    multiple: {
      type: <PERSON><PERSON><PERSON>,
      required: true,
    },
    showSearch: {
      type: <PERSON>olean,
      required: true,
    },
  },
  data() {
    return {
      i18nManager: null,
    };
  },
  created() {
    this.i18nManager = createI18nManager(this.$i18n);
  },
  methods: {
    t(key, args) {
      return this.i18nManager.getText(key, args);
    },
    handleAdd() {
      this.$emit("add");
    },
    handleDelete() {
      this.$emit("delete");
    },
    toggleSearch() {
      this.$emit("update:showSearch", !this.showSearch);
    },
  },
};
</script>
