<template>
  <el-form ref="queryForm" :model="queryParams" :inline="true" v-show="showSearch" label-width="68px">
    <el-form-item :label="t('searchForm.title')" prop="title">
      <el-input v-model="queryParams.title" :placeholder="t('searchForm.placeholder.title')" clearable
        @keyup.enter.native="handleQuery" />
    </el-form-item>
    <el-form-item :label="t('searchForm.status')" prop="status">
      <el-select v-model="queryParams.status" :placeholder="t('form.select') + t('searchForm.status')" clearable>
        <el-option :label="t('searchForm.statusOptions.enabled')" :value="0" />
        <el-option :label="t('searchForm.statusOptions.disabled')" :value="1" />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{
        t("searchForm.search")
      }}</el-button>
      <el-button icon="el-icon-refresh" @click="resetQuery">{{
        t("searchForm.reset")
      }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { createI18nManager } from "../lang";

export default {
  name: "NewsSearchForm",
  props: {
    queryParams: {
      type: Object,
      required: true,
    },
    showSearch: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      i18nManager: null,
    };
  },
  created() {
    this.i18nManager = createI18nManager(this.$i18n);
  },
  methods: {
    t(key, args) {
      return this.i18nManager.getText(key, args);
    },
    handleQuery() {
      this.$emit("query");
    },
    resetQuery() {
      this.$emit("reset");
    },
  },
};
</script>
