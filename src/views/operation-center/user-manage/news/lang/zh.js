export default {
		title: "新闻",
		searchForm: {
			title: "标题",
			placeholder: {
				title: "请输入文章标题"
			},
			status: "状态",
			statusOptions: {
				enabled: "启用",
				disabled: "禁用"
			},
			search: "搜索",
			reset: "重置"
		},
		operationButtons: {
			add: "新增",
			delete: "删除",
			hideSearch: "隐藏搜索",
			showSearch: "显示搜索"
		},
		table: {
			id: "ID",
			thumbnail: "缩略图",
			noImage: "暂无图片",
			articleTitle: "文章标题",
			imageCount: "图片数量",
			likeCount: "点赞数",
			commentCount: "评论数",
			status: "状态",
			creator: "创建者",
			createTime: "创建时间",
			operations: "操作",
			view: "查看",
			edit: "修改",
			delete: "删除"
		},
		dialog: {
			addNews: "添加新闻",
			editNews: "修改新闻",
			confirm: "确 定",
			cancel: "取 消",
			newsDetail: "新闻详情",
			unknown: "未知",
			like: "点赞",
			comment: "评论",
			image: "图片",
			noThumbnail: "暂无缩略图",
			articleImages: "文章图片",
			images: "张",
			articleContent: "文章内容",
			noData: "暂无数据",
			failedToGetNewsList: "获取新闻列表失败",
			failedToGetNewsDetail: "获取新闻详情失败",
			modifiedSuccessfully: "修改成功",
			addedSuccessfully: "新增成功",
			confirmDelete: "是否确认删除新闻编号为{0}的数据项？",
			deleteSuccess: "删除成功",
			deleteFailed: "删除新闻失败",
			enable: "启用",
			disable: "停用",
			confirmStatusChange: "确认要{0}新闻吗？",
			statusChangeFailed: "状态修改失败",
			imagePreview: "图片预览",
			close: "关闭",
			imageLoadFailed: "图片加载失败",
			warn: "警告",
			addSuccess: "成功"
		},
		validation: {
			titleRequired: "文章标题不能为空",
			contentRequired: "文章内容不能为空",
			thumbnailRequired: "请上传缩略图"
		},
		form: {
			select: "请选择"
		}
}
