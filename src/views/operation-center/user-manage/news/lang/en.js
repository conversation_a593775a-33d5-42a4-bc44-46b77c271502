export default {
    title: "News",
    searchForm: {
      title: "Title",
      placeholder: {
        title: "Please enter article title"
      },
      status: "Status",
      statusOptions: {
        enabled: "Enabled",
        disabled: "Disabled"
      },
      search: "Search",
      reset: "Reset"
    },
    operationButtons: {
      add: "Add",
      delete: "Delete",
      hideSearch: "Hide Search",
      showSearch: "Show Search"
    },
    table: {
      id: "ID",
      thumbnail: "Thumbnail",
      noImage: "No Image",
      articleTitle: "Article Title",
      imageCount: "Image Count",
      likeCount: "Like Count",
      commentCount: "Comment Count",
      status: "Status",
      creator: "Creator",
      createTime: "Create Time",
      operations: "Operations",
      view: "View",
      edit: "Edit",
      delete: "Delete"
    },
    dialog: {
      addNews: "Add News",
      editNews: "Modify News",
      confirm: "Confirm",
      cancel: "Cancel",
      newsDetail: "News Detail",
      unknown: "Unknown",
      like: "Like",
      comment: "Comment",
      image: "Image",
      noThumbnail: "No Thumbnail",
      articleImages: "Article Images",
      images: "images",
      articleContent: "Article Content",
      noData: "No Data",
      failedToGetNewsList: "Failed to get news list",
      failedToGetNewsDetail: "Failed to get news detail",
      modifiedSuccessfully: "Modified successfully",
      addedSuccessfully: "Added successfully",
      confirmDelete: "Are you sure to delete news ID {0}?",
      deleteSuccess: "Deleted successfully",
      deleteFailed: "Failed to delete news",
      enable: "Enable",
      disable: "Disable",
      confirmStatusChange: "Confirm to {0} news?",
      statusChangeFailed: "Failed to change status",
      imagePreview: "Image Preview",
      close: "Close",
      imageLoadFailed: "Image load failed",
      warn: "Warning",
      addSuccess: "Success"
    },
    validation: {
      titleRequired: "Article title cannot be empty",
      contentRequired: "Article content cannot be empty",
      thumbnailRequired: "Please upload thumbnail"
    },
    form: {
      select: "Please select"
    }
}
