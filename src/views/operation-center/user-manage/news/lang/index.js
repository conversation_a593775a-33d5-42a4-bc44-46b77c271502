import zhLang from './zh.js'
import enLang from './en.js'

/**
 * 国际化管理器
 * 负责处理模块的国际化文本获取和注册
 */
export function createI18nManager(i18nInstance) {
  const moduleKey = 'news'
  
  // 本地国际化资源
  const localI18n = {
    zh: zhLang,
    en: enLang
  }
  
  // 注册国际化文件到全局i18n
  function registerI18n() {
    try {
      const currentLang = i18nInstance.locale
      
      // 将本地化文件合并到全局i18n中
      i18nInstance.mergeLocaleMessage(currentLang, {
        [moduleKey]: localI18n[currentLang] || localI18n.zh
      })
      
      // 确保所有语言都注册
      Object.keys(localI18n).forEach(lang => {
        i18nInstance.mergeLocaleMessage(lang, {
          [moduleKey]: localI18n[lang]
        })
      })
      
    } catch (error) {
      console.error(`❌ [${moduleKey}] 注册国际化文件失败:`, error)
    }
  }
  
  // 安全获取国际化文本
  function getText(key, args) {
    const fullKey = `${moduleKey}.${key}`
    try {
      // 尝试获取国际化文本
      let text = i18nInstance.t(fullKey)
      // 参数替换：支持 {0}、{1} 这种占位符
      if (args && Array.isArray(args)) {
        console.log("🚀 ~ file: index.js:50 ~ args:", args)
        args.forEach((val, idx) => {
          text = text.replace(new RegExp(`\\{${idx}\\}`, 'g'), val)
        })
      }
      // 如果返回的是key本身，说明没有找到翻译
      if (text === fullKey) {
        // 使用本地备用文本
        return getFallbackText(key)
      }
      return text
    } catch (error) {
      // 如果出错，使用备用文本
      console.warn(`获取国际化文本失败: ${fullKey}`, error)
      return getFallbackText(key)
    }
  }
  
  // 备用文本
  function getFallbackText(key) {
    const fallbackTexts = {
      'title': '数据管理',
      'description': '数据管理界面',
      'mainContent': '主要内容',
      'search.keyword': '关键词',
      'search.keywordPlaceholder': '请输入搜索关键词',
      'table.id': 'ID',
      'table.name': '名称',
      'table.createTime': '创建时间',
      'table.actions': '操作',
      'actions.add': '添加数据',
      'actions.edit': '编辑数据',
      'actions.confirmDelete': '确定要删除这条记录吗？'
    }
    return fallbackTexts[key] || key
  }
  
  // 立即注册国际化文件
  registerI18n()
  
  // 返回管理器接口
  return {
    getText,
    registerI18n,
    moduleKey,
    localI18n
  }
}

// 导出默认的国际化工厂函数
export default createI18nManager
