export default {
  title: 'Unbind Appeal Management',
  description: 'Manage user submitted vehicle unbind appeal requests',
  
  // Search Form
  search: {
    bluetooth: 'Bluetooth Address',
    bluetoothPlaceholder: 'Please enter bluetooth address',
    nickName: 'User Nickname',
    nickNamePlaceholder: 'Please enter user nickname',
    email: '<PERSON><PERSON>',
    emailPlaceholder: 'Please enter email address',
    phone: 'Phone Number',
    phonePlaceholder: 'Please enter phone number'
  },
  
  // Table Fields
  table: {
    id: 'ID',
    bluetooth: 'Bluetooth Address',
    nickName: 'Applicant User',
    originalName: 'Original Owner Nickname',
    originalId: 'Original Owner ID',
    email: 'Original Owner Email',
    phone: 'Original Owner Phone',
    buyImg: 'Purchase Proof',
    createTime: 'Appeal Time',
    status: 'Process Status',
    actions: 'Actions'
  },
  
  // Status
  status: {
    pending: 'Pending',
    processed: 'Processed'
  },
  
  // Actions
  actions: {
    viewImage: 'View Image',
    unbind: 'Unbind',
    viewDetails: 'View Details',
    confirmUnbind: 'Are you sure to unbind this vehicle?',
    unbindWarning: 'After unbinding, the original owner will lose control of the vehicle. This operation cannot be undone!'
  },
  
  // Form
  form: {
    bluetooth: 'Bluetooth Address',
    nickName: 'Applicant User Nickname',
    originalName: 'Original Owner Nickname',
    email: 'Original Owner Email',
    phone: 'Original Owner Phone',
    buyImg: 'Purchase Proof',
    createTime: 'Appeal Time',
    status: 'Process Status'
  },
  
  // Validation
  validation: {
    bluetoothRequired: 'Bluetooth address is required',
    nickNameRequired: 'User nickname is required',
    emailRequired: 'Email is required',
    phoneRequired: 'Phone number is required'
  },
  
  // Messages
  messages: {
    unbindSuccess: 'Unbind successfully',
    unbindError: 'Unbind failed',
    loadError: 'Data load failed',
    operationError: 'Operation failed',
    noImage: 'No image available'
  },
  
  // Common
  common: {
    search: 'Search',
    reset: 'Reset',
    confirm: 'Confirm',
    cancel: 'Cancel',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    all: 'All'
  }
}
