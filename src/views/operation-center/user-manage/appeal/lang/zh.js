export default {
  title: '解绑申述管理',
  description: '管理用户提交的车辆解绑申述请求',
  
  // 搜索表单
  search: {
    bluetooth: '蓝牙地址',
    bluetoothPlaceholder: '请输入蓝牙地址',
    nickName: '用户昵称',
    nickNamePlaceholder: '请输入用户昵称',
    email: '邮箱',
    emailPlaceholder: '请输入邮箱地址',
    phone: '手机号码',
    phonePlaceholder: '请输入手机号码'
  },
  
  // 表格字段
  table: {
    id: 'ID',
    bluetooth: '蓝牙地址',
    nickName: '申请用户',
    originalName: '原车主昵称',
    originalId: '原车主ID',
    email: '原车主邮箱',
    phone: '原车主电话',
    buyImg: '购买凭证',
    createTime: '申述时间',
    status: '处理状态',
    actions: '操作'
  },
  
  // 状态
  status: {
    pending: '未处理',
    processed: '已处理'
  },
  
  // 操作
  actions: {
    viewImage: '查看图片',
    unbind: '解除绑定',
    viewDetails: '查看详情',
    confirmUnbind: '确定要解除该车辆的绑定吗？',
    unbindWarning: '解除绑定后，原车主将失去该车辆的控制权，此操作不可撤销！'
  },
  
  // 表单
  form: {
    bluetooth: '蓝牙地址',
    nickName: '申请用户昵称',
    originalName: '原车主昵称',
    email: '原车主邮箱',
    phone: '原车主电话',
    buyImg: '购买凭证',
    createTime: '申述时间',
    status: '处理状态'
  },
  
  // 验证
  validation: {
    bluetoothRequired: '蓝牙地址不能为空',
    nickNameRequired: '用户昵称不能为空',
    emailRequired: '邮箱不能为空',
    phoneRequired: '手机号码不能为空'
  },
  
  // 消息
  messages: {
    unbindSuccess: '解除绑定成功',
    unbindError: '解除绑定失败',
    loadError: '数据加载失败',
    operationError: '操作失败',
    noImage: '暂无图片'
  },
  
  // 通用
  common: {
    search: '搜索',
    reset: '重置',
    confirm: '确定',
    cancel: '取消',
    loading: '加载中...',
    noData: '暂无数据',
    operation: '操作',
    status: '状态',
    all: '全部'
  }
}
