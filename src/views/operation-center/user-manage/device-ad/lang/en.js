export default {
  title: 'Device Ad Management',
  description: 'Manage device page advertisement content, including add, edit, delete and status control',
  
  // Search Form
  search: {
    code: 'Vehicle Code',
    codePlaceholder: 'Please enter vehicle code',
    content: 'Description',
    contentPlaceholder: 'Please enter ad description',
    status: 'Status',
    statusPlaceholder: 'Please select status'
  },
  
  // Table Fields
  table: {
    id: 'ID',
    code: 'Vehicle Code',
    content: 'Description',
    enContent: 'English Description',
    img: 'Device Image',
    link: 'Jump Link',
    sort: 'Sort',
    status: 'Status',
    actions: 'Actions'
  },
  
  // Status
  status: {
    normal: 'Normal',
    disabled: 'Disabled',
    all: 'All'
  },
  
  // Actions
  actions: {
    add: 'Add Advertisement',
    edit: 'Edit',
    delete: 'Delete',
    batchDelete: 'Batch Delete',
    enable: 'Enable',
    disable: 'Disable',
    batchEnable: 'Batch Enable',
    batchDisable: 'Batch Disable',
    viewImage: 'View Image',
    viewLink: 'Visit Link',
    confirmDelete: 'Are you sure to delete selected advertisements?',
    confirmEnable: 'Are you sure to enable selected advertisements?',
    confirmDisable: 'Are you sure to disable selected advertisements?',
    selectRows: 'Please select data to operate'
  },
  
  // Form
  form: {
    code: 'Vehicle Code',
    codePlaceholder: 'Please enter vehicle code',
    content: 'Description',
    contentPlaceholder: 'Please enter ad description',
    enContent: 'English Description',
    enContentPlaceholder: 'Please enter English description',
    img: 'Device Image',
    imgPlaceholder: 'Please upload device image',
    link: 'Jump Link',
    linkPlaceholder: 'Please enter jump link (optional)',
    sort: 'Sort',
    sortPlaceholder: 'Please enter sort value (smaller number comes first)',
    status: 'Status',
    statusOptions: {
      normal: 'Normal',
      disabled: 'Disabled'
    }
  },
  
  // Validation
  validation: {
    codeRequired: 'Vehicle code is required',
    contentRequired: 'Description is required',
    enContentRequired: 'English description is required',
    imgRequired: 'Device image is required',
    sortRequired: 'Sort is required',
    sortNumber: 'Sort must be a number',
    linkFormat: 'Please enter correct link format'
  },
  
  // Messages
  messages: {
    addSuccess: 'Add successfully',
    updateSuccess: 'Update successfully',
    deleteSuccess: 'Delete successfully',
    enableSuccess: 'Enable successfully',
    disableSuccess: 'Disable successfully',
    batchDeleteSuccess: 'Batch delete successfully',
    batchEnableSuccess: 'Batch enable successfully',
    batchDisableSuccess: 'Batch disable successfully',
    loadError: 'Data load failed',
    operationError: 'Operation failed',
    noImage: 'No image available',
    invalidLink: 'Invalid link'
  },
  
  // Dialog Titles
  dialog: {
    add: 'Add Device Advertisement',
    edit: 'Edit Device Advertisement',
    imagePreview: 'Image Preview'
  },
  
  // Common
  common: {
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    selectAll: 'Select All',
    pleaseSelect: 'Please Select'
  }
}
