<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item :label="$t('bike.bike.clientName')" prop="brandName">
        <el-input
          v-model.trim="queryParams.brandName"
          :placeholder="$t('bike.customer.nameInput')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("bike.customer.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("bike.customer.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :height="tableHeight()" :data="customerList">
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.advertise.title')"
        prop="name"
        align="center"
      />
      <el-table-column
        :label="$t('bike.bike.clientName')"
        prop="brandName"
        align="center"
      />
      <el-table-column
        :label="$t('bike.advertise.ValidityPeriod')"
        align="center"
        width="300"
      >
        <template slot-scope="{ row }">
          <span v-if="row.type">{{ $t("upgradeLog.longTermEffective") }}</span>
          <span v-else>
            {{ parseTime(row.startTime) }} — {{ parseTime(row.endTime) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.customer.activatedState')"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('bike.customer.createBy')"
        align="center"
        prop="createBy"
      >
        <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.customer.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)"></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.customer.operation')"
        class-name="small-padding fixed-width"
        align="center"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">
            {{ $t("bike.customer.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <Details ref="details" />
  </div>
</template>

<script>
import { bannerList, bannerAuth, bannerDetail } from "@/api/advertise";
import Details from "./details";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "BikeCustomer",
  mixins: [commonJs],
  components: {
    Details
  },
  data() {
    return {
      // 用户表格数据
      customerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        brandName: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      bannerList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.customerList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === 1
          ? this.$t("bike.customer.startUsing")
          : this.$t("bike.customer.blockUp");
      this.$confirm(
        this.$t("bike.customer.sure") + text,
        this.$t("bike.customer.warn"),
        {
          confirmButtonText: this.$t("bike.customer.confirm"),
          cancelButtonText: this.$t("bike.customer.cancel"),
          type: this.$t("bike.customer.warning")
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = {
            id: row.id,
            status: row.status
          };
          data.push(authData);

          bannerAuth(data).then(() => {
            this.msgSuccess(text + this.$t("bike.customer.succeed"));
            this.loading = false;
          });
        })
        .catch(() => {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    //详情
    handleDetail(row) {
      this.$refs.details.dialogVisible = true;
      if (row.id) {
        bannerDetail(row.id).then(res => {
          this.$refs.details.customerDetailData = res.data;
        });
      }
    }
  }
};
</script>
