<template>
  <!--用户管理详细信息弹窗-->
  <div class="customer_details">
    <el-dialog
      :visible.sync="dialogVisible"
      :title="$t('acc.riding.have.particulars')"
      width="700px"
      center
      :close-on-click-modal="false"
    >
      <el-descriptions direction="vertical" :column="5" border>
        <el-descriptions-item
          :label="$t('acc.msg.push.title')"
          label-class-name="text-green"
        >
          <span v-NoData="customerDetailData.name"></span>
        </el-descriptions-item>
        <el-descriptions-item
          :label="$t('base.exception.brandName')"
          label-class-name="text-green"
        >
          <span v-NoData="customerDetailData.brandName"></span>
        </el-descriptions-item>
        <el-descriptions-item
          :label="$t('base.exception.state')"
          label-class-name="text-green"
        >
          <el-tag>
            {{
              !customerDetailData.status
                ? $t("bike.brand.forbidden")
                : $t("btn.startUsing")
            }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          :label="$t('bike.computer.createBy')"
          label-class-name="text-green"
        >
          <span v-NoData="customerDetailData.createBy"></span>
        </el-descriptions-item>
        <el-descriptions-item
          :label="$t('base.exception.createTime')"
          label-class-name="text-green"
        >
          {{ parseTime(customerDetailData.createTime) || "- - -" }}
        </el-descriptions-item>
        <el-descriptions-item
          :label="$t('bike.advertise.ValidityPeriod')"
          :span="5"
          label-class-name="text-green"
        >
          <el-tag v-if="customerDetailData.type === 1">
            {{ $t("upgradeLog.longTermEffective") }}
          </el-tag>
          <span v-else>
            {{ parseTime(customerDetailData.startTime) }}
            <b class="margin-lr-xs">-</b>
            {{ parseTime(customerDetailData.endTime) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item
          :label="$t('upgradeLog.advertiseContent')"
          label-class-name="text-green"
        >
          <ul>
            <li
              v-for="item in customerDetailData.bannerImgs"
              :key="item.id"
              class="flex align-center bannerImgsList"
            >
              <preview-img :imgUrl="item.img" />
              <el-link class="margin-left-sm" :href="item.route" target="_black">
                {{ item.route }}
              </el-link>
            </li>
          </ul>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false, //用户管理详细信息弹窗
      customerDetailData: {
        type: "1"
      } //用户管理详细信息
    };
  }
};
</script>
<style lang="scss" scoped>
ul {
  padding: 0;
  max-height: 250px;
  overflow: hidden;
  overflow-x: auto;
}
.bannerImgsList {
  border: 1px solid #ddd;
  padding: 20px;
  margin-bottom: 5px;
  border-radius: 5px;
  .banner-img {
    width: 100px;
    height: 100px;
    border-radius: 10px;
  }

  .ipt {
    width: calc(100% - 100px);
    margin-left: 15px;
  }
}
</style>
