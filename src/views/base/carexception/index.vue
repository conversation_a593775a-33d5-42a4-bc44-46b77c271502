<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('base.carexception.modelId')" prop="modelId">
          <selectLoadMore
            v-model="queryParams.modelId"
            :data="listData.data"
            :page="listData.page"
            :hasMore="listData.more"
            :request="getListModel"
            dictLabel="name"
            dictValue="id"
            :placeholder="$t('base.carexception.modelIdInput')"
          />
        </el-form-item>
        <el-form-item :label="$t('base.carexception.code')" prop="code">
          <el-input
            v-model.trim="queryParams.code"
            :placeholder="$t('base.carexception.codeInput')"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('base.carexception.queryTime')">
          <el-date-picker
            v-model="dateRange"
            range-separator="-"
            :start-placeholder="$t('base.carexception.beginDate')"
            :end-placeholder="$t('base.carexception.endDate')"
            type="datetimerange"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("base.carexception.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("base.carexception.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="exceptionList">
      <el-table-column
        :label="$t('base.carexception.customerName')"
        type="index"
        prop="customerName"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('base.carexception.modelId')"
        prop="modelName"
      />
      <el-table-column
        align="center"
        :label="$t('base.carexception.code')"
        prop="code"
      >
        <template slot-scope="{ row }">
          E{{ row.code.slice(-2) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('base.carexception.nickName')"
        prop="nickName"
      />
      <el-table-column
        align="center"
        :label="$t('base.carexception.email')"
        prop="email"
      >
        <span slot-scope="{ row }" v-NoData="row.email" />
      </el-table-column>
      <el-table-column
        :label="$t('base.carexception.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <span slot-scope="{ row }" v-NoData="parseTime(row.createTime)" />
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listException } from "@/api/bike/carexception";
import { listModel } from "@/api/bike/model";

export default {
  name: "Carexception",
  data() {
    return {
      showSearch: true,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      exceptionList: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 车型名称
      listData: {
        data: [],
        page: 1,
        more: true
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        code: undefined,
        modelId: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getListModel({ page = 1, more = false, keyword = "" } = {}) {
      return new Promise((resolve, reject) => {
        listModel({ p: page, modelKey: keyword }).then(res => {
          const { list, total, pageNum, pageSize } = res.data;
          if (more) {
            this.listData.data = [...this.listData.data, ...list];
          } else {
            this.listData.data = list;
          }
          this.listData.more = pageNum * pageSize < total;
          this.listData.page = pageNum;
          resolve();
        });
      });
    },
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      let queryParams = JSON.parse(JSON.stringify(this.queryParams));
      if (/^e/i.test(queryParams.code)) {
        queryParams.code = queryParams.code.slice(1);
      }
      listException(this.addDateRange(queryParams, this.dateRange)).then(
        response => {
          this.exceptionList = response.data.list;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>
