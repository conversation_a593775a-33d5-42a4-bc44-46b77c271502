<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item :label="$t('base.exception.codeOrName')" prop="key">
        <el-input
          v-model="queryParams.key"
          :placeholder="$t('base.exception.codeOrNameInput')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("base.exception.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.exception.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:computer:add']"
        >
          {{ $t("base.exception.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['bike:bike:auth']"
        >
          {{ $t("base.exception.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['bike:bike:auth']"
        >
          {{ $t("base.exception.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      ref="multipleTableRef"
      row-key="id"
      v-loading="loading"
      :data="computerList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        type="index"
        align="center"
        :label="$t('acc.msg.msg.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.code')"
        prop="code"
        align="center"
      />
      <el-table-column
        :label="$t('base.exception.showCode')"
        prop="showCode"
        align="center"
      >
        <span slot-scope="{ row }" v-NoData="row.showCode"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.name')"
        prop="name"
        align="center"
      />
      <el-table-column
        :show-overflow-tooltip="true"
        :label="$t('base.exception.desc')"
        align="center"
        prop="desc"
      >
        <span slot-scope="scope" v-NoData="scope.row.desc"></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.model.protocolVersion')"
        prop="versionName"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.versionName"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.createBy')"
        align="center"
        prop="createBy"
      />
      <el-table-column
        :label="$t('base.exception.createTime')"
        align="center"
        sortable
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.exception.state')" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.solve')"
        align="center"
        prop="solve"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleLook(scope.row)">
            {{ $t("base.exception.lookOver") }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['bike:computer:edit']"
          >
            {{ $t("base.exception.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 文本回显 -->
    <el-show-text
      :isShow.sync="drawer"
      :title="$t('base.exception.processingScheme')"
    >
      <div class="box-scrollbar" v-if="textContent">
        <span v-html="textContent" class="scroll-cont"></span>
      </div>
    </el-show-text>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="1050px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="9">
            <el-form-item :label="$t('base.exception.code')" prop="code">
              <el-input
                v-model.trim="form.code"
                clearable
                :placeholder="$t('base.exception.codeInput')"
              />
            </el-form-item>
            <el-form-item
              :label="$t('base.exception.showCode')"
              prop="showCode"
            >
              <el-input
                v-model.trim="form.showCode"
                clearable
                :placeholder="$t('form.input') + $t('base.exception.showCode')"
              />
            </el-form-item>
            <el-form-item :label="$t('base.exception.name')" prop="name">
              <el-input
                v-model.trim="form.name"
                clearable
                :placeholder="$t('base.exception.nameInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('base.exception.nameEn')" prop="nameEn">
              <el-input
                v-model.trim="form.nameEn"
                clearable
                :placeholder="$t('base.exception.nameInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('base.exception.desc')" prop="desc">
              <el-input
                :autosize="{ minRows: 6 }"
                v-model="form.desc"
                type="textarea"
                :placeholder="$t('base.exception.descInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('base.exception.descEn')" prop="descEn">
              <el-input
                :autosize="{ minRows: 6 }"
                v-model="form.descEn"
                type="textarea"
                :placeholder="$t('base.exception.descInput')"
              />
            </el-form-item>
            <el-form-item :label="$t('bike.model.protocolVersion')">
              <el-select
                v-model="form.version"
                style="width: 100%"
                clearable
                :placeholder="
                  $t('bike.info.select') + $t('bike.model.protocolVersion')
                "
              >
                <el-option
                  v-for="item in versionList"
                  :key="item.dictCode"
                  :label="item.dictLabel"
                  :value="String(item.dictCode)"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item :label="$t('base.exception.solve')" prop="solve">
              <tinymce
                v-if="open"
                v-loading="isCodeLoading"
                height="428"
                v-model="form.solve"
                :placeholder="$t('base.exception.solveInput')"
              >
              </tinymce>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("base.exception._confirm") }}
        </el-button>
        <el-button @click="cancel">
          {{ $t("base.exception._cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listException,
  exceptionupdate,
  addExceptionu,
  exceptionAuth
} from "@/api/base/exception";
import tinymce from "@/components/Editor";
import { commonJs } from "@/mixinFile/common";
export default {
  mixins: [commonJs],
  data() {
    return {
      aFn: exceptionAuth,
      drawer: false,
      textContent: "",
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 50,
        key: "",
        modelId: ""
      },
      // 表单参数
      form: {},
      computerList: [],
      // 错误代码协议版本
      versionList: [],
      title: "",
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("base.exception.nameNotNull"),
            trigger: "blur"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("base.exception.codeNotNull"),
            trigger: "blur"
          }
        ],
        showCode: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("base.exception.showCode"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  components: {
    tinymce
  },
  watch: {
    open(isOpen) {
      if (isOpen) {
        this.getDicts("agreement_version").then(response => {
          this.versionList = response.data;
        });
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listException(this.queryParams).then(res => {
        const { list, total } = res.data;
        this.computerList = list;
        this.total = total;
        this.loading = false;
      });
    },
    handleLook(row) {
      this.drawer = true;
      this.textContent = row.solve;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.isBtnLoading = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCodeLoading = false;
      this.title = this.$t("base.exception.addCode");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("base.exception.updateCode");
      this.form = Object.assign({}, row);
      if(this.form.showCode === "" || this.form.showCode === null) {
        this.form.showCode = this.form.code;
      }
      this.isCodeLoading = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            exceptionupdate(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.exception.updateSucceed"));
                  this.isBtnLoading = false;
                  this.open = false;
                  this.getList();
                }
              })
              .catch(() => {
                this.isBtnLoading = false;
              });
          } else {
            addExceptionu(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.exception.addSucceed"));
                  this.isBtnLoading = false;
                  this.open = false;
                  this.getList();
                }
              })
              .catch(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
