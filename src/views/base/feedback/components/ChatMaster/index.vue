<template>
  <div class="chat-master">
    <div class="main">
      <div class="user-name">
        <div class="text-left">
          {{ $t("base.feedback.desc") }}：{{ detailData.desc }}
        </div>
        <div v-if="detailData.imgs" class="margin-bottom-xs">
            <PreviewImg
              class="margin-right-xs"
              v-for="(item, index) in arrImg(detailData.imgs)"
              :key="index"
              :imgUrl="item"
              isHover
            />
        </div>
      </div>
      <message :detailData="detailData"></message>
      <usertext @message="messageChange" @uploadImage="uploadImage"></usertext>
    </div>
  </div>
</template>

<script>
import card from "./components/card.vue";
import list from "./components/list.vue";
import message from "./components/message.vue";
import usertext from "./components/usertext.vue";
export default {
  props: ["detailData"],
  components: {
    card,
    list,
    message,
    usertext
  },
  methods: {
    messageChange(val) {
      this.$emit("message", val, 1);
    },
    arrImg(val) {
      val = val.split(",");
      val = val.filter(function(s) {
        return s && s.trim();
      });
      return val;
    },
    uploadImage() {
      this.$emit("uploadImage");
    }
  }
};
</script>

<style lang="scss" scoped>
.chat-master {
  margin: 0 auto;
  padding-bottom: 150px;
  overflow: hidden;
  border-radius: 6px;
  font-size: 14px;
  .sidebar,
  .main {
    height: 100%;
  }
  .sidebar {
    float: left;
    color: #f4f4f4;
    background-color: #f2f2f2;
    width: 300px;
    overflow-y: scroll;
  }
  .main {
    overflow: hidden;
    background-color: #fff;
    .user-name {
      position: relative;
      background: #fff;
      border-bottom: solid 1px #ddd;
      > div {
        text-align: left;
        padding: 0 20px;
        min-height: 60px;
        font-size: 14px;
        display: flex;
        align-items: center;
      }
      .close {
        position: absolute;
        right: 30px;
        top: 50%;
        font-size: 20px;
        transform: translate(0, -50%);
      }
    }
  }
}
</style>
