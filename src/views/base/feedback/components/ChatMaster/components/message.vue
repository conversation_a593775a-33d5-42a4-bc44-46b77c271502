<template>
  <div id="message" v-scroll-bottom="detailData.msgList">
    <ul class="message-im padding-0 margin-0">
      <li v-for="(item, cIndex) in detailData.msgList" :key="cIndex">
        <p class="time">
          <span>{{ parseTime(item.createTime) }}</span>
        </p>
        <div class="main" :class="{ self: item.senderType == 2 }">
          <el-avatar class="avatar">
            {{ item.senderType == 2 ? "平" : detailData.nickName.slice(0, 1) }}
          </el-avatar>
          <p
            style="margin: 0; height: 35px; line-height: 35px"
            v-if="item.senderType == 1"
          >
            {{ detailData.nickName }}
          </p>

          <p style="margin: 0; height: 35px; line-height: 35px" v-else>
            {{ $t("base.feedback.platform") }}
          </p>
          <p class="text" v-if="item.msgType == 1" v-html="item.msg"></p>
          <p class="text" v-if="item.msgType == 2">
            <preview-img
              width="80px"
              height="80px"
              v-for="(items, indexs) in arrImg(item.msg)"
              :key="indexs"
              :imgUrl="items"
            />
          </p>
        </div>
      </li>
      <div
        class="text-center margin-bottom"
        v-if="detailData.msgList && detailData.msgList.length"
      >
        <span style="color: #999">没有更多了</span>
      </div>
    </ul>
  </div>
</template>

<script>
import TextElement from "./text-element.vue";
let disabled = false;
export default {
  props: ["detailData"],
  name: "message",
  components: {
    TextElement,
  },
  data() {
    return {};
  },
  filters: {
    time(date) {
      return formatTime(date);
    },
    filterMsg(value) {
      if (value) {
        return value[0].MsgContent.Text;
      }
      return "";
    },
  },
  directives: {
    /*这个是vue的自定义指令,官方文档有详细说明*/
    // 发送消息后滚动到底部,这里无法使用原作者的方法，也未找到合理的方法解决，暂用setTimeout的方法模拟
    "scroll-bottom"(el) {
      setTimeout(function () {
        if (!disabled) {
          el.scrollTop += 9999;
        }
      }, 1);
      // document.getElementsByClassName("message-im")[0].scrollTop = 0;
    },
  },
  methods: {
    arrImg(val) {
      val = val.split(",");
      val = val.filter(function (s) {
        return s && s.trim();
      });
      return val;
    },
  },
};
</script>

<style lang="scss" scoped>
#message {
  padding: 15px;
  max-height: 500px;
  height: 500px;
  overflow-y: scroll;
  background: #fff;
  ul {
    list-style-type: none;
    li {
      margin-bottom: 15px;
    }
  }
  .time {
    text-align: center;
    margin: 7px 0;
    > span {
      display: inline-block;
      padding: 0 18px;
      font-size: 12px;
      color: rgba(175, 175, 175, 1);

      border-radius: 2px;
    }
  }
  .main {
    background: #fff;
    .avatar {
      float: left;
      margin: 0 10px 0 0;
      width: 40px;
      height: 40px;
      background: #1890ff;
    }
    .text {
      display: inline-flex;
      padding: 10px;
      max-width: 80%;
      background-color: #f2f2f2;
      border-radius: 8px;
      line-height: 30px;
      text-align: left;
    }
  }
  .self {
    text-align: right;
    .avatar {
      float: right;
      margin: 0 0 0 10px;
      width: 40px;
      height: 40px;
      background: #ddd;
    }
    .text {
      display: inline-flex;
      padding: 10px;
      max-width: 80%;
      background-color: #fff5e5;
      border-radius: 8px;
      line-height: 30px;
      text-align: left;
    }
  }
}
</style>
