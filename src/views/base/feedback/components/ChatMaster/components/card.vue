<template>
  <div id="card">
    <header>
      <el-input
        class="search"
        v-model.trim="searchValue"
        placeholder="按 Enter 搜索"
        @keyup.enter.native="queryUser"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
    </header>
  </div>
</template>

<script>
export default {
  name: "card",
  data() {
    return {
      searchValue: "",
    };
  },
  methods: {
    queryUser() {
      let { searchValue } = this;
      this.$store.commit("updateConversationList", searchValue);
    },
  },
};
</script>

<style lang="scss" >
#card {
  position: relative;
  height: 80px;
  padding: 20px;
  .avatar {
    width: 40px;
    height: 40px;
    vertical-align: middle; /*这个是图片和文字居中对齐*/
  }
  .name {
    display: inline-block;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 16px;
  }
  .search {
    background: #e2e2e2;
    color: #afafaf;
    input {
      background: #e2e2e2;
      color: #afafaf;
      &::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #afafaf;
      }
    }
    height: 34px;
    width: 260px;
    line-height: 34px;
  }
}
</style>
