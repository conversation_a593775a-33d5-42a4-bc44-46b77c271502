<template>
  <div id="uesrtext">
    <div class="send-header-bar">
      <div class="bar-item">
        <i
          class="el-icon-picture pointer"
          title="发图片"
          @click="handleSendImageClick"
        />
      </div>
      <emoji-picker @emoji="append" :search="search">
        <div
          class="pointer bar-item"
          slot="emoji-invoker"
          slot-scope="{ events: { click: clickEvent } }"
          @click.stop="clickEvent"
        >
          <svg
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 fill-current text-grey smile-icon"
          >
            <path d="M0 0h24v24H0z" fill="none" />
            <path
              d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"
            />
          </svg>
        </div>
        <div slot="emoji-picker" slot-scope="{ emojis, insert, display }">
          <div
            class="emoji-picker"
            :style="{ top: '-320px', left: display.x + 'px' }"
          >
            <div class="emoji-picker__search">
              <input type="text" v-model="search" v-focus />
            </div>
            <div>
              <div v-for="(emojiGroup, category) in emojis" :key="category">
                <h5>{{ category }}</h5>
                <div class="emojis">
                  <span
                    v-for="(emoji, emojiName) in emojiGroup"
                    :key="emojiName"
                    @click="insert(emoji)"
                    :title="emojiName"
                  >
                    {{ emoji }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </emoji-picker>
    </div>

    <textarea
      v-model="content"
      placeholder="按 Enter 发送"
      @keydown.enter.exact.prevent="addMessage"
      @keyup.ctrl.enter.prevent.exact="handleLine"
      style="resize:none"
    />

    <el-button
      size="mini"
      type="primary"
      class="send-btn"
      v-debounce-click="addMessage"
    >
      {{ $t("msg.send") }}
    </el-button>

    <input
      type="file"
      id="imagePicker"
      ref="imagePicker"
      accept=".jpg, .jpeg, .png, .gif"
      @change="sendImage"
      style="display: none"
    />
  </div>
</template>

<script>
import EmojiPicker from "vue-emoji-picker";

export default {
  name: "uesrtext",
  components: {
    EmojiPicker
  },
  data() {
    return {
      content: "",
      search: ""
    };
  },
  directives: {
    focus: {
      inserted(el) {
        el.focus();
      }
    }
  },
  methods: {
    append(emoji) {
      this.content += emoji;
    },
    addMessage() {
      if (this.content === "") {
        this.content = "";
        this.msgError(this.$t("base.feedback.pleaseEnterARply"));
        return;
      }
      this.$emit("message", this.content);
      this.content = "";
    },
    /**换行 */
    handleLine() {
      this.content += "\n";
    },
    /**发送图片 */
    handleSendImageClick() {
      this.$emit("uploadImage");
    },
    sendImage() {
      if (this.IM.otherAccount == "") {
        return;
      }
      const message = this.tim.createImageMessage({
        to: this.IM.otherAccount,
        conversationType: "C2C",
        payload: {
          file: document.getElementById("imagePicker") // 或者用event.target
        },
        onProgress: percent => {
          this.$set(message, "progress", percent); // 手动给message 实例加个响应式属性: progress
        }
      });

      this.tim
        .sendMessage(message)
        .then(res => {
          this.$store.commit("updateMessage", res.data.message);
          this.$refs.imagePicker.value = null;
        })
        .catch(imError => {
          this.msgError(imError.message);
        });
    }
  }
};
</script>

<style lang="scss" scope>
#uesrtext {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 150px;
  border-top: solid 1px #ddd;
  .send-btn {
    position: absolute;
    bottom: 10px;
    right: 20px;
  }
  > textarea {
    padding: 10px;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    font-size: 14px;
  }
  .send-header-bar {
    background: #fff;
    position: absolute;
    left: 0;
    right: 0;
    top: -35px;
    display: inline-flex;
    padding: 5px 10px;
    box-sizing: border-box;
    column-gap: 8px;

    .bar-item {
      width: 28px;
      height: 28px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.3s ease-in-out;

      &:hover {
        background: #ececec;
      }

      .smile-icon {
        width: 25px;
        height: 25px;
      }

      i {
        font-size: 25px;
      }
    }
  }
}

.emoji-picker {
  position: absolute;
  z-index: 1;
  font-family: Montserrat;
  border: 1px solid #ccc;
  width: 30rem;
  height: 20rem;
  overflow: scroll;
  padding: 1rem;
  box-sizing: border-box;
  border-radius: 0.5rem;
  background: #fff;
  box-shadow: 1px 1px 8px #c7dbe6;
}
.emoji-picker__search {
  display: flex;
  position: sticky;
  top: 0;
}
.emoji-picker__search > input {
  flex: 1;
  border-radius: 10rem;
  border: 1px solid #ccc;
  padding: 0.5rem 1rem;
  outline: none;
}
.emoji-picker h5 {
  margin-bottom: 0;
  color: #b1b1b1;
  text-transform: uppercase;
  font-size: 0.8rem;
  cursor: default;
}
.emoji-picker .emojis {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  grid-gap: 10px;
  justify-items: center;
  align-items: center;
  font-size: 18px;
}
.emoji-picker .emojis:after {
  content: "";
  flex: auto;
}
.emoji-picker .emojis span {
  padding: 0.2rem;
  cursor: pointer;
  border-radius: 5px;
}
.emoji-picker .emojis span:hover {
  background: #ececec;
  cursor: pointer;
}
</style>
