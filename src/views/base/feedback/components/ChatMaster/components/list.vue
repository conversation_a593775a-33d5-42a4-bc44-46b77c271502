<template>
  <div id="list">
    <ul>
      <li
        v-for="(item, index) in IM.conversationList"
        :class="{ active: item.otherAccount === IM.otherAccount }"
        v-on:click="changeCurrentSessionId(item.otherAccount)"
        :key="index"
      >
        <!--   :class="[item.id === currentSessionId ? 'active':'']" -->
        <div class="user-unRead">
          <img class="avatar" :src="item.icon" :alt="item.sssTitle" />
          <div class="unRead" v-if="item.unRead > 0">{{ item.unRead }}</div>
        </div>
        <div class="user">
          <div class="name">
            <span>{{ item.title }}</span>
            <span>{{ item.updateTime.slice(5, -3) }}</span>
          </div>
          <div class="msg">{{ item.msgBody | filterMsg }}</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  filters: {
    filterMsg(value) {
      value = JSON.parse(value);
      if (value) {
        if (value[0].MsgType == "TIMImageElem") {
          return "[图片]";
        }
        return value[0].MsgContent.Text;
      }
      return "";
    },
  },
  methods: {
    changeCurrentSessionId: function (id) {
      this.$store.commit("updateCurrentConversation", id);
    },
  },
};
</script>

<style lang="scss" scoped>
#list {
  ul {
    padding: 0;
    margin: 0;
    height: 605px;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 0;
    }
    li {
      padding: 0 20px;
      height: 80px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      &:hover {
        background-color: rgba(255, 255, 255, 1);
      }
      .user-unRead {
        position: relative;
        .unRead {
          position: absolute;
          top: -10px;
          right: 0;
          color: #fff;
          background: rgba(232, 55, 61, 1);
          padding: 1px 5px;
          min-width: 25px;
          min-height: 25px;
          border-radius: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
        }
      }
      .user {
        flex: 1;
        display: flex;
        flex-flow: column wrap;
        justify-content: center;
        padding-left: 17px;
        .name {
          color: #4c4948;
          font-size: 14px;
          display: flex;
          justify-content: space-between;
          word-break: break-all;
          margin-bottom: 6px;
          > span:last-child {
            color: #8b8b8b;
          }
        }
        .msg {
          color: #8b8b8b;
          font-size: 12px;
          margin-top: 6px;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
    li.active {
      /*注意这个是.不是冒号:*/
      background-color: #fff;
    }
    .avatar {
      border-radius: 100%;
      width: 50px;
      height: 50px;
      vertical-align: middle;
    }
  }
}
</style>
