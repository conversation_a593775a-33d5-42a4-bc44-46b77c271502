<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('base.feedback.type')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="
            $t('base.feedback.pleaseChoose') + $t('base.feedback.type')
          "
          clearable
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('acc.user.nickName')" prop="userInfo">
        <el-input
          v-model="queryParams.userInfo"
          :placeholder="$t('form.input') + $t('acc.user.nickName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.riding.have.email')" prop="email">
        <el-input
          v-model="queryParams.email"
          :placeholder="$t('form.input') + $t('acc.riding.have.email')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('base.feedback.LastReply')" prop="senderType">
        <el-select
          v-model="queryParams.senderType"
          :placeholder="
            $t('base.feedback.pleaseChoose') + $t('base.feedback.LastReply')
          "
          clearable
        >
          <el-option
            v-for="item in senderTypeOptions"
            :key="item.value"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          v-debounce-click="handleQuery"
        >
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          v-debounce-click="resetQuery"
        >
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :height="tableHeight()" :data="userList">
      <el-table-column
        :label="$t('base.feedback.feedbackType')"
        prop="type"
        align="center"
        :formatter="typeVal"
      />
      <el-table-column
        :label="$t('base.feedback.desc')"
        prop="desc"
        align="center"
        show-overflow-tooltip
      >
        <span slot-scope="scope" v-NoData="scope.row.desc"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.feedback.nickName')"
        align="center"
        prop="nickName"
      />
      <el-table-column
        :label="$t('base.feedback.email')"
        prop="email"
        align="center"
        show-overflow-tooltip
      >
        <span slot-scope="scope" v-NoData="scope.row.email" />
      </el-table-column>
      <el-table-column
        :label="$t('base.feedback.LastReply')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.senderType == 1 || scope.row.senderType == null"
          >
            {{ $t("base.feedback.user") }}
          </el-tag>
          <el-tag type="danger" v-if="scope.row.senderType == 2">
            {{ $t("base.feedback.system") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.exception.createTime')"
        align="center"
        prop="createTime"
        sortable
      >
        <template slot-scope="{ row }">
          {{ parseTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.feedback.operation')" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="lookDetail(scope.row)">
            {{ $t("base.feedback.lookOver") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :title="$t('base.feedback.respondFeedback')"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      center
      width="600px"
    >
      <el-form :model="form">
        <el-form-item v-if="form.msgType == 1">
          <el-input
            v-model="form.msg"
            type="textarea"
            :autosize="{ minRows: 10 }"
          />
        </el-form-item>
        <el-form-item label prop="msg" v-if="form.msgType == 2">
          <!-- <el-upload
            v-if="dialogFormVisible"
            ref="upload"
            class="upload-demo"
            :action="actionUrl"
            :headers="$store.state.user.upload.headers"
            multiple
            list-type="picture-card"
            :file-list="fileList"
            accept=".png,.jpeg,jpg"
          >
            <i class="el-icon-plus"></i>
          </el-upload> -->

          <el-upload-sortable
            v-model="form.msg"
            :imgW="80"
            :imgH="80"
            :max="12"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t("base.store.cancel") }}
        </el-button>
        <el-button type="primary" v-debounce-click="() => onSubmit(2)">
          {{ $t("base.store.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      fullscreen
      :close-on-click-modal="false"
      :title="$t('base.feedback.respondFeedback')"
      :visible.sync="outerVisible"
      custom-class="msg-style-dialog"
    >
      <ChatMaster
        :detailData="detailData"
        @message="onSubmit"
        @uploadImage="uploadImage"
      />
    </el-dialog>
  </div>
</template>

<script>
import { listFeedback, feedbackDetail, feedbackMsg } from "@/api/base/feedback";
import ChatMaster from "./components/ChatMaster";
import { VUE_BASE_UPLOAD } from "@/api/config";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    ChatMaster
  },
  data() {
    return {
      activeName: "1",
      // 遮罩层
      loading: true,
      senderTypeOptions: [
        {
          value: 1,
          key: this.$t("base.feedback.user")
        },
        {
          value: 2,
          key: this.$t("base.feedback.system")
        }
      ],
      typeOptions: [
        {
          value: 1,
          key: this.$t("base.feedback.complaint")
        },
        {
          value: 2,
          key: this.$t("base.feedback.repairs")
        },
        {
          value: 3,
          key: this.$t("base.feedback.errorCorrection")
        },
        {
          value: 4,
          key: this.$t("base.feedback.feedback")
        },
        {
          value: 5,
          key: this.$t("base.feedback.suggest")
        }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 50,
        type: undefined,
        userInfo: undefined,
        email: undefined,
        senderType: undefined
      },
      actionUrl: VUE_BASE_UPLOAD,
      userList: [],
      form: {
        msgType: "1",
        msg: "",
        feedbackId: ""
      },
      outerVisible: false,
      reverse: false,
      dialogFormVisible: false,
      detailData: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listFeedback(this.queryParams).then(res => {
        const { list, total } = res.data;
        this.userList = list;
        this.total = total;
        this.loading = false;
      });
    },
    onSubmit(val, type) {
      if (type == 1) {
        this.form.msg = val;
        this.form.msgType = 1;
      } else if (this.form.msgType == 2) {
        if (!this.form.msg) {
          this.msgError(this.$t("base.feedback.pleaseUploadPictures"));
          return;
        }
      }

      feedbackMsg(this.form).then(() => {
        this.dialogFormVisible = false;
        this.getList();
        this.lookDetail(this.recordRow);
      });
    },
    uploadImage() {
      this.form.msg = "";
      this.form.msgType = 2;
      this.dialogFormVisible = true;
    },
    lookDetail(row) {
      this.outerVisible = true;
      this.recordRow = row;
      feedbackDetail(row.id).then(response => {
        response.data.id = row.id;
        this.form.feedbackId = row.id;
        this.detailData = response.data;
      });
    },
    handleDetail(row) {
      this.$router.push({ name: "UserDetail", params: row });
    },
    typeVal(row, column, cellValue) {
      let index = this.typeOptions.findIndex(item => item.value == cellValue);
      return this.typeOptions[index].key;
    }
  }
};
</script>
<style lang="scss">
.msg-style-dialog {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
