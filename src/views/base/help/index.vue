<template>
  <div class="app-container help-page">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item
        :label="$t('base.help.title') + '/' + $t('base.help.desc')"
        prop="key"
      >
        <el-input
          v-model.trim="queryParams.key"
          :placeholder="
            $t('form.input') +
              $t('base.help.title') +
              '/' +
              $t('base.help.desc')
          "
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("base.help.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.help.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['bike:bike:add']"
        >
          {{ $t("base.help.newAdd") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="bikeList">
      <el-table-column
        type="index"
        :label="$t('base.help.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.help.customerName')"
        prop="customerName"
        align="center"
      />
      <el-table-column
        :label="$t('base.help.title')"
        prop="title"
        :show-overflow-tooltip="true"
        align="center"
      />
      <el-table-column
        :label="$t('base.help.desc')"
        prop="desc"
        :show-overflow-tooltip="true"
        align="center"
      />
      <el-table-column
        :label="$t('base.help.createBy')"
        prop="createBy"
        align="center"
      />
      <el-table-column
        :label="$t('base.help.createTime')"
        prop="createTime"
        align="center"
        sortable
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.help.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleLook(scope.row)">
            {{ $t("base.help.lookOver") }}
          </el-button>
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['bike:computer:edit']"
          >
            {{ $t("base.help.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 文本回显 -->
    <el-show-Text :isShow.sync="drawer" :title="textContent.title">
      <div class="box-scrollbar" v-if="textContent.content">
        <span v-html="textContent.content" class="scroll-cont"></span>
      </div>
    </el-show-Text>

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="1130px"
      center
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="10">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('base.help.title')" prop="title">
                  <el-input
                    v-model="form.title"
                    :placeholder="$t('form.input') + $t('base.help.title')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('base.help.desc')" prop="desc">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 4 }"
                    v-model="form.desc"
                    :placeholder="$t('base.help.descInput')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('discover.posted.imgs')" prop="cover">
                  <el-upload-sortable
                    v-model="form.cover"
                    :imgW="80"
                    :imgH="80"
                    :isLimit="1"
                    :max="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="14">
            <el-form-item :label="$t('base.help.content')" prop="content">
              <tinymce
                v-if="open"
                v-model="form.content"
                :height="400"
                :placeholder="$t('base.help.contentInput')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("base.help.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("base.help.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHelp, addHelp, editHelp } from "@/api/base/help";
import { listDictCustomer } from "@/api/base/dict";
import tinymce from "@/components/Editor";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: { tinymce },
  data() {
    return {
      drawer: false,
      textContent: {},
      // 用户表格数据
      bikeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      customerOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          {
            required: true,
            message: this.$t("base.help.titleNotNull"),
            trigger: "blur"
          }
        ],
        desc: [
          {
            required: true,
            message: this.$t("base.help.descNotNull"),
            trigger: "blur"
          }
        ],
        content: [
          {
            required: true,
            message: this.$t("base.help.contentNotNull"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    listDictCustomer().then(response => {
      this.customerOptions = response.data;
    });

    this.getList();
  },
  watch: {
    "form.content"(val) {
      if (val) {
        this.clearValidateItem("form", "content");
      }
    }
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      listHelp(this.queryParams)
        .then(response => {
          this.bikeList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleLook(row) {
      this.drawer = true;
      this.textContent = row;
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleUpdate(row) {
      this.reset();

      this.open = true;
      this.title = this.$t("base.help.updateHelp");
      this.form = Object.assign({}, row);
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("base.help.addInstrument");
    },
    handleDetail(row) {
      this.$router.push({
        name: "BikeDetail",
        query: {
          id: row.id
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            editHelp(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.help.updateSucceed"));
                  this.isBtnLoading = false;
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addHelp(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.help.addSucceed"));
                  this.isBtnLoading = false;
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
