<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="dialogVisble"
    width="680px"
  >
    <div>
      <div class="flex justify-center">
        <el-badge
          v-if="infoDetail"
          :value="
            infoDetail.status == 0
              ? $t('acc.user.detail.activated')
              : $t('acc.user.detail.nonactivated')
          "
          class="item"
          :type="infoDetail.status == 1 ? 'info' : 'primary'"
        >
          <el-avatar
            shape="square"
            style="width: 100px; height: 100px"
            :src="infoDetail.backImg"
          ></el-avatar>
        </el-badge>
      </div>
      
      <div
        class="text-center"
        style="margin-top: 10px; font-size: 18px; font-weight: 700"
      >
        {{ infoDetail.nickName }}
        <i
          v-if="infoDetail.gender != 3"
          :class="[
            infoDetail.gender == 1
              ? 'el-icon-male text-bule'
              : 'el-icon-female text-pink',
          ]"
        ></i>
      </div>
      <div class="flex justify-center" style="margin-top: 10px">
        <div style="margin-right: 30px">
          <el-tooltip
            class="item"
            effect="dark"
            :content="
              $t('acc.user.detail.sumMileage') +
              (infoDetail.sumMileage || 0) +
              'km'
            "
            placement="left-start"
          >
            <div>
              <span
                class="el-icon-bicycle"
                style="font-size: 20px; color: #1890ff"
              ></span>
              {{ infoDetail.sumMileage || 0 }}km
            </div>
          </el-tooltip>
        </div>
        <div>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('acc.user.detail.city') + infoDetail.city"
            placement="right-start"
          >
            <div>
              <span
                class="el-icon-office-building"
                style="font-size: 20px; color: #13ce66"
              ></span>

              {{ infoDetail.city }}
            </div>
          </el-tooltip>
        </div>
      </div>
      <div class="flex justify-center margin-top-xs">
        <el-tooltip
          class="item"
          effect="dark"
          :content="
            $t('acc.user.detail.registerTime') +
            parseTime(infoDetail.registerTime)
          "
          placement="left-start"
        >
          <div>
            <span
              class="el-icon-date"
              style="font-size: 20px; color: #666"
            ></span>
            {{ parseTime(infoDetail.registerTime) }}
          </div>
        </el-tooltip>
      </div>
      <el-table v-loading="loading" :data="bikeList" class="margin-top" border>
        <el-table-column
          :label="$t('acc.user.detail.carName')"
          align="center"
          prop="carName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t('acc.user.detail.bindTime')"
          align="center"
          prop="bindTime"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ parseTime(scope.row.bindTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { detailUser } from "@/api/acc/user";

export default {
  data() {
    return {
      dialogVisble: false,
      infoDetail: {},
      bikeList: [],
      loading: false,
    };
  },

  mounted() {},
  methods: {
    /** 查询品牌列表 */
    getList(id) {
      this.loading = true;
      detailUser(id).then((response) => {
        let { user, ridingData, bikeList } = response.data;
        this.infoDetail = Object.assign(user, ridingData);
        this.bikeList = bikeList;
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss" scope>
.form-line {
  .el-form-item {
    border-bottom: 1px solid #ddd;
    margin-bottom: 0;
  }
}
</style>
