<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form box-shadow">
      <h3 class="title">
        {{ $t("login.title") }}
        <span class="lang">
          <lang-select class="set-language" style="font-size: 20px" />
        </span>
      </h3>

      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" clearable
          :placeholder="$t('login.username')">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" auto-complete="off" clearable
          :placeholder="$t('login.password')" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input v-model="loginForm.code" auto-complete="off" :placeholder="$t('login.smsCode')" style="width: 63%"
          clearable @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px">
        {{ $t("login.remember") }}
      </el-checkbox>
      <el-form-item style="width: 100%">
        <el-button :loading="loading" size="medium" type="primary" style="width: 100%"
          @click.native.prevent="handleLogin">
          {{ $t("login.logIn") }}
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2025 AddMotor All Rights Reserved.</span>
    </div>

    <!-- 验证码 -->
    <el-dialog title="验证码" :visible.sync="isCodeDiag" width="470px" center append-to-body :close-on-click-modal="false">
      <el-form :model="codeForm" ref="codeForm" :inline="true" label-width="92px" label-position="left"
        @submit.native.prevent="submitCode">
        <el-form-item label="选择管理员" style="min-width: 150px" prop="phone" :rules="[
          {
            required: true,
            message: '请选择接收验证码用户',
            trigger: 'change'
          }
        ]">
          <el-radio-group v-model="codeForm.phone">
            <el-radio :label="item.dictValue" v-for="(item, index) in dataList" :key="index">{{ item.dictLabel
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="验证码" prop="code" :rules="[
          { required: true, message: '请输入验证码', trigger: 'change' }
        ]">
          <el-input size="small" clearable v-model="codeForm.code" placeholder="请输入验证码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" :disabled="getSmsCodeisWaiting" @click="getCodeVal">{{ codeValTitle
            }}</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isSubLogin" @click="submitCode">确 定</el-button>
        <el-button @click="isCodeDiag = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import LangSelect from "@/components/LangSelect";
import { getCodeImg, getSmsSend, getSmsUser, getSmsVerify } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { getToken, setToken, removeToken } from "@/utils/auth";

export default {
  name: "Login",
  components: { LangSelect },
  data() {
    return {
      isCodeDiag: false,
      isSubLogin: false,
      getSmsCodeisWaiting: false,
      codeValTitle: "获取验证码",
      dataList: [],
      codeForm: {
        code: "",
        phone: ""
      },
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: "blur",
            message: this.$t("login.usernameVoid")
          }
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            message: this.$t("login.passwordVoid")
          }
        ],
        code: [
          {
            required: true,
            trigger: "change",
            message: this.$t("login.smsCodeVoid")
          }
        ]
      },
      loading: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.loading = false;
              this.$router.push({ path: this.redirect || "/" });
            })
            .catch(err => {
              if (err === 40010) {
                this.resetForm("codeForm");
                this.isCodeDiag = true;
                this.getDictType();
              }
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
    // 获取验证码
    getCodeVal() {
      if (!this.codeForm.phone) {
        this.$message({ type: "error", message: "请选择接收验证码用户" });
        return;
      }
      let holdTime = 60;
      this.getSmsCodeisWaiting = true;
      this.codeValTitle = "重新获取(60)";
      this.codeTimeEr = setInterval(() => {
        if (holdTime <= 0) {
          this.getSmsCodeisWaiting = false;
          this.codeValTitle = "获取验证码";
          clearInterval(this.codeTimeEr);
          return;
        }
        this.codeValTitle = "重新获取(" + --holdTime + ")";
      }, 1000);
      const data = {
        username: this.loginForm.username,
        password: this.loginForm.password,
        phone: this.codeForm.phone
      };
      getSmsSend(data).then(() => {
        this.$message({
          showClose: true,
          message: "请向管理员要验证码",
          type: "success"
        });
      });
    },
    // 校验验证码
    submitCode() {
      this.$refs.codeForm.validate(valid => {
        if (valid) {
          this.isSubLogin = true;
          this.codeForm.username = this.loginForm.username;
          this.codeForm.password = this.loginForm.password;
          getSmsVerify(this.codeForm)
            .then(res => {
              const { iamKey, iamValue, token, username } = res.data;
              setToken(token);
              Cookies.set("iamKeys", iamKey, { expires: 30 });
              Cookies.set(iamKey, iamValue, { expires: 30 });
              this.$router.push({ path: this.redirect || "/" });
            })
            .finally(() => {
              this.isSubLogin = false;
            });
        }
      });
    },
    getDictType() {
      getSmsUser().then(res => {
        this.dataList = res.data;
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/image/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
  position: relative;

  .lang {
    position: absolute;
    right: 0;
  }
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
