<template>
  <div class="dashboard-editor-container">
    <el-row :gutter="15">
      <el-col :span="24" :xs="24" :sm="24" :lg="24">
        <panel-group :data="countData" />
      </el-col>
    </el-row>

    <el-row :gutter="15" style="margin-bottom: 15px;">
      <el-col :span="6" :xs="24" :sm="12" :lg="6">
        <div class="bg-white circle-list">
          <h4 class="text-center padding-top">
            {{ circleTitleList[0].title }}
          </h4>
          <circle-chart :circleData="circleData.carPercentOfBrand" />
        </div>
      </el-col>
      <el-col :span="6" :xs="24" :sm="12" :lg="6">
        <div class="bg-white circle-list">
          <h4 class="text-center padding-top">
            {{ circleTitleList[1].title }}
          </h4>
          <circle-chart :circleData="circleData.postedPercentOfBrand" />
        </div>
      </el-col>
      <el-col :span="6" :xs="24" :sm="12" :lg="6">
        <div class="bg-white circle-list">
          <h4 class="text-center padding-top">
            {{ circleTitleList[2].title }}
          </h4>
          <circle-chart :circleData="circleData.exceptionPercentOfBrand" />
        </div>
      </el-col>
      <el-col :span="6" :xs="24" :sm="12" :lg="6">
        <div class="bg-white circle-list">
          <h4 class="text-center padding-top">
            {{ circleTitleList[3].title }}
          </h4>
          <circle-chart :circleData="circleData.userPercentOfBrand" />
        </div>
      </el-col>
    </el-row>

    <!--用户/车辆增长统计表-->
    <el-row style="background: #fff; margin-bottom: 15px">
      <el-col :span="24" :xs="24" :sm="24" :lg="24">
        <div class="statistics_title">
          <h4>{{ $t("home.posted.growthStatistics") }}</h4>
          <div class="title_right">
            <span @click="switchDate(1)" :class="{ isActived: time == 1 }">
              {{ $t("home.posted.day") }}
            </span>
            <span @click="switchDate(2)" :class="{ isActived: time == 2 }">
              {{ $t("home.posted.week") }}
            </span>
            <span @click="switchDate(3)" :class="{ isActived: time == 3 }">
              {{ $t("home.posted.month") }}
            </span>
          </div>
        </div>
        <div class="tips">
          <span></span>
          <div>{{ $t("home.posted.addUserNum") }}</div>
          <span></span>
          <div>{{ $t("home.posted.addBikeNum") }}</div>
        </div>
        <posted :postedData="postedData" ref="posted" />
      </el-col>
    </el-row>
    <el-row class="layFlex">
      <!--文章量/评论-->
      <el-col
        :span="24"
        :xs="24"
        :sm="24"
        :md="24"
        :lg="24"
        style="background: #fff;"
      >
        <div class="statistics_title">
          <h4>{{ $t("home.essay") }}</h4>
          <div class="title_right">
            <span @click="switchDate4(1)" :class="{ isActived: time4 == 1 }">
              {{ $t("home.posted.day") }}
            </span>
            <span @click="switchDate4(2)" :class="{ isActived: time4 == 2 }">
              {{ $t("home.posted.week") }}
            </span>
            <span @click="switchDate4(3)" :class="{ isActived: time4 == 3 }">
              {{ $t("home.posted.month") }}
            </span>
          </div>
        </div>
        <div class="tips">
          <span></span>
          <div>{{ $t("home.posted.comment") }}</div>
          <span></span>
          <div>{{ $t("home.posted.essay") }}</div>
        </div>
        <essay :essayData="essayData" ref="essay" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import PanelGroup from "./dashboard/PanelGroup";
import CircleChart from "./dashboard/circle";
import posted from "./dashboard/posted";
import essay from "./dashboard/essay";
import {
  statisticsCount,
  postedCount,
  commentCount,
  proportion
} from "@/api/sys";

export default {
  name: "Index",
  components: {
    PanelGroup,
    posted,
    essay,
    CircleChart
  },
  data() {
    return {
      time: 1,
      time1: 1,
      time2: 1,
      time3: 1,
      time4: 1,
      type: "1",
      type_1: "1",
      type_2: "1",
      type_3: "1",
      type_4: "1",
      modelOptions: [],
      circleData: [],
      dictOptions: [],
      countData: null,
      postedData: null,
      milesData: null,
      ridingData: null,
      ridingTotalData: null,
      essayData: null,
      list: [],
      queryParams: {
        modelId: "",
        code: ""
      },
      circleTitleList: [
        {
          title: this.$t("home.ProportionVehicles")
        },
        {
          title: this.$t("home.InteractiveHeatRatio")
        },
        {
          title: this.$t("home.ErrorCodeRatio")
        },
        {
          title: this.$t("home.ProportionBrandUsers")
        }
      ]
    };
  },
  mounted() {
    this.init();
    this.growthData();
    this.getProportion();
    this.growthData4();
  },
  methods: {
    init() {
      statisticsCount().then(res => {
        this.countData = res.data;
      });
    },
    //用户/车辆增长统计
    growthData() {
      postedCount().then(res => {
        if (this.type == "1") {
          let dataOne = res.data.dataOne.slice(-30);
          let dataTwo = res.data.dataTwo.slice(-30);
          let data = { type1: "1", dataOne, dataTwo };
          this.postedData = data;
        } else if (this.type == "2") {
          let dataOne = this.divideWeek(res.data.dataOne.slice(-60));
          let dataTwo = this.divideWeek(res.data.dataTwo.slice(-60));
          let data = { type1: "2", dataOne, dataTwo };
          this.postedData = data;
        } else if (this.type == "3") {
          let dataOne = this.divideMonth(res.data.dataOne);
          let dataTwo = this.divideMonth(res.data.dataTwo);
          let data = { type1: "3", dataOne, dataTwo };
          this.postedData = data;
        }
      });
    },
    getProportion() {
      proportion().then(res => {
        this.circleData = res.data;
      });
    },
    growthData4() {
      commentCount().then(res => {
        if (this.type_4 == "1") {
          let dataOne = res.data.dataOne.slice(-30);
          let dataTwo = res.data.dataTwo.slice(-30);
          let data = { type1: "1", dataOne, dataTwo };
          this.essayData = data;
        } else if (this.type_4 == "2") {
          let dataOne = this.divideWeek(res.data.dataOne.slice(-60));
          let dataTwo = this.divideWeek(res.data.dataTwo.slice(-60));
          let data = { type1: "2", dataOne, dataTwo };
          this.essayData = data;
        } else if (this.type_4 == "3") {
          let dataOne = this.divideMonth(res.data.dataOne);
          let dataTwo = this.divideMonth(res.data.dataTwo);
          let data = { type1: "3", dataOne, dataTwo };
          this.essayData = data;
        }
      });
    },
    switchDate(index) {
      if (index === 1) {
        this.time = index;
        this.type = "1";
        this.growthData();
      } else if (index === 2) {
        this.time = index;
        this.type = "2";
        this.growthData();
      } else if (index === 3) {
        this.time = index;
        this.type = "3";
        this.growthData();
      } else {
        this.sort = index;
      }
    },
    switchDate1(index) {
      if (index === 1) {
        this.time1 = index;
        this.type_1 = "1";
        this.growthData1();
      } else if (index === 2) {
        this.time1 = index;
        this.type_1 = "2";
        this.growthData1();
      } else if (index === 3) {
        this.time1 = index;
        this.type_1 = "3";
        this.growthData1();
      } else {
        this.sort = index;
      }
    },
    switchDate2(index) {
      if (index === 1) {
        this.time2 = index;
        this.type_2 = "1";
        this.growthData2();
      } else if (index === 2) {
        this.time2 = index;
        this.type_2 = "2";
        this.growthData2();
      } else if (index === 3) {
        this.time2 = index;
        this.type_2 = "3";
        this.growthData2();
      } else {
        this.sort = index;
      }
    },
    switchDate3(index) {
      if (index === 1) {
        this.time3 = index;
        this.type_3 = "1";
        this.growthData3();
      } else if (index === 2) {
        this.time3 = index;
        this.type_3 = "2";
        this.growthData3();
      } else if (index === 3) {
        this.time3 = index;
        this.type_3 = "3";
        this.growthData3();
      } else {
        this.sort = index;
      }
    },
    switchDate4(index) {
      if (index === 1) {
        this.time4 = index;
        this.type_4 = "1";
        this.growthData4();
      } else if (index === 2) {
        this.time4 = index;
        this.type_4 = "2";
        this.growthData4();
      } else if (index === 3) {
        this.time4 = index;
        this.type_4 = "3";
        this.growthData4();
      } else {
        this.sort = index;
      }
    },

    //将数据以月份划分
    divideMonth(array) {
      let dataArr = [];
      array.map(mapItem => {
        let res = dataArr.some(item => {
          // 判断相同日期，有就添加到当前项
          // this.parseTime(mapItem.date, "{y}-{m}")
          if (item.date == this.parseTime(mapItem.date, "{y}-{m}")) {
            item.List.push(mapItem.num);
            return true;
          }
        });
        if (!res) {
          // 如果没有找到相同日期添加一个新对象
          dataArr.push({
            date: this.parseTime(mapItem.date, "{y}-{m}"),
            List: [mapItem.num],
            total: null
          });
        }
      });
      // 计算每个月的总量
      for (let i = 0; i < dataArr.length; i++) {
        dataArr[i].total = this.sum(dataArr[i].List);
      }
      return dataArr;
    },
    //将数据以周划分
    divideWeek(array) {
      let weekList = [];
      array.map(mapItem => {
        let res = weekList.some(item => {
          // 判断相同周，有就添加到当前项
          if (this.iSameWeek(item.date, mapItem.date)) {
            item.List.push(mapItem.num);
            return true;
          }
        });
        if (!res) {
          // 如果没有找到相同日期添加一个新对象
          weekList.push({
            date: this.parseTime(mapItem.date, "{y}-{m}-{d}"),
            List: [mapItem.num],
            total: null
          });
        }
      });
      // 计算每个周的总量
      for (let i = 0; i < weekList.length; i++) {
        weekList[i].total = this.sum(weekList[i].List);
      }
      return weekList;
    },
    //计算每天是当年的第几周
    calculationWeek(nowTime) {
      let time;
      let checkDate = new Date(nowTime);
      checkDate.setDate(checkDate.getDate() + 4 - (checkDate.getDay() || 7));
      time = checkDate.getTime();
      checkDate.setMonth(0);
      checkDate.setDate(1);
      return Math.floor(Math.round((time - checkDate) / 86400000) / 7) + 1;
    },
    // 相同周
    tMonday(dtm) {
      let dte = new Date(dtm);
      let day = dte.getDay();
      let dty = dte.getDate();
      if (day === 0) {
        day = 7;
      }
      dte.setDate(dty - day + 1);
      return dte.getFullYear() + "-" + dte.getMonth() + "-" + dte.getDate();
    },
    // 判断相同周
    iSameWeek(date1, date2) {
      let dt1 = new Date();
      dt1.setTime(date1);
      let dt2 = new Date();
      dt2.setTime(date2);
      let md1 = this.tMonday(dt1);
      let md2 = this.tMonday(dt2);
      return md1 === md2;
    },
    //计算总和
    sum(array) {
      let total = 0;
      for (let i = 0; i < array.length; i++) {
        total += parseInt(array[i]);
      }
      return total;
    }
  },
  destroyed() {
    clearInterval(this.timer);
  }
};
</script>

<style lang="scss" scoped>
@media screen and (min-width: 992px) {
  .layFlex {
    display: flex;
    justify-content: space-between;
  }
}
.tab-box {
  overflow-x: auto;
  white-space: nowrap;
}
.dashboard-editor-container {
  padding: 15px;
  background-color: rgb(240, 242, 245);
  position: relative;
  .flex-lw {
    display: flex;
  }
  .chart-wrapper {
    background: #fff;
    margin-bottom: 32px;
  }
  .riding-rank {
    position: absolute;
    top: 190px;
    left: 1150px;
    bottom: 0px;
    right: 30px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
.bg-white {
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
h4 {
  margin: 0 0 20px 0;
  font-weight: 600;
  color: #495057;
  letter-spacing: 1.5px;
}

.statistics_title {
  line-height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  margin-top: 20px;
  h4 {
    margin: 0;
  }

  span {
    display: block;
    width: 50px;
    border-radius: 5px;
  }
  .isActived {
    background: #1890ff;
    color: #fff;
  }
  .errorClass {
    background: #f6f6f6;
  }
  .title_right {
    display: flex;
    text-align: center;
    background: #f6f6f6;
    border-radius: 5px;
  }
  .title_right:hover {
    cursor: pointer;
  }
}
.tips {
  height: 30px;
  line-height: 30px;
  display: flex;
  align-items: center;
  padding-left: 30px;
  color: #495057;
  font-size: 14px;
  span {
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #1890ff;
  }
  span:nth-of-type(2) {
    background: #f98b3f;
    margin-left: 30px;
  }
  div {
    display: inline-block;
    padding-left: 10px;
  }
}
</style>
