<template>
  <div>
    <div class="el-dialog-body">
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="10">
            <!-- 标题 -->
            <el-col>
              <el-form-item :label="$t('acc.msg.msg.title')" prop="title">
                <el-input
                  v-model="form.title"
                  :readonly="bool"
                  clearable
                  :placeholder="
                    $t('acc.msg.msg.pleaseInput') + $t('acc.msg.msg.title')
                  "
                />
              </el-form-item>
            </el-col>
            <!-- 消息类型 -->
            <el-col>
              <el-form-item
                :label="$t('acc.msg.msg.messageType')"
                prop="msgType"
              >
                <el-select
                  v-model="form.msgType"
                  clearable
                  :readonly="bool"
                  :placeholder="
                    $t('form.select') + $t('acc.msg.msg.messageType')
                  "
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in statusOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 通知类型 -->
            <el-col v-if="form.msgType === 1">
              <el-form-item
                :label="$t('acc.msg.msg.notifyType')"
                prop="pushType"
              >
                <el-select
                  v-model="form.pushType"
                  clearable
                  :placeholder="
                    $t('form.select') + $t('acc.msg.msg.notifyType')
                  "
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in notifyOption"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 链接地址 -->
            <el-col v-if="form.msgType === 1 && form.pushType === 2">
              <el-form-item
                :label="$t('acc.msg.msg.linkAddress')"
                prop="pushData"
              >
                <el-input
                  v-model="form.pushData"
                  type="text"
                  clearable
                  :placeholder="
                    $t('acc.msg.msg.pleaseInput') +
                      $t('acc.msg.msg.linkAddress')
                  "
                />
              </el-form-item>
            </el-col>
            <!-- 消息范围 -->
            <el-col>
              <el-form-item
                :label="$t('acc.msg.msg.messageRange')"
                prop="scope"
              >
                <el-radio-group v-model="form.scope">
                  <el-radio :label="1">{{ $t("acc.msg.msg.oneMan") }}</el-radio>
                  <el-radio :label="0">{{ $t("acc.msg.msg.all") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <!-- 选择用户 -->
            <el-col v-if="form.scope === 1">
              <el-form-item
                :label="$t('acc.msg.msg.selectUser')"
                prop="userIds"
              >
                <div class="flex">
                  <el-select
                    v-model="form.userIds"
                    :placeholder="$t('form.select')"
                    multiple
                    clearable
                    style="width: 100%;"
                    @remove-tag="onRemoveTag"
                  >
                    <el-option
                      v-for="item in pushUserList"
                      :key="item.id"
                      :label="item.nickName"
                      :value="item.id"
                    />
                  </el-select>
                  <el-button
                    class="margin-left-xs"
                    type="primary"
                    @click="handleChooseUser"
                  >
                    {{ $t("acc.msg.msg.choose") }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <!-- 消息体 -->
            <el-col>
              <el-form-item :label="$t('acc.msg.msg.messageBody')" prop="msg">
                <el-input
                  v-model="form.msg"
                  type="textarea"
                  :readonly="bool"
                  :placeholder="
                    $t('acc.msg.msg.pleaseInput') +
                      $t('acc.msg.msg.messageBody')
                  "
                />
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="14">
            <!-- 描述 -->
            <el-col>
              <el-form-item :label="$t('acc.msg.msg.describe')" prop="desc">
                <tinymce
                  v-model="form.desc"
                  height="450"
                  :placeholder="
                    $t('acc.msg.msg.pleaseInput') + $t('acc.msg.msg.describe')
                  "
                />
              </el-form-item>
            </el-col>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="text-center margin-bottom-sm margin-top-sm">
      <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
        {{ $t("acc.msg.msg._confirm") }}
      </el-button>
      <el-button @click="$emit('close')">
        {{ $t("acc.msg.msg._cancel") }}
      </el-button>
    </div>

    <el-dialog
      v-bind="dialogOption"
      :view.sync="dialogOption.view"
      :visible.sync="dialogOption.show"
      :close-on-click-modal="false"
      append-to-body
      @close="closeDynamicDialog"
      class="el-dialog-dynamic"
      center
    >
      <component
        :is="dialogOption.view"
        :pushUserList="pushUserList"
        @selectUser="handleSelectUser"
        @close="closeDynamicDialog"
      >
      </component>
    </el-dialog>
  </div>
</template>

<script>
import tinymce from "@/components/Editor";
import PeopleListDialog from "./people-list-dialog";
import { addMsg, editMsg, msgDetail } from "@/api/acc/msg";
import { checkValidUrl } from "@/utils/validate";

export default {
  props: {
    rowData: {
      type: Object,
      default: {}
    },
    bool: Boolean
  },
  components: {
    tinymce,
    PeopleListDialog
  },
  created() {
    // 编辑模式
    if (this.rowData.id) {
      this.form = {
        title: this.rowData.title,
        msg: this.rowData.msg,
        msgType: this.rowData.msgType,
        pushType: this.rowData.pushType,
        pushData: this.rowData.pushData,
        scope: this.rowData.scope,
        desc: this.rowData.desc
      };
      this.getMsgDetail();
    }
  },
  data() {
    return {
      isBtnLoading: false,
      form: {
        scope: 0,
        userIds: []
      },
      rules: {
        title: [
          {
            required: true,
            message: this.$t("acc.msg.msg.titleNotBeNull"),
            trigger: "blur"
          }
        ],
        msg: [
          {
            required: true,
            message: this.$t("acc.msg.msg.messageBodyNotBeNull"),
            trigger: "blur"
          }
        ],
        msgType: [
          {
            required: true,
            message: this.$t("acc.msg.msg.messageTypeNotBeNull"),
            trigger: "change"
          }
        ],
        pushType: [
          {
            required: true,
            message: this.$t("acc.msg.msg.pleaseChooseNotifyType"),
            trigger: "change"
          }
        ],
        scope: [
          {
            required: true,
            message: this.$t("acc.msg.msg.pleaseChoosePushRange"),
            trigger: "change"
          }
        ],
        linkAddress: [
          {
            required: true,
            message: this.$t("acc.msg.msg.linkAddressNotBeNull"),
            trigger: "blur"
          }
        ],
        desc: [
          {
            required: false,
            message: this.$t("acc.msg.msg.describeNotBeNull"),
            trigger: "blur"
          }
        ],
        userIds: [
          {
            type: "array",
            required: true,
            message: this.$t("acc.msg.msg.pleaseChooseUser"),
            trigger: "change"
          }
        ],
        pushData: [
          {
            required: false,
            validator: checkValidUrl,
            trigger: "blur"
          }
        ]
      },
      // 状态数据字典
      statusOptions: [
        {
          dictValue: 0,
          dictLabel: this.$t("acc.msg.msg.system")
        },
        {
          dictValue: 1,
          dictLabel: this.$t("acc.msg.msg.push")
        }
      ],
      // 通知类型字典
      notifyOption: [
        {
          dictValue: 1,
          dictLabel: this.$t("acc.msg.msg.notifyOption.messageListPage")
        },
        {
          dictValue: 2,
          dictLabel: this.$t("acc.msg.msg.notifyOption.H5Page")
        },
        {
          dictValue: 3,
          dictLabel: this.$t("acc.msg.msg.notifyOption.homePage")
        },
        {
          dictValue: 4,
          dictLabel: this.$t("acc.msg.msg.notifyOption.discoveryPage")
        }
      ],
      // 选择推送的用户
      pushUserList: [],
      dialogOption: {
        width: "",
        title: "",
        show: false,
        view: ""
      }
    };
  },
  methods: {
    // 详情
    getMsgDetail() {
      msgDetail(this.rowData.id).then(res => {
        this.pushUserList = res.data.userIds;
        res.data.users && (this.form.userIds = res.data.users.split(","));
      });
    },
    /**
     * 点击用户选择
     */
    handleChooseUser() {
      this.showDynamicDialog(
        "PeopleListDialog",
        this.$t("acc.msg.msg.selectUser"),
        "800px"
      );
    },
    /**
     * 用户选择回调
     */
    handleSelectUser(listData) {
      this.pushUserList = listData;
      this.form.userIds = this.pushUserList.map(item => item.id);
    },
    /**
     * 删除选择的用户
     */
    onRemoveTag(tagId) {
      const deleteIndex = this.pushUserList.findIndex(
        item => item.id === tagId
      );

      this.pushUserList.splice(deleteIndex, 1);
    },
    /**
     * 保存
     */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          let params = JSON.parse(JSON.stringify(this.form));

          // 编辑模式
          if (this.rowData.id) {
            params.id = this.rowData.id;

            params.userIds && (params.userIds = params.userIds.join());

            editMsg(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("acc.msg.msg.updateSucceed"));
                  this.$emit("refresh");
                  this.$emit("close");
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addMsg(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("acc.msg.msg.addSucceed"));
                  this.$emit("refresh");
                  this.$emit("close");
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    },
    /**
     * 删除选择的用户
     */
    handleDeleteUser(index) {
      this.pushUserList.splice(index, 1);
    },
    showDynamicDialog(view, title, width = "1200px") {
      this.dialogOption.show = true;
      this.dialogOption.view = view;
      this.dialogOption.title = title;
      this.dialogOption.width = width;
    },
    closeDynamicDialog() {
      this.dialogOption.show = false;
      this.dialogOption.view = null;
    }
  }
};
</script>
