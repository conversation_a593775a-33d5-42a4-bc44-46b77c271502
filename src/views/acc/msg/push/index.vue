<template>
  <div style="padding:40px;width:750px">
    <el-form ref="form" :model="form" :rules="rules" label-width="150px">
      <el-form-item :label="$t('acc.msg.push.title')" prop="title">
        <el-input v-model="form.title" :placeholder="$t('acc.msg.push.pleaseInputTitle')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('acc.msg.push.describe')" prop="desc">
        <el-input type="textarea" v-model="form.desc" :placeholder="$t('acc.msg.push.pleaseInputDescribe')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('acc.msg.push.pushData')" prop="pushData">
        <el-input type="textarea" v-model="form.pushData " :placeholder="$t('acc.msg.push.pleaseInputPushData')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('acc.msg.push.whetherToNotifyEveryone')" prop="all">
        <el-radio-group v-model="form.all">
          <el-radio label="1">{{$t('acc.msg.push.yes')}}</el-radio>
          <el-radio label="0">{{$t('acc.msg.push.no')}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">{{$t('acc.msg.push.immediatelyCreate')}}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{$t('acc.msg.push.reset')}}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { pushAcc } from "@/api/acc/msg";
export default {
  data() {
    return {
      form: {},
      rules: {
        title: [{ required: true, message: $t('acc.msg.push.titleNotBeNull'), trigger: "blur" }],
        desc: [{ required: true, message: $t('acc.msg.push.describeNotBeNull'), trigger: "blur" }],
        pushData: [
          { required: true, message: $t('acc.msg.push.pushDatabeNotBeNull'), trigger: "blur" },
        ],
        all: [
          { required: true, message: $t('acc.msg.push.pleaseChooseToNotifyEveryone'), trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    onSubmit() {
      let { form, fileList, msgUrl } = this;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          var date = new Date();

          var year = date.getFullYear();

          var month = date.getMonth() + 1;
          var day = date.getDate();
          var hour = date.getHours();
          var minute = date.getMinutes();
          var second = date.getSeconds();

          this.form.createTime =
            year +
            "-" +
            month +
            "-" +
            day +
            " " +
            hour +
            ":" +
            minute +
            ":" +
            second;
          pushAcc(this.form).then((response) => {
            this.msgSuccess($t('acc.msg.push.sendSuccess'));
          });
        }
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("form");
    },
  },
};
</script>

<style scoped>
</style>
