<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.riding.not.nickName')" prop="nikeName">
        <el-input
          v-model="queryParams.nikeName"
          clearable
          :placeholder="$t('form.input') + $t('acc.riding.not.nickName')"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.riding.not.cyclingTime')">
        <el-date-picker
          v-model="dateRange"
          clearable
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('acc.riding.not.beginDate')"
          :end-placeholder="$t('acc.riding.not.endDate')"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("acc.riding.not.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("acc.riding.not.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :height="tableHeight()" :data="trackNotList">
      <el-table-column
        type="index"
        align="center"
        width="65"
        :label="$t('acc.riding.not.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.not.nickName')"
        prop="nickName"
        align="center"
      >
        <template v-slot="{ row }">
          <el-link
            @click="
              toJumpPagePath({
                path: '/bike/userCenter/user',
                query: {
                  nickName: row.nickName
                }
              })
            "
          >
            {{ row.nickName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.not.ridingTime')"
        prop="ridingTime"
        sortable
        align="center"
      >
        <template v-slot="{ row }">
          {{ formattedDurationTime(row.ridingTime) }}
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('acc.riding.have.calorie')"
        prop="mileage"
        sortable
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.calorie }}
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('acc.riding.not.mileage')"
        prop="mileage"
        sortable
        align="center"
      >
        <template slot-scope="scope">
          {{ (scope.row.mileage * 0.1).toFixed(1) }}km
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        property="maxSpeed"
        sortable
        :label="$t('acc.riding.not.maxSpeed')"
      >
        <template slot-scope="scope">
          {{ (scope.row.maxSpeed * 0.1).toFixed(1) }}km/h
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.not.syncTime')"
        align="center"
        sortable
        prop="syncTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.syncTime) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { ridingTrackNotList } from "@/api/acc/riding";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      trackNotList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        nikeName: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      ridingTrackNotList(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then(res => {
        const { list, total } = res.data;
        this.trackNotList = list;
        this.total = total;
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
