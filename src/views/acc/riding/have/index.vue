<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item :label="$t('acc.riding.not.nickName')" prop="nikeName">
        <el-input
          v-model.trim="queryParams.nikeName"
          clearable
          :placeholder="$t('form.input') + $t('acc.riding.not.nickName')"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.riding.have.cyclingTime')">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('acc.riding.have.beginDate')"
          :end-placeholder="$t('acc.riding.have.endDate')"
          :default-time="['00:00:00', '23:59:59']"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("acc.riding.have.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("acc.riding.have.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :height="tableHeight()" :data="trackNotList">
      <el-table-column
        type="index"
        width="65"
        :label="$t('acc.riding.have.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.have.nickName')"
        prop="nickName"
        align="center"
      >
        <template v-slot="{ row }">
          <el-link
            @click="
              toJumpPagePath({
                path: '/bike/userCenter/user',
                query: {
                  nickName: row.nickName
                }
              })
            "
          >
            {{ row.nickName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.have.ridingTime')"
        prop="ridingTime"
        sortable
        align="center"
      />
      <el-table-column
        :label="$t('acc.riding.have.calorie')"
        prop="mileage"
        sortable
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.calorie }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.have.mileage')"
        prop="mileage"
        sortable
        align="center"
      >
        <template slot-scope="scope">
          {{ (scope.row.mileage * 1).toFixed(2) }}km
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.have.syncTime')"
        align="center"
        sortable
        prop="syncTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.syncTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.riding.have.operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">
            {{ $t("acc.riding.have.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 详情 -->
    <el-dialog
      :title="$t('acc.riding.have.particulars')"
      :close-on-click-modal="false"
      :visible.sync="dialogTableVisible"
      width="900px"
      center
    >
      <el-descriptions direction="vertical" :column="4" border>
        <el-descriptions-item :label="$t('acc.riding.have.altitudeHeight')">
          {{ gridData.altitudeHeight }} m
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.altitudeUp')">
          {{ gridData.altitudeUp }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.ridingTime')">
          {{ gridData.ridingTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.startTime')">
          {{ parseTime(gridData.startTime) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.calorie')">
          {{ gridData.calorie }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.carbonEmission')">
          {{ gridData.carbonEmission }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.mileage')">
          {{ (gridData.mileage * 1).toFixed(2) }} km
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.maxSpeed')">
          {{ gridData.maxSpeed }} km
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.riding.have.economyTree')">
          {{ gridData.economyTree }} kg
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { ridingTracktList, rackDetail } from "@/api/acc/riding";
import { commonJs } from "@/mixinFile/common";
import { parseTime } from "../../../../utils/ruoyi";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      trackNotList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 50,
        nikeName: undefined
      },
      gridData: {},
      dateRange: [],
      dialogTableVisible: false
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      ridingTracktList(this.addDateRange(this.queryParams, this.dateRange))
        .then(res => {
          const { list, total } = res.data;
          this.trackNotList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDetail(row) {
      this.dialogTableVisible = true;
      rackDetail(row.id).then(response => {
        this.gridData = response.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.row-col-style-bottom {
  .el-col {
    margin-bottom: 10px;
    span {
      display: inline-block;
      min-width: 100px;
    }
  }
}
</style>
