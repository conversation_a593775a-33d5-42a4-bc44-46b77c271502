<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="`${$t('acc.log.email')}`" prop="email">
        <el-input
          v-model.trim="queryParams.email"
          clearable
          :placeholder="
            `${$t('acc.msg.msg.pleaseInput')}${$t('acc.log.email')}`
          "
          @keyup.enter.native="handleQuery"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('acc.log.equipmentType')" prop="type">
        <el-select
          v-model="queryParams.type"
          clearable
          :placeholder="$t('form.select') + $t('acc.log.equipmentType')"
        >
          <el-option
            v-for="dict in typeList"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("bike.bike.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("bike.bike.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :height="tableHeight()" :data="bikeList">
      <el-table-column
        type="index"
        align="center"
        :label="$t('acc.msg.msg.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="nickName"
        align="center"
        :label="$t('acc.log.nickName')"
      />
      <el-table-column
        prop="email"
        align="center"
        :label="$t('acc.riding.have.email')"
      />
      <el-table-column
        prop="sn"
        align="center"
        :label="$t('acc.log.equipmentSn')"
      />
      <el-table-column
        align="center"
        prop="type"
        :label="$t('acc.log.equipmentType')"
      >
        <template slot-scope="{ row }">
          <el-tag v-show="row.type === 1">
            {{ $t("acc.log.smartCarLock") }}
          </el-tag>
          <el-tag v-show="row.type === 2">
            {{ $t("acc.log.electronicShif") }}
          </el-tag>
          <el-tag v-show="row.type === 3">
            {{ $t("acc.log.smartTurnSignal") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="modelName"
        :label="$t('acc.log.deviceModel')"
      />
      <el-table-column
        align="center"
        prop="registerTime"
        sortable
        :label="$t('bike.bike.bindTime')"
      >
        <span
          slot-scope="scope"
          v-NoData="parseTime(scope.row.bindTime)"
        ></span>
      </el-table-column>
      <el-table-column
        :label="$t('bike.bike.operation')"
        class-name="small-padding fixed-width"
        header-align="center"
      >
        <template slot-scope="scope">
          <div class="flex flex-wrap justify-around padding-lr-xs">
            <el-button type="text" @click="handleDetail(scope.row)">
              {{ $t("bike.bike.particulars") }}
            </el-button>
            <el-button
              v-show="scope.row.bikeId"
              type="text"
              class="text-red"
              @click="handleUntie(scope.row)"
            >
              {{ $t("bike.bike.untie") }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :title="$t('bike.bike.particulars')"
      :visible.sync="dialogTableVisible"
      :close-on-click-modal="false"
      center
    >
      <el-card shadow="nerver">
        <el-form
          class="form-content-detail"
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-position="left"
        >
          <el-form-item :label="$t('bike.bike.nickName') + ':'" prop="modelKey">
            {{ detailData.nickName }}
          </el-form-item>
          <el-form-item :label="$t('acc.riding.have.email') + ':'" prop="email">
            {{ detailData.email }}
          </el-form-item>
          <el-form-item :label="$t('acc.log.equipmentSn') + ':'" prop="sn">
            {{ detailData.sn }}
          </el-form-item>
          <el-form-item :label="$t('acc.log.equipmentType') + ':'" prop="type">
            <el-tag v-show="detailData.type === 1">
              {{ $t("acc.log.smartCarLock") }}
            </el-tag>
            <el-tag v-show="detailData.type === 2">
              {{ $t("acc.log.electronicShif") }}
            </el-tag>
            <el-tag v-show="detailData.type === 3">
              {{ $t("acc.log.smartTurnSignal") }}
            </el-tag>
          </el-form-item>
          <el-form-item
            :label="$t('acc.log.deviceModel') + ':'"
            prop="modelKey"
          >
            {{ detailData.modelName }}
          </el-form-item>
          <el-form-item :label="$t('bike.bike.bindTime') + ':'" prop="bindTime">
            {{ parseTime(detailData.bindTime) }}
          </el-form-item>
          <el-form-item
            :label="$t('bike.bike.bluetooth') + ':'"
            prop="modelKey"
          >
            {{ detailData.bluetooth }}
          </el-form-item>
        </el-form>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import { equipList, equipDelBind, equipInfo } from "@/api/acc/log";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      bikeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      typeList: [
        {
          dictLabel: this.$t("acc.log.smartCarLock"),
          dictValue: 1
        },
        {
          dictLabel: this.$t("acc.log.electronicShif"),
          dictValue: 2
        },
        {
          dictLabel: this.$t("acc.log.smartTurnSignal"),
          dictValue: 3
        }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        email: "",
        type: ""
      },
      // 表单校验
      detailData: {},
      dialogTableVisible: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      equipList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.bikeList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDetail(row) {
      this.dialogTableVisible = true;
      equipInfo({ id: row.bikeId, type: row.type }).then(res => {
        this.detailData = Object.assign(row, res.data);
      });
    },
    handleUntie({ id, bikeId, type }) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      })
        .then(() => {
          equipDelBind({ id, bikeId, type }).then(() => {
            this.msgSuccess(
              this.$t("bike.bike.untie") + this.$t("acc.user.succeed")
            );
            this.getList();
          });
        })
        .catch();
    }
  }
};
</script>
