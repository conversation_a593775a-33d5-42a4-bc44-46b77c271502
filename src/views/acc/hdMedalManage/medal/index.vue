<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.medal.userKey')" prop="userKey">
        <el-input
          v-model="queryParams.userKey"
          :placeholder="$t('acc.medal.importNameOrCodingOrSynopsis')"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.medal.timeOfRelease')">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('acc.medal.beginTime')"
          :end-placeholder="$t('acc.medal.endTime')"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          v-debounce-click="handleQuery"
        >
          {{ $t("acc.medal.search") }}
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          v-debounce-click="resetQuery"
        >
          {{ $t("acc.medal.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8"></el-row>

    <el-table
      v-loading="loading"
      :data="accMedalList"
      :height="tableHeight()"
    >
      <el-table-column
        type="index"
        :label="$t('acc.medal.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.medal.email')"
        align="center"
        prop="email"
      />
      <el-table-column
        :label="$t('acc.medal.nickName')"
        align="center"
        prop="nickName"
      />
      <el-table-column
        :label="$t('acc.medal.name')"
        align="center"
        prop="name"
      />
      <el-table-column
        :label="$t('acc.medal.timeOfRelease')"
        align="center"
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.medal.provideCause')"
        align="center"
        prop="createEvent"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('base.medal.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleCancel(scope.row.id)"
          >
            {{ $t("base.medal.cancelSend") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listAccMedal, listAccDel } from "@/api/acc/medal";

export default {
  name: "BikeCustomer",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      accMedalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 30,
        userKey: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listAccMedal(this.addDateRange(this.queryParams, this.dateRange))
        .then(response => {
          this.accMedalList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleCancel(row) {
      this.$confirm(
        this.$t("base.carexception.sure") +
          this.$t("base.medal.cancelSend") +
          "?",
        {
          confirmButtonText: this.$t("bike.computer.confirm"),
          cancelButtonText: this.$t("bike.computer.cancel"),
          type: this.$t("bike.computer.warning")
        }
      ).then(() => {
        listAccDel(row).then(response => {
          this.msgSuccess(this.$t("dialog.cancelMedal"));
          this.getList();
        });
      });
    }
  }
};
</script>
