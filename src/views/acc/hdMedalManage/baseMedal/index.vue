<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item :label="$t('base.medal.medalName')" prop="key">
        <el-input
          v-model.trim="queryParams.key"
          :placeholder="$t('base.medal.medalNameInput')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("base.medal.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.medal.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['base:medal:add']"
        >
          {{ $t("base.medal.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
        >
          {{ $t("base.medal.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
        >
          {{ $t("base.medal.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      row-key="id"
      :data="baseMedalList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        type="index"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.name')"
        prop="nameCh"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('base.medal.enName')"
        prop="nameEn"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('base.medal.desc')"
        prop="descCh"
        align="center"
        :show-overflow-tooltip="true"
      >
        <span slot-scope="scope" v-NoData="scope.row.descCh"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.enDesc')"
        prop="descEn"
        align="center"
        :show-overflow-tooltip="true"
      >
        <span slot-scope="scope" v-NoData="scope.row.descEn"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.illumeIcon')"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <preview-img
            :imgUrl="scope.row.brightImg"
            width="45px"
            height="45px"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.notIllumeIcon')"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <preview-img :imgUrl="scope.row.darkImg" width="45px" height="45px" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.AchievementsPostVal')"
        prop="postsValue"
        align="center"
      />
      <el-table-column
        :label="$t('base.medal.AchievementCommentVal')"
        prop="remarkValue"
        align="center"
      />
      <el-table-column :label="$t('base.medal.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.createBy')"
        align="center"
        prop="createBy"
      >
        <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.createTime')"
        align="center"
        sortable
        width="120"
        prop="createTime"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) || "- - -" }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            hasPerminone="['base:medal:edit']"
          >
            {{ $t("base.medal.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="610px"
      center
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.name')" prop="nameCh">
              <el-input
                v-model.trim="form.nameCh"
                clearable
                :placeholder="$t('base.medal.medalNameInput')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.enName')" prop="nameEn">
              <el-input
                v-model.trim="form.nameEn"
                clearable
                :placeholder="$t('form.input') + $t('base.medal.enName')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('base.medal.AchievementsPostVal')"
              prop="postsValue"
            >
              <el-input
                v-model.trim="form.postsValue"
                clearable
                oninput="value=value.replace(/[^\d]/g, '')"
                :placeholder="
                  $t('form.input') + $t('base.medal.AchievementsPostVal')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('base.medal.AchievementCommentVal')"
              prop="remarkValue"
            >
              <el-input
                v-model.trim="form.remarkValue"
                clearable
                oninput="value=value.replace(/[^\d]/g, '')"
                :placeholder="
                  $t('form.input') + $t('base.medal.AchievementCommentVal')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.desc')" prop="descCh">
              <el-input
                v-model="form.descCh"
                :placeholder="$t('base.medal.descInput')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.enDesc')" prop="descEn">
              <el-input
                v-model="form.descEn"
                :placeholder="$t('base.medal.enDesc')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.illumeIcon')" prop="brightImg">
              <el-upload-sortable
                v-model="form.brightImg"
                :imgW="98"
                :imgH="98"
                :isLimit="1"
                :max="1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('base.medal.notIllumeIcon')"
              prop="darkImg"
            >
              <el-upload-sortable
                v-model="form.darkImg"
                :imgW="98"
                :imgH="98"
                :isLimit="1"
                :max="1"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("base.medal._confirm") }}
        </el-button>
        <el-button @click="cancel">
          {{ $t("base.medal._cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBaseMedal,
  authBaseMedal,
  addBaseMedal,
  editBaseMedal
} from "@/api/base/hdMedal";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  name: "BikeCustomer",
  data() {
    return {
      aFn: authBaseMedal,
      // 用户表格数据
      baseMedalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      medalOptions: [],
      medalMap: {},
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        nameCh: [
          {
            required: true,
            message: this.$t("base.medal.medalNameInput"),
            trigger: "blur"
          }
        ],
        nameEn: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("base.medal.enName"),
            trigger: "blur"
          }
        ],
        postsValue: [
          {
            required: true,
            message:
              this.$t("form.input") + this.$t("base.medal.AchievementsPostVal"),
            trigger: "blur"
          }
        ],
        remarkValue: [
          {
            required: true,
            message:
              this.$t("form.input") +
              this.$t("base.medal.AchievementCommentVal"),
            trigger: "blur"
          }
        ],
        brightImg: [
          {
            required: true,
            message: this.$t("base.medal.illumeIcon"),
            trigger: "change"
          }
        ],
        darkImg: [
          {
            required: true,
            message: this.$t("base.medal.notIllumeIcon"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    "form.brightImg"(val) {
      if (val) {
        this.clearValidateItem("form", "brightImg");
      }
    },
    "form.darkImg"(val) {
      if (val) {
        this.clearValidateItem("form", "darkImg");
      }
    }
  },
  created() {
    this.getDicts("base_medal_type").then(response => {
      this.medalOptions = response.data;
      this.medalMap = new Map();
      this.medalOptions.forEach(({ dictLabel, dictValue }) => {
        this.medalMap.set(dictValue, dictLabel);
      });
    });

    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listBaseMedal(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.baseMedalList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("base.medal.addHdMedal");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("base.medal.updateHdMedal");
      this.form = Object.assign({}, row);
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            editBaseMedal(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.medal.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addBaseMedal(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.medal.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
