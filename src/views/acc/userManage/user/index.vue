<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.user.nickName')" prop="key">
        <el-input
          v-model="queryParams.key"
          :placeholder="$t('form.input') + $t('acc.user.nickName')"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.riding.have.email')" prop="email">
        <el-input
          v-model="queryParams.email"
          :placeholder="$t('form.input') + $t('acc.riding.have.email')"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('acc.user.sex')" prop="gender">
        <el-select
          v-model="queryParams.gender"
          clearable
          :placeholder="$t('form.select') + $t('acc.user.sex')"
          style="width: 120px"
        >
          <el-option
            v-for="dict in genderOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('acc.user.activatedState')" prop="status">
        <el-select
          v-model="queryParams.status"
          clearable
          :placeholder="$t('form.select') + $t('acc.user.activatedState')"
          style="width: 120px;"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('acc.user.lastActivityTime')">
        <el-date-picker
          v-model="dateRange"
          style="width: 300px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('acc.user.beginDate')"
          :end-placeholder="$t('acc.user.endDate')"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("acc.user.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("acc.user.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          size="mini"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['acc:user:auth']"
        >
          {{ $t("acc.user.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['acc:user:auth']"
        >
          {{ $t("acc.user.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      :data="userList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="65" align="center" />
      <el-table-column
        :label="$t('discover.posted.headName')"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <el-avatar
            shape="square"
            fit="contain"
            icon="el-icon-user-solid"
            :src="scope.row.headImg"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.nickName')"
        prop="nickName"
        align="center"
      />
      <el-table-column
        :label="$t('acc.user.country')"
        prop="country"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.country" />
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.email')"
        prop="email"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.email" />
      </el-table-column>
      <el-table-column :label="$t('acc.user.sex')" align="center" width="100px">
        <template slot-scope="scope">
          {{
            scope.row.gender == 1
              ? $t("acc.user.man")
              : scope.row.gender == 2
              ? $t("acc.user.woman")
              : $t("acc.user.unknown")
          }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.user.city')" align="center" prop="city">
        <span slot-scope="scope" v-NoData="scope.row.city" />
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.activatedState')"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.lastActivityTime')"
        align="center"
        prop="lastActiveTime"
        sortable
      >
        <span
          slot-scope="scope"
          v-NoData="parseTime(scope.row.lastActiveTime)"
        />
      </el-table-column>
      <el-table-column :label="$t('acc.user.operation')" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleDetail(scope.row)"
            hasPerminone="['acc:user:query']"
          >
            {{ $t("acc.user.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <CompDetail ref="compDetail" />
  </div>
</template>

<script>
import { listUser, authUser } from "@/api/acc/user";
import CompDetail from "./detail/index";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "User",
  mixins: [commonJs],
  components: {
    CompDetail
  },
  data() {
    return {
      aFn: authUser,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        status: undefined,
        key: undefined,
        email: undefined,
        gender: undefined // 1男 2女 3保密
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    const { nickName } = this.$route.query;

    if (nickName) {
      this.queryParams.key = nickName;
    }

    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_user_sex").then(response => {
      this.genderOptions = response.data;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange))
        .then(res => {
          const { list, total } = res.data;
          this.userList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    handleDetail(row) {
      this.$refs["compDetail"].dialogVisble = true;
      this.$refs["compDetail"].getList(row.id);
    }
  }
};
</script>
