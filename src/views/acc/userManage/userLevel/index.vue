<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane :label="$t('acc.userLevel.title')" name="level">
        <Level />
      </el-tab-pane>
      <el-tab-pane :label="$t('acc.userLevel.interval.title')" name="interval">
        <Interval />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Level from './components/level/index.vue'
import Interval from './components/interval/index.vue'

export default {
  name: "UserLevel",
  components: {
    Level,
    Interval
  },
  data() {
    return {
      activeTab: 'level'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
