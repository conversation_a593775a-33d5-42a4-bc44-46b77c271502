<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item :label="$t('base.medal.medalName')" prop="key">
        <el-input
          v-model="queryParams.key"
          :placeholder="$t('base.medal.medalNameInput')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          v-debounce-click="handleQuery"
        >
          {{ $t("base.medal.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.medal.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          hasPerminone="['base:medal:add']"
        >
          {{ $t("base.medal.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-debounce-click="() => handleAuth(0, aFn, getList)"
        >
          {{ $t("base.medal.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          v-debounce-click="() => handleAuth(1, aFn, getList)"
        >
          {{ $t("base.medal.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      ref="multipleTableRef"
      v-loading="loading"
      row-key="id"
      :data="baseMedalList"
      :height="tableHeight()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column
        type="index"
        align="center"
        :label="$t('acc.msg.msg.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.medalName')"
        prop="name"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('base.medal.enName')"
        prop="enName"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('base.medal.desc')"
        prop="desc"
        align="center"
        :show-overflow-tooltip="true"
      >
        <span slot-scope="scope" v-NoData="scope.row.desc"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.enDesc')"
        prop="enDesc"
        align="center"
        :show-overflow-tooltip="true"
      >
        <span slot-scope="scope" v-NoData="scope.row.enDesc"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.illumeIcon')"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <preview-img
            :imgUrl="scope.row.lightenIcon"
            width="45px"
            height="45px"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.notIllumeIcon')"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <preview-img
            :imgUrl="scope.row.greyIcon"
            width="45px"
            height="45px"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.rule')"
        prop="rule"
        align="center"
      />
      <el-table-column
        :label="$t('base.medal.type')"
        prop="type"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ getMedal(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.medal.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.createBy')"
        align="center"
        prop="createBy"
      >
        <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.createTime')"
        align="center"
        sortable
        width="120"
        prop="createTime"
      >
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)"></span>  
      </el-table-column>
      <el-table-column
        :label="$t('base.medal.operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row, aFn, getList)"
            hasPerminone="['base:medal:edit']"
          >
            {{ $t("base.medal.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="610px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.name')" prop="name">
              <el-input
                v-model.trim="form.name"
                clearable
                :placeholder="$t('base.medal.medalNameInput')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.enName')" prop="enName">
              <el-input
                v-model.trim="form.enName"
                clearable
                :placeholder="$t('form.input') + $t('base.medal.enName')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.rule')" prop="rule">
              <el-input
                v-model.trim="form.rule"
                clearable
                oninput="value = value.replace(/[^\d]/g, '')"
                :placeholder="$t('base.medal.ruleInput')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.type')" prop="type">
              <el-select
                v-model="form.type"
                clearable
                style="width: 100%"
                :placeholder="$t('form.select') + $t('base.medal.type')"
              >
                <el-option
                  v-for="dict in medalOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="+dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.desc')" prop="desc">
              <el-input
                v-model="form.desc"
                type="textarea"
                :placeholder="$t('base.medal.descInput')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('base.medal.enDesc')" prop="enDesc">
              <el-input
                v-model="form.enDesc"
                type="textarea"
                :placeholder="$t('form.input') + $t('base.medal.enDesc')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('base.medal.illumeIcon')"
              prop="lightenIcon"
            >
              <el-upload-sortable
                v-model="form.lightenIcon"
                :imgW="98"
                :imgH="98"
                :isLimit="1"
                :max="1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('base.medal.notIllumeIcon')"
              prop="greyIcon"
            >
              <el-upload-sortable
                v-model="form.greyIcon"
                :imgW="98"
                :imgH="98"
                :isLimit="1"
                :max="1"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("base.medal.confirm") }}
        </el-button>
        <el-button @click="cancel">
          {{ $t("base.medal.cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBaseMedal,
  authBaseMedal,
  addBaseMedal,
  editBaseMedal
} from "@/api/base/medal";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  name: "BikeCustomer",
  data() {
    return {
      aFn: authBaseMedal,
      // 用户表格数据
      baseMedalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      medalOptions: [],
      medalMap: {},
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("base.medal.medalNameInput"),
            trigger: "blur"
          }
        ],
        enName: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("base.medal.enName"),
            trigger: "blur"
          }
        ],
        rule: [
          {
            required: true,
            message: this.$t("base.medal.ruleInput"),
            trigger: "blur"
          }
        ],
        type: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("base.medal.type"),
            trigger: "change"
          }
        ],
        lightenIcon: [
          {
            required: true,
            message: this.$t("base.medal.illumeIcon"),
            trigger: "blur"
          }
        ],
        greyIcon: [
          {
            required: true,
            message: this.$t("base.medal.notIllumeIcon"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    "form.lightenIcon"(url) {
      if (url) {
        this.clearValidateItem("form", "lightenIcon");
      }
    },
    "form.greyIcon"(url) {
      if (url) {
        this.clearValidateItem("form", "greyIcon");
      }
    }
  },
  created() {
    this.getDicts("base_medal_type").then(response => {
      this.medalOptions = response.data;
      this.medalMap = new Map();
      this.medalOptions.forEach(({ dictLabel, dictValue }) => {
        this.medalMap.set(dictValue, dictLabel);
      });
    });

    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listBaseMedal(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.baseMedalList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("base.medal.addMedal");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("base.medal.updateMedal");
      this.form = Object.assign({}, row);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            editBaseMedal(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.medal.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addBaseMedal(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.medal.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    },
    getMedal(type) {
      return this.medalMap.get(String(type));
    }
  }
};
</script>
