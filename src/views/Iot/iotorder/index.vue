<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('acc.user.bikeName')" prop="bikeName">
          <el-input
            v-model.trim="queryParams.bikeName"
            :placeholder="$t('form.input') + $t('acc.user.bikeName')"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('shopManage.searchTxt.status')" prop="state">
          <el-select
            v-model="queryParams.state"
            :placeholder="$t('form.select') + $t('shopManage.searchTxt.status')"
            clearable
          >
            <el-option
              v-for="(item, index) in stateList"
              :key="index"
              :label="item"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("acc.user.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("acc.user.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="userList">
      <el-table-column
        :label="$t('shopManage.searchTxt.order')"
        prop="orderNo"
        align="center"
      />
      <el-table-column
        :label="$t('acc.user.bikeName')"
        prop="bikeName"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.bikeName"></span>
      </el-table-column>
      <el-table-column
        :label="$t('acc.log.nickName')"
        prop="userName"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.userName"></span>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.searchTxt.goodName')"
        prop="productName"
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.productName"></span>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.amount')"
        prop="amount"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.tableTxt.payChannel')"
        prop="payChannel"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.payChannel === '10' ? 'primary' : 'danger'">
            {{ row.payChannel === "10" ? "GooglePay" : "ApplePay" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.searchTxt.status')"
        align="center"
        prop="state"
      >
        <template slot-scope="{ row }">
          <el-tag v-show="!is_Empty(row.state)" :type="isStateType(row.state)">
            {{ stateList[row.state] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.isRenew')"
        align="center"
        prop="type"
      >
        <template slot-scope="{ row }">
          <el-tag v-show="!is_Empty(row.type)" :type="isStateType(row.type)">
            {{ typeList[row.type] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.orderTime')"
        align="center"
        sortable
        prop="orderTime"
      >
        <span slot-scope="scope" v-NoData="parseTime(scope.row.orderTime)" />
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.payTime')"
        align="center"
        sortable
        prop="payTime"
      >
        <span slot-scope="scope" v-NoData="parseTime(scope.row.payTime)" />
      </el-table-column>
      <el-table-column
        :label="$t('shopManage.tableTxt.detail')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleDetail(scope.row)"
            hasPerminone="['acc:user:query']"
          >
            {{ $t("acc.user.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
    <CompDetail ref="compDetail" :typeList="typeList" />
  </div>
</template>

<script>
import { orderList } from "@/api/iot/iot";
import CompDetail from "./detail/index";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    CompDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      showSearch: true,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      stateList: {
        0: this.$t("shopManage.stateList.waitPay"),
        1: this.$t("shopManage.stateList.payed"),
        2: this.$t("shopManage.stateList.payError"),
        3: this.$t("shopManage.stateList.cancelPay")
      },
      typeList: {
        1: this.$t("shopManage.tableTxt.FirstFree"),
        2: this.$t("shopManage.tableTxt.Renew")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        bikeName: "",
        state: ""
      }
    };
  },
  computed: {
    isStateType() {
      return state => {
        switch (state) {
          case 0:
            return "info";
          case 1:
            return "success";
          case 2:
            return "danger";
          case 3:
            return "warning";
        }
      };
    }
  },
  created() {
    if (this.$route.query.nickName) {
      this.queryParams.key = this.$route.query.nickName;
    }
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      orderList(this.queryParams).then(response => {
        this.userList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleDetail(row) {
      this.$refs["compDetail"].dialogVisble = true;
      this.$refs["compDetail"].getList(row.id);
    }
  }
};
</script>
