<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('acc.log.nickName')" prop="userName">
          <el-input
            v-model="queryParams.userName"
            :placeholder="$t('form.input') + $t('acc.log.nickName')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('acc.user.bikeName')" prop="bikeName">
          <el-input
            v-model="queryParams.bikeName"
            :placeholder="$t('form.input') + $t('acc.user.bikeName')"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("base.feedback.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("base.feedback.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="userList">
      <el-table-column
        type="index"
        align="center"
        :label="$t('base.feedback.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.log.nickName')"
        prop="userName"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-link
            @click="
              toJumpPagePath({
                path: '/bike/userCenter/user',
                query: {
                  nickName: row.userName
                }
              })
            "
          >
            {{ row.userName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.bikeName')"
        prop="bikeName"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-link
            @click="
              toJumpPagePath({
                path: '/bike/equipmentCenter/bikeMenu',
                query: {
                  carName: row.bikeName
                }
              })
            "
          >
            {{ row.bikeName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.diagnosticMessage')"
        prop="code"
        align="center"
      >
        <template slot-scope="{ row }" v-if="row.code">
          <el-button type="text" @click="onSeeDetail(row.code)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.diagnosticTime')"
        prop="createTime"
        sortable
        align="center"
      >
        <template slot-scope="{ row }">
          {{ parseTime(row.createTime) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <el-dialog
      :visible.sync="isVisible"
      width="350px"
      :title="$t('acc.user.diagnosticMessage')"
      center
    >
      <p
        class="flex justify-around align-center"
        v-for="(item, index) in codeList(rowCode)"
        :key="index"
      >
        <span class="flex-sub text-center">
          {{ mqttErrList[item.item] }}
        </span>
        - - -
        <span
          class="flex-sub text-center"
          :class="mqttErrTypeColor(item.result)"
        >
          {{ mqttErrType[item.result] }}
        </span>
      </p>
    </el-dialog>
  </div>
</template>

<script>
import { iotBikeDiagnosis } from "@/api/iot/iot";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      isVisible: false,
      rowCode: "",
      // 遮罩层
      loading: true,
      senderTypeOptions: {
        1: this.$t("acc.user.read"),
        2: this.$t("acc.user.unread")
      },
      typeOptions: {
        1: this.$t("acc.user.DisplacementAlarm"),
        2: this.$t("acc.user.SOSAlarm"),
        3: this.$t("acc.user.FenceAlarm"),
        4: this.$t("acc.user.VibrationAlarm")
      },
      mqttErrList: {
        1: this.$t("mqttErr.Vltd"),
        2: this.$t("mqttErr.BrakeSteering"),
        3: this.$t("mqttErr.MotorHall"),
        4: this.$t("mqttErr.CommunicationDetection"),
        5: this.$t("mqttErr.OvertemperatureDetection"),
        6: this.$t("mqttErr.Controller"),
        7: this.$t("mqttErr.OvercurrentDetection")
      },
      mqttErrType: {
        0: this.$t("mqttErr.Normal"),
        1: this.$t("mqttErr.Failure"),
        2: this.$t("mqttErr.Risk")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        userName: null,
        bikeName: null
      },
      userList: []
    };
  },
  computed: {
    codeList() {
      return code => {
        if (code) {
          return JSON.parse(code).results;
        }
      };
    },
    mqttErrTypeColor() {
      return type => {
        let className = "";
        switch (+type) {
          case 0:
            className = "text-green";
            break;
          case 1:
            className = "text-red";
            break;
          case 2:
            className = "text-gray";
            break;
        }
        return className;
      };
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      iotBikeDiagnosis(this.queryParams).then(res => {
        const { list, total } = res.data;
        this.userList = list;
        this.total = total;
        this.loading = false;
      });
    },
    onSeeDetail(code) {
      this.rowCode = code;
      this.isVisible = true;
    },
    jump(row, type) {
      if (type === 1) {
        this.$router.push({
          path: "/bike/userCenter/user",
          query: {
            nickName: row.userId
          }
        });
      } else {
        this.$router.push({
          path: "/Basic/modelCenter/model",
          query: {
            bikeName: row.bikeName
          }
        });
      }
    }
  }
};
</script>
