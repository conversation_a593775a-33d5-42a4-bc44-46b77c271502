<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        ref="queryForm"
        :model="queryParams"
        :inline="true"
        @submit.prevent.native
        v-show="showSearch"
      >
        <el-form-item :label="$t('acc.user.country')" prop="countryName">
          <el-input
            v-model="queryParams.countryName"
            :placeholder="$t('form.input') + $t('acc.user.country')"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("bike.customer.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.customer.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("bike.customer.newAdd") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="customerList">
      <el-table-column
        type="index"
        width="60"
        :label="$t('acc.msg.msg.serialNumber')"
        align="center"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.country')"
        prop="countryName"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.tableTxt.productNo')"
        prop="no"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.searchTxt.goodName')"
        prop="name"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.searchTxt.goodName') + '(en)'"
        prop="nameEn"
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.tableTxt.price')"
        prop="price"
        sortable
        align="center"
      />
      <el-table-column
        :label="$t('shopManage.tableTxt.price') + '(en)'"
        prop="priceEn"
        sortable
        align="center"
      />
      <el-table-column
        :label="$t('base.fqa.sort')"
        prop="sort"
        sortable
        align="center"
      >
        <span slot-scope="scope" v-NoData="scope.row.sort"></span>
      </el-table-column>
      <el-table-column
        :label="$t('system.role.remark')"
        prop="remark"
        align="center"
        show-overflow-tooltip
      >
        <span slot-scope="scope" v-NoData="scope.row.remark"></span>
      </el-table-column>
      <el-table-column :label="$t('bike.customer.operation')" align="center">
        <template v-slot="{ row }">
          <el-button type="text" @click="handleUpdate(row)">
            {{ $t("bike.customer.update") }}
          </el-button>
          <el-button
            type="text"
            class="text-red"
            @click="handleDel([row.id], delFn, getList)"
          >
            {{ $t("queryParams.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      :close-on-click-modal="false"
      width="500px"
      append-to-body
      center
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="110px"
        label-position="left"
      >
        <el-form-item :label="$t('acc.user.country')" prop="countryName">
          <el-input
            v-model.trim="form.countryName"
            clearable
            :placeholder="$t('form.input') + $t('acc.user.country')"
          />
        </el-form-item>
        <el-form-item :label="$t('shopManage.tableTxt.productNo')" prop="no">
          <el-input
            v-model.trim="form.no"
            clearable
            :placeholder="
              $t('form.input') + $t('shopManage.tableTxt.productNo')
            "
          />
        </el-form-item>
        <el-form-item :label="$t('shopManage.searchTxt.goodName')" prop="name">
          <el-input
            v-model="form.name"
            clearable
            :placeholder="
              $t('form.input') + $t('shopManage.searchTxt.goodName')
            "
          />
        </el-form-item>
        <el-form-item
          :label="$t('shopManage.searchTxt.goodName') + '(en)'"
          prop="nameEn"
        >
          <el-input
            v-model="form.nameEn"
            clearable
            :placeholder="
              $t('form.input') + $t('shopManage.searchTxt.goodName')
            "
          />
        </el-form-item>
        <el-form-item :label="$t('shopManage.tableTxt.price')" prop="price">
          <el-input-number
            v-model="form.price"
            clearable
            :min="1"
            :precision="2"
            style="width: 80%"
            :placeholder="$t('form.input') + $t('shopManage.tableTxt.price')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('shopManage.tableTxt.price') + '(en)'"
          prop="priceEn"
        >
          <el-input-number
            v-model="form.priceEn"
            clearable
            :min="1"
            :precision="2"
            style="width: 80%"
            :placeholder="$t('form.input') + $t('shopManage.tableTxt.price')"
          />
        </el-form-item>
        <el-form-item :label="$t('base.fqa.sort')" prop="sort">
          <el-input-number
            v-model="form.sort"
            clearable
            :min="0"
            :precision="0"
            controls-position="right"
            style="width: 80%"
            :placeholder="$t('form.input') + $t('base.fqa.sort')"
          />
        </el-form-item>
        <el-form-item :label="$t('system.role.remark')" prop="remark">
          <el-input
            v-model.trim="form.remark"
            type="textarea"
            clearable
            :placeholder="$t('form.input') + $t('system.role.remark')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.customer._confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("bike.customer._cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  productList,
  productSave,
  productUpdate,
  productDelete
} from "@/api/iot/iot";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "BikeCustomer",
  mixins: [commonJs],
  data() {
    return {
      delFn: productDelete,
      // 用户表格数据
      customerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      showSearch: true,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        countryName: ""
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        countryName: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("acc.user.country"),
            trigger: "blur"
          }
        ],
        no: [
          {
            required: true,
            message:
              this.$t("form.input") + this.$t("shopManage.tableTxt.productNo"),
            trigger: "blur"
          }
        ],
        name: [
          {
            required: true,
            message:
              this.$t("form.input") + this.$t("shopManage.searchTxt.goodName"),
            trigger: "blur"
          }
        ],
        nameEn: [
          {
            required: true,
            message:
              this.$t("form.input") +
              this.$t("shopManage.searchTxt.goodName") +
              "(en)",
            trigger: "blur"
          }
        ],
        price: [
          {
            required: true,
            message:
              this.$t("form.input") + this.$t("shopManage.tableTxt.price"),
            trigger: ["blur", "change"]
          }
        ],
        priceEn: [
          {
            required: true,
            message:
              this.$t("form.input") +
              this.$t("shopManage.tableTxt.price") +
              "(en)",
            trigger: ["blur", "change"]
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      productList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.customerList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleAdd() {
      this.open = true;
      this.title = this.$t("acc.user.addIotCharges");
      this.reset();
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("acc.user.resetIotChares");
      this.form = Object.assign({}, row);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            productUpdate(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.customer.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            productSave({ type: 3, ...this.form })
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.customer.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
