<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('acc.user.alarmType')" prop="type">
          <el-select
            v-model="queryParams.type"
            :placeholder="
              $t('base.feedback.pleaseChoose') + $t('base.feedback.type')
            "
            clearable
          >
            <el-option
              v-for="(value, key) in typeOptions"
              :key="key"
              :label="value"
              :value="key"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('shopManage.searchTxt.status')" prop="read">
          <el-select
            v-model="queryParams.read"
            :placeholder="$t('form.select') + $t('shopManage.searchTxt.status')"
            clearable
          >
            <el-option
              v-for="(value, key) in senderTypeOptions"
              :key="key"
              :label="value"
              :value="key"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('acc.user.alarmTime')">
          <el-date-picker
            v-model="dateRange"
            value-format="timestamp"
            type="datetimerange"
            range-separator="-"
            :start-placeholder="$t('acc.user.beginDate')"
            :end-placeholder="$t('acc.user.endDate')"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            v-debounce-click="handleQuery"
          >
            {{ $t("base.feedback.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("base.feedback.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="userList">
      <el-table-column
        type="index"
        align="center"
        :label="$t('base.feedback.serialNumber')"
      >
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.log.nickName')"
        prop="userName"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-link
            v-if="!is_Empty(row.userName)"
            @click="
              toJumpPagePath({
                path: '/bike/userCenter/user',
                query: {
                  nickName: row.userId
                }
              })
            "
          >
            {{ row.userName }}
          </el-link>
          <span v-else>- - -</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.bikeName')"
        prop="bikeName"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-link
            v-if="!is_Empty(row.bikeName)"
            @click="
              toJumpPagePath({
                path: '/bike/equipmentCenter/bikeMenu',
                query: {
                  carName: row.bikeName
                }
              })
            "
          >
            {{ row.bikeName }}
          </el-link>
          <span v-else>- - -</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.alarmType')"
        prop="type"
        align="center"
      >
        <span slot-scope="scope" v-NoData="typeOptions[row.type]" />
      </el-table-column>
      <el-table-column :label="$t('acc.user.LocationInfo')" align="center">
        <template slot-scope="{ row }">
          <el-link
            v-if="row.lon && row.lat"
            @click="
              toBuMap({
                longitude: row.lon,
                latitude: row.lat,
                content: '车辆位置'
              })
            "
          >
            车辆位置
          </el-link>
          <span v-else>- - -</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.isRead')"
        prop="read"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.read === 0 ? 'danger' : 'primary'">
            {{ senderTypeOptions[row.read] || "- - -" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('acc.user.alarmTime')"
        prop="alarmTime"
        sortable
        align="center"
      >
        <template slot-scope="{ row }">
          {{ parseTime(row.createTime) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.p"
      :limit.sync="queryParams.l"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { iotBikeAlarm } from "@/api/iot/iot";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 遮罩层
      loading: true,
      senderTypeOptions: {
        0: this.$t("acc.user.unread"),
        1: this.$t("acc.user.read")
      },
      typeOptions: {
        1: this.$t("acc.user.DisplacementAlarm"),
        2: this.$t("acc.user.SOSAlarm"),
        3: this.$t("acc.user.FenceAlarm"),
        4: this.$t("acc.user.VibrationAlarm")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: "",
        read: ""
      },
      userList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      iotBikeAlarm(
        this.addDateRange(this.queryParams, this.dateRange, {
          beginTime: "sTime",
          endTime: "eTime",
          timestampLen: 10
        })
      ).then(res => {
        const { list, total } = res.data;
        this.userList = list;
        this.total = total;
        this.loading = false;
      });
    }
  }
};
</script>
