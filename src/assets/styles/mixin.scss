@mixin boxShadow($boxShadow: (0px 12px 32px 4px rgba(0,0,0,.04),0px 8px 20px rgba(0,0,0,.08))) {
  box-shadow: $boxShadow;
}
@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

/* 修改滚动条的样式 */
@mixin scrollBar(
  $width: 6px,
  $trackBgColor: #f1f1f1,
  $borderRadius: 20px,
  $thumbBgColor: #99a9bf,
  $thumbHoverBgColor: #b5cae7
) {
  &::-webkit-scrollbar {
    width: $width; /* 设置滚动条宽度 */
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background-color: $trackBgColor; /* 设置滚动条轨道背景色 */
  }

  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    background: $thumbBgColor;
    border-radius: $borderRadius;
    opacity: 0;
    transition: opacity 0.2s ease-out;
    &:hover {
      opacity: 1;
      background: $thumbHoverBgColor;
    }
  }
}

/*  滚动盒子 */
@mixin boxOverflowScroll(
  $minW: auto,
  $maxW: auto,
  $minH: auto,
  $maxH: auto,
  $overflowX: auto,
  $overflowY: auto,
  $direction: vertical
) {
  overflow: hidden;
  @if $direction == vertical {
    min-height: $minH;
    max-height: $maxH;
    overflow-y: $overflowY;
  } @else if $direction == horizontal {
    min-width: $minW;
    max-width: $maxW;
    overflow-x: $overflowX;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  } @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}
