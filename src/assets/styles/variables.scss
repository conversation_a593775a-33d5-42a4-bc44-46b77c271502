// base color
$blue: #2563eb; // 主色调（按钮/高亮）
$light-blue: #60a5fa; // 按钮悬停/淡色背景
$red: #ef4444; // 错误状态
$pink: #f472b6; // 点缀色
$green: #22c55e; // 成功状态
$tiffany: #2dd4bf; // 次要强调色
$yellow: #facc15; // 警告色
$panGreen: #10b981; // 品牌绿色

// sidebar（亮色模式统一）
$menuText: #475569; // slate-600，深灰文本
$menuActiveText: #2563eb; // 激活项蓝色
$subMenuActiveText: #0f172a; // slate-900，激活子菜单文字

$menuBg: #ffffff; // 白色背景
$menuHover: #f0f4ff; // hover 背景蓝白
$subMenuBg: #f9fafb; // 子菜单背景淡灰
$subMenuHover: #e0edff; // 子菜单 hover 蓝灰

$sideBarWidth: 240px;

// 导出变量供 JS 使用
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}