@font-face {
  font-family: "iconfont"; /* Project id 3957044 */
  src: url('iconfont.woff2?t=1678954394532') format('woff2'),
       url('iconfont.woff?t=1678954394532') format('woff'),
       url('iconfont.ttf?t=1678954394532') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-facebookfacebook52:before {
  content: "\e605";
}

.icon-google-circle-fill:before {
  content: "\e887";
}

.icon-ic_apple_round:before {
  content: "\e618";
}

