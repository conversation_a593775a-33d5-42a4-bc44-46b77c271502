<template>
  <el-dialog
    :title="title"
    v-bind="$attrs"
    :visible="isShow"
    :width="width"
    center
    :close-on-click-modal="false"
    @close="$emit('update:isShow', false)"
  >
    <div v-loading="diaLoading">
      <slot>
        <el-empty description="~空空如也~"></el-empty>
      </slot>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "ElShowText",
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    width: {
      type: [String, Number],
      default: "40%"
    }
  },
  watch: {
    isShow(show) {
      if (show) {
        this.diaLoading = true;
        this.loadingTimer = setTimeout(() => {
          this.diaLoading = false;
        }, 1200);
      }
    }
  },
  data() {
    return {
      diaLoading: false,
      loadingTimer: null
    };
  },
  destroyed() {
    this.diaLoading = false;
    clearTimeout(this.loadingTimer);
  }
};
</script>
<style lang="scss">
@import "@/assets/styles/mixin.scss";
.box-border {
  border: 1px solid #f6f6f6;
  padding: 15px;
}
.box-scrollbar {
  @extend .box-border;
  @include boxOverflowScroll($minH: 400px, $maxH: 500px);
  @include scrollBar();
}
</style>
