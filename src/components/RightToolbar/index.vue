<template>
  <div class="fr">
    <el-row>
      <el-tooltip
        class="item"
        effect="dark"
        :content="showSearch ? '隐藏搜索' : '显示搜索'"
        placement="top"
      >
        <el-button
          size="mini"
          circle
          icon="el-icon-search"
          @click="toggleSearch()"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="刷新" placement="top">
        <el-button
          size="mini"
          circle
          icon="el-icon-refresh"
          v-debounce-click="() => refresh()"
        />
      </el-tooltip>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "RightToolbar",
  props: {
    showSearch: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleSearch() {
      this.$emit("update:showSearch", !this.showSearch);
    },
    refresh() {
      this.$emit("queryTable");
    }
  }
};
</script>
