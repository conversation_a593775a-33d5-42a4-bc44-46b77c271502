<template>
  <preview-img :imgStyle="imgStyle" :imgUrl="imgSrc" />
</template>

<script>
export default {
  name: "ElImgIcon",  
  props: {
    imgStyle: {
      type: Object,
      default: () => {
        return {
          width: "25px",
          height: "25px",
          cursor: "pointer"
        };
      }
    },
    imgSrc: {
      type: String,
      default: require("@/assets/image/xiazai.png")
    }
  }
};
</script>

<style></style>
