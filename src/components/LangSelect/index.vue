<template>
  <el-dropdown trigger="click" class="international pointer" @command="handleSetLanguage">
    <div>
      <svg-icon class-name="international-icon" icon-class="language" />
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language === 'zh'" command="zh">
        中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language === 'en'" command="en">
        English
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  methods: {
    handleSetLanguage(lang) {
      // 先更新 store 状态
      this.$store.dispatch('app/setLanguage', lang)
      // 然后更新 i18n 实例的语言设置
      this.$i18n.locale = lang
      // 显示成功消息
      this.$message({
        message: lang === 'zh' ? '语言切换成功' : 'Language switched successfully',
        type: 'success'
      })
    }
  }
}
</script>