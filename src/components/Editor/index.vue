<template>
  <div
    v-loading="isLoading"
    :class="{ fullscreen: fullscreen }"
    class="tinymce-container"
    :style="{ width: containerWidth }"
  >
    <textarea :id="tinymceId" class="tinymce-textarea" />
    <div class="editor-custom-btn-container">
      <editorImage
        color="#1890ff"
        class="editor-upload-btn"
        @successCBK="imageSuccessCBK"
      />
    </div>
  </div>
</template>

<script>
/**
 * docs:
 * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce
 */
import editorImage from "./components/EditorImage";
import plugins from "./plugins";
import toolbar from "./toolbar";
import load from "./dynamicLoadScript";

// why use this cdn, detail see https://github.com/PanJiaChen/tinymce-all-in-one
// const tinymceCDN = "https://unpkg.com/tinymce-all-in-one@4.9.3/tinymce.min.js";
// 使用更稳定的CDN源
const tinymceCDN = "https://cdn.jsdelivr.net/npm/tinymce@5.10.7/tinymce.min.js";

export default {
  name: "Tinymce",
  components: { editorImage },
  props: {
    id: {
      type: String,
      default: function () {
        return (
          "vue-tinymce-" +
          +new Date() +
          ((Math.random() * 1000).toFixed(0) + "")
        );
      },
    },
    value: {
      type: String,
      default: "",
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return [];
      },
    },
    menubar: {
      type: String,
      default: "file edit insert view format table",
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360,
    },
    width: {
      type: [Number, String],
      required: false,
      default: "auto",
    },
  },
  data() {
    return {
      isLoading: true,
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false,
      languageTypeList: {
        en: "en",
        zh: "zh_CN",
        es: "es_MX",
        ja: "ja",
      },
    };
  },
  computed: {
    containerWidth() {
      const width = this.width;
      if (/^[\d]+(\.[\d]+)?$/.test(width)) {
        // matches `100`, `'100'`
        return `${width}px`;
      }
      return width;
    },
  },
  watch: {
    value(val) {
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() => {
          const editor = window.tinymce.get(this.tinymceId);
          if (editor) {
            editor.setContent(val || "");
          }
        });
      }
    },
  },
  mounted() {
    console.log('Editor 组件已挂载，ID:', this.tinymceId);
    // 确保目标元素存在
    this.$nextTick(() => {
      const targetElement = document.getElementById(this.tinymceId);
      if (targetElement) {
        console.log('找到目标 textarea 元素');
        this.init();
      } else {
        console.error('未找到目标 textarea 元素，ID:', this.tinymceId);
      }
    });
  },
  activated() {
    console.log('Editor 组件已激活');
    this.$nextTick(() => {
      if (window.tinymce && !window.tinymce.get(this.tinymceId)) {
        console.log('重新初始化 TinyMCE');
        this.initTinymce();
      }
    });
  },
  deactivated() {
    console.log('Editor 组件已停用');
    this.destroyTinymce();
  },
  destroyed() {
    this.destroyTinymce();
  },
  methods: {
    init() {
      // dynamic load tinymce from cdn
      load(tinymceCDN, (err) => {
        this.isLoading = false;
        if (err) {
          console.error('TinyMCE CDN 加载失败:', err);
          this.$message.error(`TinyMCE 加载失败: ${err.message}`);
          return;
        }
        console.log('TinyMCE CDN 加载成功');
        this.initTinymce();
      });
    },
    initTinymce() {
      const _this = this;
      
      try {
        console.log('开始初始化 TinyMCE，目标元素ID:', this.tinymceId);
        
        window.tinymce.init({
          selector: `#${this.tinymceId}`,
          // 暂时移除中文语言包，使用默认英文
          // language: this.languageTypeList[this.$store.state.app.language] || 'zh_CN',
          height: this.height,
          body_class: "panel-body ",
          object_resizing: false,
          toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
          menubar: this.menubar,
          plugins: plugins,
          end_container_on_empty_block: true,
          powerpaste_word_import: "clean",
          code_dialog_height: 450,
          code_dialog_width: 1000,
          advlist_bullet_styles: "square",
          advlist_number_styles: "default",
          imagetools_cors_hosts: ["www.tinymce.com", "codepen.io"],
          default_link_target: "_blank",
          link_title: false,
          content_style: "img {max-width:100%;}",
          nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
          branding: false, // 移除TinyMCE品牌信息
          
          init_instance_callback: (editor) => {
            console.log('TinyMCE 编辑器初始化完成');
            if (_this.value) {
              editor.setContent(_this.value);
            }
            _this.hasInit = true;
            _this.hasChange = false; // 重置变更状态
            editor.on("NodeChange Change KeyUp SetContent", () => {
              this.hasChange = true;
              this.$emit("input", editor.getContent());
            });
          },
          
          setup(editor) {
            editor.on("FullscreenStateChanged", (e) => {
              _this.fullscreen = e.state;
            });
            
            editor.on("init", () => {
              console.log('TinyMCE 编辑器设置完成，可以接收输入');
            });
          },
        // it will try to keep these URLs intact
        // https://www.tiny.cloud/docs-3x/reference/configuration/Configuration3x@convert_urls/
        // https://stackoverflow.com/questions/5196205/disable-tinymce-absolute-to-relative-url-conversions
        convert_urls: false,
        // 整合七牛上传
        // images_dataimg_filter(img) {
        //   setTimeout(() => {
        //     const $image = $(img);
        //     $image.removeAttr('width');
        //     $image.removeAttr('height');
        //     if ($image[0].height && $image[0].width) {
        //       $image.attr('data-wscntype', 'image');
        //       $image.attr('data-wscnh', $image[0].height);
        //       $image.attr('data-wscnw', $image[0].width);
        //       $image.addClass('wscnph');
        //     }
        //   }, 0);
        //   return img
        // },
        // images_upload_handler(blobInfo, success, failure, progress) {
        //   progress(0);
        //   const token = _this.$store.getters.token;
        //   getToken(token).then(response => {
        //     const url = response.data.qiniu_url;
        //     const formData = new FormData();
        //     formData.append('token', response.data.qiniu_token);
        //     formData.append('key', response.data.qiniu_key);
        //     formData.append('file', blobInfo.blob(), url);
        //     upload(formData).then(() => {
        //       success(url);
        //       progress(100);
        //     })
        //   }).catch(err => {
        //     failure('出现未知问题，刷新页面，或者联系程序员')
        //     console.log(err);
        //   });
        // },
        });
      } catch (error) {
        console.error('TinyMCE 初始化失败:', error);
        this.$message.error('富文本编辑器初始化失败，请刷新页面重试');
      }
    },
    destroyTinymce() {
      try {
        if (window.tinymce) {
          const tinymce = window.tinymce.get(this.tinymceId);
          if (tinymce) {
            if (this.fullscreen) {
              tinymce.execCommand("mceFullScreen");
            }
            tinymce.destroy();
            console.log('TinyMCE 编辑器已销毁');
          }
        }
      } catch (error) {
        console.error('销毁 TinyMCE 编辑器时出错:', error);
      }
    },
    setContent(value) {
      try {
        const editor = window.tinymce.get(this.tinymceId);
        if (editor) {
          editor.setContent(value);
        }
      } catch (error) {
        console.error('设置内容失败:', error);
      }
    },
    getContent() {
      try {
        const editor = window.tinymce.get(this.tinymceId);
        if (editor) {
          return editor.getContent();
        }
        return '';
      } catch (error) {
        console.error('获取内容失败:', error);
        return '';
      }
    },
    imageSuccessCBK(arr) {
      arr.forEach((v) =>
        window.tinymce
          .get(this.tinymceId)
          .insertContent(
            `<img class="wscnph" src="${v.url}"  style="width:100%">`
          )
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
  width: 100%;
  
  // 确保编辑器容器可以接收事件
  pointer-events: auto;
  
  // 加载状态样式
  &[v-loading] {
    min-height: 300px;
  }
}

.tinymce-container {
  ::v-deep {
    .mce-fullscreen {
      z-index: 10000;
    }
    
    // TinyMCE 编辑器样式
    .tox-tinymce {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      
      &:hover {
        border-color: #c0c4cc;
      }
      
      &.tox-tinymce--toolbar-sticky-off {
        .tox-toolbar-overlord {
          background-color: #f5f7fa;
        }
      }
    }
    
    // 编辑区域样式
    .tox-edit-area {
      border: none !important;
    }
    
    .tox-edit-area__iframe {
      background-color: #fff !important;
    }
    
    // 工具栏样式
    .tox-toolbar {
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
    }
    
    // 按钮样式
    .tox-tbtn {
      &:hover {
        background-color: #ecf5ff;
        border-color: #b3d8ff;
        color: #409eff;
      }
    }
    
    // 状态栏样式
    .tox-statusbar {
      background-color: #f5f7fa;
      border-top: 1px solid #e4e7ed;
    }
  }
}

.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.editor-custom-btn-container {
  position: absolute;
  right: 8px;
  top: 8px;
  z-index: 2005;
}

.fullscreen .editor-custom-btn-container {
  z-index: 10000;
  position: fixed;
}

.editor-upload-btn {
  display: inline-block;
}
</style>
