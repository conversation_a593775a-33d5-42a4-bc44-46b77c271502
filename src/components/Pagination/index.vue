<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import { scrollTo } from "@/utils/scroll-to";

export default {
  name: "Pagination",
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50];
      }
    },
    layout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper"
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: <PERSON>olean,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(page) {
        this.$emit("update:page", page);
      }
    },
    pageSize: {
      get() {
        return this.limit;
      },
      set(limit) {
        this.$emit("update:limit", limit);
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit("pagination");
      if (this.autoScroll) {
        scrollTo(0, 800);
      }
    },
    handleCurrentChange(val) {
      this.$emit("pagination");
      if (this.autoScroll) {
        scrollTo(0, 800, {
          ease: "out-bounce",
          duration: 1500
        });
      }
    }
  }
};
</script>

<style scoped>
.pagination-container {
  background: #fff;
  padding: 32px 16px;
}
.pagination-container.hidden {
  display: none;
}
</style>

<!-- <template>
  <div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :layout="layout"
      :total="400"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: "Pro40AdminIndex",
  props: {
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 10
    },
    small: {
      type: Boolean,
      default: true
    },
    background: {
      type: Boolean,
      default: true
    },
    layout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper"
    },
    pagerCount: {
      type: Number,
      default: 7
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 40];
      }
    }
  },
  data() {
    return {};
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(page) {
        this.$emit("update:page", page);
      }
    }
  },
  mounted() {},

  methods: {}
};
</script> -->
