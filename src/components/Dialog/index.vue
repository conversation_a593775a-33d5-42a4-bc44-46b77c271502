<template>
  <el-dialog
    v-bind="$attrs"
    :title="title"
    :visible="isVisible"
    :width="width"
    center
    @close="$emit('update:isVisible', false)"
  >
    <slot></slot>
    <template v-if="isDialogFooter">
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('onCancel')">{{ cancelTitle }}</el-button>
        <el-button type="primary" @click="$emit('onSubmit')">
          {{ submitTitle }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import lang from "@/lang";
export default {
  name: "Dialog",
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "标题"
    },
    width: {
      type: String,
      default: "30%"
    },
    center: {
      type: Boolean,
      default: true
    },
    cancelTitle: {
      type: String,
      default: lang.t("bike.model.cancel")
    },
    submitTitle: {
      type: String,
      default: lang.t("bike.model.confirm")
    },
    isDialogFooter: {
      type: Boolean,
      default: false
    }
  }
};
</script>
