# FileUpload 文件上传分段按钮组件

基于分段上传技术的文件上传组件，支持大文件断点续传，提供按钮式的上传界面。

## 特性

- 🚀 **分段上传**: 支持大文件分段上传，提高上传成功率
- 🔄 **断点续传**: 网络中断后可自动重试，支持断点续传
- 📁 **多文件支持**: 支持单文件和多文件上传
- 🎯 **文件类型限制**: 可限制上传的文件类型
- 📊 **实时进度**: 显示详细的上传进度和分块信息
- 🎨 **美观界面**: 提供文件列表、预览、下载等功能
- 🔒 **安全可靠**: 使用MD5校验确保文件完整性
- 🌍 **国际化支持**: 完整的中英文国际化支持
- 🎛️ **双模式支持**: 支持按钮模式和拖拽模式两种上传方式

## 基础用法

```vue
<template>
  <FileUpload 
    v-model="fileList"
    @change="handleChange"
    @upload-success="handleUploadSuccess"
  />
</template>

<script>
import FileUpload from '@/components/FileUpload/index.vue';

export default {
  components: {
    FileUpload
  },
  data() {
    return {
      fileList: []
    };
  },
  methods: {
    handleChange(value) {
      console.log('文件列表变化:', value);
    },
    handleUploadSuccess(response, file, fileList) {
      console.log('上传成功:', response);
    }
  }
};
</script>
```

## 属性配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value / v-model | Array/String | [] | 文件URL列表，支持数组和字符串格式 |
| limit | Number | 5 | 最大上传文件数量 |
| accept | String | '*/*' | 接受的文件类型，同HTML input accept |
| multiple | Boolean | true | 是否支持多文件选择 |
| size | String | 'medium' | 按钮尺寸：large/medium/small/mini |
| mode | String | 'button' | 上传模式：button-按钮模式, drag-拖拽模式 |
| dragWidth | Number/String | '100%' | 拖拽区域宽度（仅拖拽模式有效） |
| dragHeight | Number/String | 200 | 拖拽区域高度（仅拖拽模式有效） |
| uploadUrl | String | 'http://api.binyo.net/app-store/upload-fd?do_action=action.segmentFd' | 上传接口地址 |
| maxSize | Number | 500 | 最大文件大小限制（MB） |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | (value) | v-model绑定事件 |
| change | (value) | 文件列表变化事件 |
| upload-success | (response, file, fileList) | 文件上传成功事件 |
| upload-error | (error, file, fileList) | 文件上传失败事件 |

## 使用示例

### 1. 按钮模式（默认）

```vue
<FileUpload v-model="files" mode="button" />
```

### 2. 拖拽模式

```vue
<FileUpload 
  v-model="files" 
  mode="drag"
  :drag-width="'100%'"
  :drag-height="200"
/>
```

### 3. 限制文件类型和数量

```vue
<FileUpload 
  v-model="images"
  mode="drag"
  :limit="3"
  accept="image/*"
  @change="handleImageChange"
/>
```

### 4. 单文件上传

```vue
<!-- 按钮模式 -->
<FileUpload 
  v-model="singleFile"
  mode="button"
  :limit="1"
  :multiple="false"
  size="large"
/>

<!-- 拖拽模式 -->
<FileUpload 
  v-model="singleFile"
  mode="drag"
  :limit="1"
  :multiple="false"
  :drag-width="300"
  :drag-height="150"
/>
```

### 5. 自定义上传地址

```vue
<FileUpload 
  v-model="files"
  mode="drag"
  upload-url="https://your-api.com/upload"
  :max-size="100"
/>
```

### 6. 字符串格式

```vue
<template>
  <FileUpload v-model="fileUrls" mode="drag" />
</template>

<script>
export default {
  data() {
    return {
      // 字符串格式，多个URL用逗号分隔
      fileUrls: 'http://example.com/file1.jpg,http://example.com/file2.pdf'
    };
  }
};
</script>
```

### 7. 模式对比

```vue
<template>
  <div>
    <!-- 按钮模式：适合传统文件选择场景 -->
    <FileUpload 
      v-model="buttonFiles"
      mode="button"
      :limit="5"
      size="medium"
    />
    
    <!-- 拖拽模式：适合现代化的拖拽上传场景 -->
    <FileUpload 
      v-model="dragFiles"
      mode="drag"
      :limit="5"
      :drag-width="'100%'"
      :drag-height="200"
    />
  </div>
</template>
```

## 技术原理

### 分段上传流程

1. **文件选择**: 用户选择文件后，组件会检查文件大小和类型
2. **MD5计算**: 计算整个文件的MD5哈希值，用于服务端验证
3. **文件分块**: 将文件按5MB大小分割成多个块
4. **分块上传**: 逐个上传每个分块，每个分块都有独立的MD5
5. **进度更新**: 实时更新上传进度和分块信息
6. **结果处理**: 上传完成后拼接完整的文件URL

### 上传参数

组件会向服务器发送以下参数：

- `Path_0[]`: 文件分块数据
- `name`: 原始文件名
- `md5`: 整个文件的MD5哈希值
- `chunk`: 当前分块索引（从0开始）
- `chunks`: 总分块数量
- `totalSize`: 文件总大小
- `chunk_md5`: 当前分块的MD5哈希值

### 服务器响应格式

服务器应返回以下格式的JSON响应：

```json
{
  "status": 200,
  "message": "上传成功",
  "data": {
    "domain": "http://binyo.net",
    "path": ["/u_file/app/20/filename.ext"]
  }
}
```

## 样式定制

组件提供了丰富的CSS类名，可以通过覆盖样式来定制外观：

```scss
.file-upload-segment {
  // 主容器样式
  
  .upload-progress {
    // 上传进度样式
  }
  
  .file-list {
    // 文件列表样式
    
    .file-item {
      // 单个文件项样式
    }
  }
}
```

## 国际化

组件已完整支持国际化，包含以下语言文件：

### 中文 (zh.js)
```javascript
fileUpload: {
  selectFile: '选择文件',
  uploading: '上传中...',
  uploadProgress: '上传进度',
  remaining: '剩余',
  limitReached: '已达到限制',
  uploadedFiles: '已上传文件',
  clearAll: '清空所有',
  preview: '预览',
  download: '下载',
  delete: '删除',
  // ... 更多文本
}
```

### 英文 (en.js)
```javascript
fileUpload: {
  selectFile: "Select File",
  uploading: "Uploading...",
  uploadProgress: "Upload Progress",
  remaining: "remaining",
  limitReached: "Limit reached",
  uploadedFiles: "Uploaded Files",
  clearAll: "Clear All",
  preview: "Preview",
  download: "Download",
  delete: "Delete",
  // ... 更多文本
}
```

### 使用方法

组件会自动根据当前语言环境显示对应的文本。如需添加其他语言支持，请在对应的语言文件中添加 `fileUpload` 配置项。

## 注意事项

1. **浏览器兼容性**: 需要现代浏览器支持 File API 和 Fetch API
2. **网络环境**: 建议在稳定的网络环境下使用，组件会自动重试失败的分块
3. **服务器配置**: 确保服务器支持分段上传和MD5校验
4. **文件大小**: 虽然支持大文件上传，但建议根据实际需求设置合理的大小限制
5. **并发限制**: 组件按顺序上传分块，避免过多并发请求
6. **国际化**: 确保项目中已正确配置 vue-i18n 并包含所需的语言文件

## 故障排除

### 常见问题

1. **SparkMD5 加载失败**
   - 检查网络连接
   - 可以本地部署 SparkMD5 库

2. **上传失败**
   - 检查服务器接口地址是否正确
   - 确认服务器支持分段上传
   - 查看浏览器控制台错误信息

3. **文件列表不显示**
   - 检查 v-model 绑定的数据格式
   - 确认数据中包含有效的URL

### 调试模式

组件会在控制台输出详细的调试信息，包括：
- 文件分块信息
- MD5计算结果
- 上传进度
- 服务器响应

可以通过浏览器开发者工具查看这些信息来排查问题。 