<template>
  <div class="file-upload-segment">
    <!-- 按钮模式 -->
    <div v-if="mode === 'button'" class="button-mode">
      <el-button type="primary" :loading="uploading" :disabled="isLimitReached || uploading" @click="handleButtonClick"
        :size="size" :icon="uploading ? 'el-icon-loading' : 'el-icon-upload'">
        {{ $t('fileUpload.selectFile') + (uploading ? ` ${uploadProgress}%` : (isLimitReached ? ` (${limit})` : `
        (${remainingCount} ${$t('fileUpload.remaining')})`)) }}
      </el-button>
    </div>

    <!-- 拖拽模式 -->
    <div v-else-if="mode === 'drag'" class="drag-mode" :class="{
      'drag-over': isDragOver,
      'uploading': uploading,
      'disabled': isLimitReached || uploading
    }" @click="handleDragAreaClick" @drop="handleDrop" @dragover="handleDragOver" @dragenter="handleDragEnter"
      @dragleave="handleDragLeave">
      <div class="drag-content">
        <div class="drag-icon">
          <i v-if="!uploading" class="el-icon-upload"></i>
          <i v-else class="el-icon-loading"></i>
        </div>
        <div class="drag-text">
          <p class="drag-title">
            <span v-if="!uploading">{{ $t('fileUpload.dragOrClick') }}</span>
            <span v-else>{{ $t('fileUpload.uploading') }} {{ uploadProgress }}%</span>
          </p>
          <p class="drag-hint" v-if="!uploading">
            {{ $t('fileUpload.supportedFormats') }} ({{ remainingCount }} {{ $t('fileUpload.remaining') }})
          </p>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input ref="fileInput" type="file" :accept="accept" :multiple="multiple && !isLimitReached" style="display: none"
      @change="handleFileSelect" />

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" :show-text="true" :format="formatProgress"></el-progress>
      <div class="upload-info" v-if="currentFile">
        <p>{{ $t('fileUpload.uploadingFile') }}: {{ currentFile.name }}</p>
        <p>{{ $t('fileUpload.chunkProgress') }}: {{ currentChunk }}/{{ chunks }}</p>
        <p>{{ $t('fileUpload.fileSize') }}: {{ formatBytes(currentFile.size) }}</p>
        <p v-if="fileMd5">{{ $t('fileUpload.fileMd5') }}: {{ fileMd5.substring(0, 8) }}...</p>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div class="file-list-header">
        <span>{{ $t('fileUpload.uploadedFiles') }} ({{ fileList.length }}/{{ limit }})</span>
        <el-button v-if="fileList.length > 0" type="text" size="mini" @click="clearAll">
          {{ $t('fileUpload.clearAll') }}
        </el-button>
      </div>
      <div class="file-item" v-for="(file, index) in fileList" :key="index">
        <div class="file-info">
          <i :class="getFileIcon(file.name)" class="file-icon"></i>
          <span class="file-name" :title="file.name">{{ file.name }}</span>
          <span class="file-url" :title="file.url">{{ file.url }}</span>
        </div>
        <div class="file-actions">
          <el-button type="text" size="mini" @click="previewFile(file)" v-if="isImageFile(file.name)">
            {{ $t('fileUpload.preview') }}
          </el-button>
          <el-button type="text" size="mini" @click="downloadFile(file)">
            {{ $t('fileUpload.download') }}
          </el-button>
          <el-button type="text" size="mini" @click="removeFile(index)" style="color: #f56c6c;">
            {{ $t('fileUpload.delete') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog :visible.sync="dialogVisible" append-to-body show-close close-on-click-modal modal-append-to-body
      style="z-index: 9999;" :title="$t('fileUpload.filePreview')">
      <img width="100%" :src="VUE_BASE_UPLOAD + dialogImageUrl" alt="" v-if="isImageFile(previewFileName)" />
      <div v-else class="file-preview">
        <i :class="getFileIcon(previewFileName)" class="preview-icon"></i>
        <p>{{ previewFileName }}</p>
        <p>{{ $t('fileUpload.unsupportedPreview') }}</p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { VUE_BASE_UPLOAD } from "@/api/config";
export default {
  name: 'FileUpload',
  props: {
    value: {
      type: [Array, String],
      default: () => []
    },
    limit: {
      type: Number,
      default: 1
    },
    accept: {
      type: String,
      default: '*/*'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'small'
    },
    // 上传模式：button-按钮模式, drag-拖拽模式
    mode: {
      type: String,
      default: 'button',
      validator: value => ['button', 'drag'].includes(value)
    },
    // 拖拽区域宽度
    dragWidth: {
      type: [Number, String],
      default: '100%'
    },
    // 拖拽区域高度
    dragHeight: {
      type: [Number, String],
      default: 200
    },
    // 上传接口地址
    uploadUrl: {
      type: String,
      default: VUE_BASE_UPLOAD + '/app-store/upload-fd'
    },
    // 最大文件大小 (MB)
    maxSize: {
      type: Number,
      default: 1000
    }
  },
  data() {
    return {
      VUE_BASE_UPLOAD,  // 添加到data中以便模板使用
      fileList: this.formatFileList(this.value),
      dialogVisible: false,
      dialogImageUrl: '',
      previewFileName: '',
      uploading: false,
      uploadProgress: 0,
      currentFile: null,
      chunks: 0,
      currentChunk: 0,
      fileMd5: '',
      CHUNK_SIZE: 5 * 1024 * 1024, // 5MB
      // 拖拽相关
      isDragOver: false,
      dragCounter: 0
    };
  },
  computed: {
    // 检查是否达到限制
    isLimitReached() {
      return this.fileList.length >= this.limit;
    },

    // 剩余可上传数量
    remainingCount() {
      return Math.max(0, this.limit - this.fileList.length);
    },

    // 上传按钮文字 (已移至模板中使用国际化)
  },
  watch: {
    value(newVal) {
      this.fileList = this.formatFileList(newVal);
    }
  },
  mounted() {
    // 动态加载 SparkMD5 库
    this.loadSparkMD5();

    // 初始化时确保fileList正确设置
    this.fileList = this.formatFileList(this.value);
  },
  methods: {
    // 加载 SparkMD5 库
    loadSparkMD5() {
      if (window.SparkMD5) {
        return Promise.resolve();
      }

      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/spark-md5/3.0.2/spark-md5.min.js';
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load SparkMD5'));
        document.head.appendChild(script);
      });
    },

    // 格式化文件列表，支持字符串和数组
    formatFileList(value) {
      if (!value) {
        return [];
      }

      if (typeof value === 'string') {
        const urls = value.split(',').filter(url => url.trim());
        return urls.map((url, index) => ({
          url: url.trim(),
          name: this.getFileNameFromUrl(url.trim()),
          uid: `uid_${index}`
        }));
      }

      if (Array.isArray(value)) {
        return value.map((url, index) => ({
          url,
          name: this.getFileNameFromUrl(url),
          uid: `uid_${index}`
        }));
      }

      return [];
    },

    // 从URL中提取文件名
    getFileNameFromUrl(url) {
      if (!url) return 'unknown';
      const parts = url.split('/');
      return parts[parts.length - 1] || 'unknown';
    },

    // 格式化输出，根据原始value类型返回对应格式
    formatOutput(urls) {
      if (typeof this.value === 'string') {
        return urls.join(',');
      }
      return urls;
    },

    // 点击上传按钮
    handleButtonClick() {
      if (this.isLimitReached) {
        this.$message.warning(`${this.$t('fileUpload.maxFilesReached')} ${this.limit} ${this.$t('fileUpload.filesSelected')}`);
        return;
      }
      this.$refs.fileInput.click();
    },

    // 点击拖拽区域
    handleDragAreaClick() {
      if (this.isLimitReached || this.uploading) {
        return;
      }
      this.$refs.fileInput.click();
    },

    // 拖拽进入
    handleDragEnter(e) {
      e.preventDefault();
      e.stopPropagation();
      this.dragCounter++;
      if (!this.isLimitReached && !this.uploading) {
        this.isDragOver = true;
      }
    },

    // 拖拽离开
    handleDragLeave(e) {
      e.preventDefault();
      e.stopPropagation();
      this.dragCounter--;
      if (this.dragCounter === 0) {
        this.isDragOver = false;
      }
    },

    // 拖拽悬停
    handleDragOver(e) {
      e.preventDefault();
      e.stopPropagation();
      if (!this.isLimitReached && !this.uploading) {
        this.isDragOver = true;
      }
    },

    // 文件放置
    handleDrop(e) {
      e.preventDefault();
      e.stopPropagation();
      this.isDragOver = false;
      this.dragCounter = 0;

      if (this.isLimitReached || this.uploading) {
        return;
      }

      const files = Array.from(e.dataTransfer.files);
      this.processFiles(files);
    },

    // 文件选择
    async handleFileSelect(event) {
      const files = Array.from(event.target.files);
      if (files.length === 0) return;

      this.processFiles(files);

      // 清空文件输入框
      event.target.value = '';
    },

    // 处理文件（统一处理文件选择和拖拽的文件）
    async processFiles(files) {
      if (files.length === 0) return;

      // 检查数量限制
      if (this.fileList.length + files.length > this.limit) {
        this.$message.warning(`${this.$t('fileUpload.maxFilesReached')} ${this.limit} ${this.$t('fileUpload.filesSelected')}，${this.$t('fileUpload.currentlyHas')} ${this.fileList.length} 个`);
        return;
      }

      // 逐个上传文件
      for (let file of files) {
        if (this.fileList.length >= this.limit) break;

        try {
          await this.uploadFile(file);
        } catch (error) {
          console.error('文件上传失败:', error);
          this.$message.error(`${this.$t('fileUpload.uploadFailed')}: ${file.name} - ${error.message}`);
        }
      }
    },

    // 上传单个文件
    async uploadFile(file) {
      // 检查文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize;
      if (!isLtMaxSize) {
        throw new Error(`${this.$t('fileUpload.fileSizeExceeded')} ${this.maxSize}MB`);
      }

      try {
        // 开始分段上传
        const result = await this.startChunkUpload(file);

        console.log("🚀 ~ 上传完成，返回结果:", result);

        // 添加到文件列表
        this.fileList.push({
          name: file.name,
          url: result.url,
          uid: Date.now() + Math.random()
        });

        // 触发更新事件
        this.emitChange();

        this.$message.success(`${file.name} ${this.$t('fileUpload.uploadSuccess')}`);

      } catch (error) {
        console.error('上传失败:', error);
        throw error;
      }
    },

    // 开始分段上传
    async startChunkUpload(file) {
      this.uploading = true;
      this.uploadProgress = 0;
      this.currentFile = file;

      try {
        // 确保 SparkMD5 已加载
        await this.loadSparkMD5();

        const chunks = Math.ceil(file.size / this.CHUNK_SIZE);
        const lastChunkSize = file.size - (chunks - 1) * this.CHUNK_SIZE;

        this.chunks = chunks;
        this.currentChunk = 0;

        console.log(`文件名: ${file.name}`);
        console.log(`文件大小: ${this.formatBytes(file.size)}`);
        console.log(`分块数量: ${chunks}`);
        console.log(`最后分块大小: ${this.formatBytes(lastChunkSize)}`);

        // 计算文件MD5
        console.log(this.$t('fileUpload.calculatingMd5'));
        const fileMd5 = await this.calculateFileMD5(file);
        this.fileMd5 = fileMd5;
        console.log(`文件MD5: ${fileMd5}`);

        // 上传所有分块
        let uploadResult = null;
        for (let i = 0; i < chunks; i++) {
          const start = i * this.CHUNK_SIZE;
          const end = Math.min(start + this.CHUNK_SIZE, file.size);
          const chunk = file.slice(start, end);

          this.currentChunk = i + 1;
          console.log(`${this.$t('fileUpload.uploadingChunk')} ${i + 1}/${chunks} (${this.formatBytes(chunk.size)})`);

          // 计算分块MD5
          const chunkMd5 = await this.calculateChunkMD5(chunk);
          console.log(`${this.$t('fileUpload.chunkProgress')} ${i + 1} MD5: ${chunkMd5}`);

          // 上传分块
          const result = await this.uploadChunk(file, chunk, i, chunks, fileMd5, chunkMd5);
          if (!result.success) {
            throw new Error(`${this.$t('fileUpload.chunkUploadFailed')} ${i + 1}`);
          }

          // 保存最后一个分块的返回结果
          if (result.data) {
            uploadResult = result.data;
          }

          // 更新进度
          const percent = Math.round(((i + 1) / chunks) * 100);
          this.uploadProgress = percent;
        }

        console.log(this.$t('fileUpload.allChunksCompleted'));
        console.log('🚀 ~ 上传结果:', uploadResult);

        // 拼接完整的URL
        let finalPath = '';
        if (uploadResult && uploadResult.path) {
          finalPath = uploadResult.path;
          console.log('🚀 ~ 使用的path:', finalPath);
        } else {
          finalPath = `/u_file/uploads_fd/${file.name}`;
          console.log('🚀 ~ 使用默认path:', finalPath);
        }

        return {
          url: finalPath, // 只返回path部分
          name: file.name,
          status: 'success'
        };

      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
        this.currentFile = null;
        this.chunks = 0;
        this.currentChunk = 0;
        this.fileMd5 = '';
      }
    },

    // 使用SparkMD5计算文件MD5
    calculateFileMD5(file) {
      return new Promise((resolve, reject) => {
        const chunkSize = 2 * 1024 * 1024; // 2MB chunks for hashing
        const chunks = Math.ceil(file.size / chunkSize);
        const spark = new window.SparkMD5.ArrayBuffer();
        let currentChunk = 0;

        const loadNext = () => {
          const start = currentChunk * chunkSize;
          const end = Math.min(start + chunkSize, file.size);
          const reader = new FileReader();

          reader.onload = (e) => {
            spark.append(e.target.result);
            currentChunk++;

            if (currentChunk < chunks) {
              loadNext();
            } else {
              resolve(spark.end());
            }
          };

          reader.onerror = () => {
            reject(new Error(this.$t('fileUpload.fileReadError')));
          };

          reader.readAsArrayBuffer(file.slice(start, end));
        };

        loadNext();
      });
    },

    // 计算分块MD5
    calculateChunkMD5(chunk) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const spark = new window.SparkMD5.ArrayBuffer();
          spark.append(e.target.result);
          resolve(spark.end());
        };
        reader.onerror = () => {
          reject(new Error(this.$t('fileUpload.chunkReadError')));
        };
        reader.readAsArrayBuffer(chunk);
      });
    },

    // 上传分块
    async uploadChunk(file, chunk, chunkIndex, chunks, fileMd5, chunkMd5) {
      const MAX_RETRIES = 3;
      let attempt = 0;

      while (attempt < MAX_RETRIES) {
        try {
          const formData = new FormData();
          formData.append('file', chunk);
          formData.append('name', file.name);
          formData.append('md5', fileMd5);
          formData.append('chunk', chunkIndex);
          formData.append('chunks', chunks);
          formData.append('totalSize', file.size);
          formData.append('chunk_md5', chunkMd5);

          const response = await fetch(this.uploadUrl, {
            method: 'POST',
            body: formData
          });

          const result = await response.json();
          console.log("🚀 ~ 分块上传响应:", result);

          if (result.status !== 200) {
            throw new Error(result.message || this.$t('fileUpload.uploadFailed'));
          }

          // 如果是最后一个分块，返回完整的URL信息
          if (chunkIndex === chunks - 1) {
            return {
              success: true,
              data: result.data
            };
          }

          return { success: true };

        } catch (error) {
          attempt++;
          if (attempt >= MAX_RETRIES) {
            throw error;
          }

          // 指数退避
          await new Promise(resolve =>
            setTimeout(resolve, 1000 * Math.pow(2, attempt - 1))
          );
        }
      }
    },

    // 格式化字节显示
    formatBytes(bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const dm = decimals < 0 ? 0 : decimals;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },

    // 格式化进度显示
    formatProgress(percentage) {
      return `${percentage}% (${this.currentChunk}/${this.chunks})`;
    },

    // 获取文件图标
    getFileIcon(fileName) {
      if (!fileName) return 'el-icon-document';

      const ext = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        // 图片
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'bmp': 'el-icon-picture',
        'webp': 'el-icon-picture',
        'svg': 'el-icon-picture',

        // 视频
        'mp4': 'el-icon-video-camera',
        'avi': 'el-icon-video-camera',
        'mov': 'el-icon-video-camera',
        'wmv': 'el-icon-video-camera',
        'flv': 'el-icon-video-camera',
        'mkv': 'el-icon-video-camera',

        // 音频
        'mp3': 'el-icon-headset',
        'wav': 'el-icon-headset',
        'flac': 'el-icon-headset',
        'aac': 'el-icon-headset',

        // 文档
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-document',
        'xlsx': 'el-icon-document',
        'ppt': 'el-icon-document',
        'pptx': 'el-icon-document',
        'txt': 'el-icon-document',

        // 压缩包
        'zip': 'el-icon-folder',
        'rar': 'el-icon-folder',
        '7z': 'el-icon-folder',
        'tar': 'el-icon-folder',
        'gz': 'el-icon-folder'
      };

      return iconMap[ext] || 'el-icon-document';
    },

    // 判断是否为图片文件
    isImageFile(fileName) {
      if (!fileName) return false;
      const ext = fileName.split('.').pop().toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext);
    },

    // 预览文件
    previewFile(file) {
      this.previewFileName = file.name;
      if (this.isImageFile(file.name)) {
        this.dialogImageUrl = file.url;
      }
      this.dialogVisible = true;
    },

    // 下载文件
    downloadFile(file) {
      const link = document.createElement('a');
      link.href = VUE_BASE_UPLOAD + file.url;  // 拼接完整URL
      link.download = file.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 删除文件
    removeFile(index) {
      this.fileList.splice(index, 1);
      this.emitChange();
      this.$message.success(this.$t('fileUpload.deleteSuccess'));
    },

    // 清空所有文件
    clearAll() {
      this.$confirm(this.$t('fileUpload.confirmClearAll'), this.$t('fileUpload.confirmClearAllTitle'), {
        confirmButtonText: this.$t('fileUpload.confirm'),
        cancelButtonText: this.$t('fileUpload.cancel'),
        type: 'warning'
      }).then(() => {
        this.fileList = [];
        this.emitChange();
        this.$message.success(this.$t('fileUpload.clearAllSuccess'));
      }).catch(() => { });
    },

    // 触发变更事件
    emitChange() {
      const urls = this.fileList.map(file => file.url);
      console.log("🚀 ~ file: index.vue:724 ~ urls:", urls)
      this.$emit('input', this.formatOutput(urls));
      this.$emit('change', this.formatOutput(urls));
    }
  }
};
</script>

<style scoped lang="scss">
.file-upload-segment {

  // 按钮模式样式
  .button-mode {
    display: inline-block;
  }

  // 拖拽模式样式
  .drag-mode {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
      background: #f0f8ff;
    }

    &.drag-over {
      border-color: #409eff;
      background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
      transform: scale(1.02);
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
    }

    &.uploading {
      border-color: #409eff;
      background: #f0f8ff;
      cursor: not-allowed;

      .drag-icon i {
        color: #409eff;
        animation: rotate 2s linear infinite;
      }
    }

    &.disabled {
      background: #f5f7fa;
      border-color: #e4e7ed;
      cursor: not-allowed;

      &:hover {
        border-color: #e4e7ed;
        background: #f5f7fa;
        transform: none;
      }

      .drag-content {
        .drag-icon i {
          color: #c0c4cc;
        }

        .drag-text {
          .drag-title {
            color: #c0c4cc;
          }

          .drag-hint {
            color: #c0c4cc;
          }
        }
      }
    }

    .drag-content {
      text-align: center;
      padding: 20px;

      .drag-icon {
        margin-bottom: 16px;

        i {
          font-size: 48px;
          color: #c0c4cc;
          transition: all 0.3s ease;
        }
      }

      .drag-text {
        .drag-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin: 0 0 8px 0;
          transition: color 0.3s ease;
        }

        .drag-hint {
          font-size: 14px;
          color: #909399;
          margin: 0;
          line-height: 1.4;
        }
      }
    }
  }

  .upload-progress {
    margin-top: 15px;

    .upload-info {
      margin-top: 10px;
      font-size: 12px;
      color: #666;
      font-family: monospace;
      background: #f5f7fa;
      padding: 10px;
      border-radius: 4px;

      p {
        margin: 3px 0;
      }
    }
  }

  .file-list {
    margin-top: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    max-width: 100%;

    .file-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      background: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-size: 14px;
      font-weight: 500;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f9f9f9;
      }

      .file-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;

        .file-icon {
          font-size: 18px;
          color: #409eff;
          margin-right: 10px;
          flex-shrink: 0;
          align-self: flex-start;
        }

        .file-name {
          font-weight: 500;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 13px;
        }

        .file-url {
          font-size: 11px;
          color: #909399;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        // 窄空间适配 - 图标与文本分开显示
        &.narrow-space {
          .file-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            margin: 0;
            font-size: 16px;
          }

          .file-name,
          .file-url {
            margin-left: 35px;
          }
        }
      }

      .file-actions {
        display: flex;
        gap: 2px;
        flex-shrink: 0;
        flex-wrap: wrap;

        .el-button {
          padding: 2px 4px;
          font-size: 11px;
          min-height: auto;

          &.el-button--text {
            padding: 2px 4px;
          }
        }
      }
    }
  }

  .file-preview {
    text-align: center;
    padding: 40px 20px;

    .preview-icon {
      font-size: 64px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }

    p {
      margin: 8px 0;
      color: #606266;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 响应式设计 - 针对窄容器优化
@media (max-width: 300px) {
  .file-upload-segment {
    .file-list {
      .file-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 8px 10px;
        position: relative;

        .file-info {
          width: 100%;
          margin-bottom: 8px;
          position: relative;

          .file-icon {
            position: absolute;
            left: 0;
            top: 2px;
            margin: 0;
            font-size: 14px;
          }

          .file-name {
            margin-left: 25px;
            margin-bottom: 2px;
            font-size: 12px;
            font-weight: 500;
          }

          .file-url {
            margin-left: 25px;
            font-size: 10px;
            color: #999;
          }
        }

        .file-actions {
          width: 100%;
          justify-content: flex-end;
          gap: 4px;

          .el-button {
            font-size: 10px;
            padding: 1px 3px;
            line-height: 1.2;
          }
        }
      }
    }
  }
}

// 针对有 narrow-container 类的容器
.narrow-container .file-upload-segment {
  .file-list {
    .file-item {
      flex-direction: column;
      align-items: flex-start;
      padding: 8px 10px;
      position: relative;

      .file-info {
        width: 100%;
        margin-bottom: 8px;
        position: relative;

        .file-icon {
          position: absolute;
          left: 0;
          top: 2px;
          margin: 0;
          font-size: 14px;
        }

        .file-name {
          margin-left: 25px;
          margin-bottom: 2px;
          font-size: 12px;
          font-weight: 500;
        }

        .file-url {
          margin-left: 25px;
          font-size: 10px;
          color: #999;
        }
      }

      .file-actions {
        width: 100%;
        justify-content: flex-end;
        gap: 4px;

        .el-button {
          font-size: 10px;
          padding: 1px 3px;
          line-height: 1.2;
        }
      }
    }
  }
}

// 美化预览对话框
::v-deep .el-dialog {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .el-dialog__body {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;

    img {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      max-height: 70vh;
      object-fit: contain;
    }
  }
}
</style>