<template>
  <el-time-picker
    v-model="selectedTime"
    :style="TimePickerStyle"
    :is-range="isRange"
    :range-separator="rangeSeparator"
    :value-format="valueFormat"
    :default-value="defaultValue"
    :format="format"
    :arrow-control="arrowControl"
    :start-placeholder="$t('acc.medal.beginTime')"
    :end-placeholder="$t('acc.medal.endTime')"
    @change="handleChange"
  >
  </el-time-picker>
</template>

<script>
export default {
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    value: {
      type: [String, Array],
      default: null
    },
    isRange: {
      type: Boolean,
      default: true
    },
    arrowControl: {
      type: Boolean,
      default: false
    },
    rangeSeparator: {
      type: String,
      default: " ～ "
    },
    valueFormat: {
      type: String,
      default: "HH:mm"
    },
    defaultValue: {
      type: Array,
      default: () => [new Date(2024, 9, 10, 9, 0), new Date(2024, 9, 10, 18, 30)]
    },
    format: {
      type: String,
      default: "HH:mm"
    },
    Width: {
      type: String,
      default: "100%"
    },
    isValueArray: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectedTime: this.value || null
    };
  },
  computed: {
    TimePickerStyle() {
      return {
        width: this.Width
      };
    }
  },
  watch: {
    value(newVal) {
      if (!newVal) {
        this.selectedTime = null;
        return;
      }
      
      if (Array.isArray(newVal)) {
        this.selectedTime = newVal;
      } else if (typeof newVal === 'string') {
          if (newVal.indexOf("-") !== -1) {
            this.selectedTime = newVal.split("-");
        } else if (newVal.indexOf(this.rangeSeparator) !== -1) {
            this.selectedTime = newVal.split(this.rangeSeparator);
        } else {
          this.selectedTime = [newVal, newVal];
        }
      } else {
        this.selectedTime = null;
      }
    }
  },
  methods: {
    handleChange(e) {
      this.$emit("change", e ? e.join(this.rangeSeparator) : null);
    }
  }
};
</script>
