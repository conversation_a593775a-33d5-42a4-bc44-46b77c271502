<template>
  <FileUpload 
    v-model="fileValue" 
    :limit="limit" 
    :accept="accept" 
    :css="css" 
    @input="handleFileChange"
    @change="handleFileChange"
  />
</template>

<script>
import FileUpload from "@/components/FileUpload";
export default {
  name: "DrUpload",
  components: { FileUpload },
  props: {
    value: {
      type: [String, Array],
      default: ""
    },
    limit: {
      type: Number,
      default: 1
    },
    drag: "",
    css: "",
    listType: {
      type: String,
      default: "picture-card"
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    pclass: "",
    showFileList: {
      type: <PERSON>olean,
      default: true
    },
    accept: {
      type: String,
      default: '*/*'
    }
  },
  data() {
    return {
      fileValue: this.value
    };
  },
  watch: {
    value(newVal) {
      this.fileValue = newVal;
    }
  },
  methods: {
    handleFileChange(value) {
      // 为了保持与原有页面的兼容性，需要模拟原来的 handleReturnData 逻辑
      // 原来的逻辑总是返回字符串格式（逗号分隔的URL）
      const compatibleValue = this.convertToCompatibleFormat(value);
      
      this.$emit("input", compatibleValue);
      this.$emit("change", compatibleValue);
      
      // 兼容原有的事件名称，一些页面可能监听这些事件
      this.$emit("uploadSuccess", null, null, this.createFileListFormat(value));
    },
    
    // 转换为兼容格式，模拟原来的 handleReturnData 逻辑
    convertToCompatibleFormat(value) {
      if (!value) return "";
      
      // 如果已经是字符串格式，直接返回
      if (typeof value === 'string') {
        return value;
      }
      
      // 如果是数组格式，转换为字符串
      if (Array.isArray(value)) {
        return value.join(',');
      }
      
      return "";
    },
    
    // 创建符合原来期望的 fileList 格式，供 uploadSuccess 事件使用
    createFileListFormat(value) {
      if (!value) return [];
      
      let urls = [];
      if (typeof value === 'string') {
        urls = value.split(',').filter(url => url.trim());
      } else if (Array.isArray(value)) {
        urls = value;
      }
      
      return urls.map(url => ({
        url: url.trim(),
        name: this.getFileNameFromUrl(url.trim()),
        status: 'success'
      }));
    },
    
    // 从URL中提取文件名
    getFileNameFromUrl(url) {
      if (!url) return 'unknown';
      const parts = url.split('/');
      return parts[parts.length - 1] || 'unknown';
    }
  }
};
</script>

<style lang="scss" scoped>
// 保持原有样式以确保兼容性，但大部分功能现在由 FileUpload 组件提供
.dr-upload {
  // 这里可以添加一些特定的样式覆盖，如果需要的话
}
</style>
