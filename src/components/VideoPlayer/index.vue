<template>
  <div class="video-player-wrapper">
    <!-- 视频封面 -->
    <div class="video-cover" :style="{ width: width + 'px', height: height + 'px' }" @click="openVideoModal">
      <img v-if="cover" :src="host + cover" :alt="alt" :style="{ width: width + 'px', height: height + 'px' }"
        class="cover-image" />
      <div v-else class="no-cover" :style="{ width: width + 'px', height: height + 'px' }">
        <i class="el-icon-video-play"></i>
      </div>
      <div class="play-button">
        <i class="el-icon-video-play"></i>
      </div>
    </div>

    <!-- 视频播放弹窗 -->
    <el-dialog :visible.sync="videoModalVisible" :width="modalWidth" :before-close="closeVideoModal" class="video-modal"
      center>
      <div slot="title" class="video-modal-title">
        {{ title || '视频播放' }}
      </div>

      <div class="video-container" v-if="videoModalVisible">
        <video ref="videoPlayer" :src="host + src" :poster="cover" controls 
          preload="metadata" @loadedmetadata="onPlayerReady">
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import 'video.js/dist/video-js.css'
import { VUE_BASE_UPLOAD } from "@/api/config"
export default {
  name: 'VideoPlayer',
  props: {
    // 视频地址
    src: {
      type: String,
      required: true
    },
    // 封面图片
    cover: {
      type: String,
      default: ''
    },
    // 封面宽度
    width: {
      type: Number,
      default: 120
    },
    // 封面高度
    height: {
      type: Number,
      default: 80
    },
    // 视频标题
    title: {
      type: String,
      default: ''
    },
    // 图片alt属性
    alt: {
      type: String,
      default: '视频封面'
    },
    // 弹窗宽度
    modalWidth: {
      type: String,
      default: '800px'
    },
    // 视频播放宽度
    videoWidth: {
      type: String,
      default: '100%'
    },
    // 视频播放高度
    videoHeight: {
      type: String,
      default: '450px'
    }
  },
  data() {
    return {
      host: VUE_BASE_UPLOAD,
      videoModalVisible: false
    }
  },
  methods: {
    // 打开视频弹窗
    openVideoModal() {
      if (!this.src) {
        this.$message.warning('视频地址不能为空')
        return
      }
      this.videoModalVisible = true
      this.$emit('open')
      // 等待DOM更新后自动播放
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.play().catch(err => {
            console.log('自动播放失败:', err)
          })
        }
      })
    },

    // 关闭视频弹窗
    closeVideoModal() {
      this.videoModalVisible = false
      // 停止播放
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
        this.$refs.videoPlayer.currentTime = 0
      }
      this.$emit('close')
    },

    // 播放器准备就绪
    onPlayerReady() {
      this.$emit('ready', this.$refs.videoPlayer)
    },

    // 获取播放器实例
    getPlayer() {
      return this.$refs.videoPlayer || null
    }
  }
}
</script>

<style lang="scss" scoped>
.video-player-wrapper {
  display: inline-block;

  .video-cover {
    position: relative;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;

      .play-button {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
      }
    }

    .cover-image {
      display: block;
      object-fit: cover;
    }

    .no-cover {
      background: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #c0c4cc;
      font-size: 24px;
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      opacity: 0.8;
      transition: all 0.3s;
    }
  }
}

.video-modal {
  .video-modal-title {
    font-size: 16px;
    font-weight: 500;
  }

  .video-container {
    width: 100%;
    max-height: 500px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;

    video {
      width: 100%;
      height: auto;
      max-width: 100%;
      max-height: 500px;
      object-fit: contain;
    }
  }
}

// 覆盖Element UI弹窗样式
::v-deep .video-modal {
  .el-dialog {
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
  }
  
  .el-dialog__header {
    padding: 20px 20px 10px;
  }

  .el-dialog__body {
    padding: 10px 20px 30px;
  }
}
</style>