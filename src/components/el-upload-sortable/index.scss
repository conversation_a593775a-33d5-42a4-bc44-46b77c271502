.upload-queue {
  .video-box {
    width: 100%;
    height: 100%;
  }
  .el-upload-list__item:hover {
    .el-upload-list__item-actions {
      display: block;
      opacity: 1;
    }
  }

  .el-upload--picture-card .el-upload--text {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.video-box {
  .el-dialog {
    width: 900px;
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
      display: none;
    }
    
    .el-dialog__body {
      width: inherit;
      height: 650px;
      padding: 0;
      video {
        width: inherit;
        height: inherit;
        // object-fit: fill;
      }
    }
  }
}