<template>
  <div class="coust-style">
    <!-- 模块4 -->
    <div class="post-form model-wrap margin-top-sm">
      <div class="model-wrap-style">
        <div class="title" style="padding-left: 30px">
          <div>{{ title }} <slot name="title" /></div>
        </div>
      </div>
      <div class="model-content" style="padding: 30px 20px">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ModalStyle",
  props: {
    title: {}
  }
};
</script>

<style lang="scss">
.coust-style {
  .el-form-item {
    margin-bottom: 10px;
  }
  .el-form-item__label {
    color: #999;
  }
}
.post-form {
  font-size: 14px;
  .el-input--medium .el-input__inner,
  .el-form-item--medium .el-range-editor--small.el-input__inner,
  .el-form-item--medium .el-range-editor--medium.el-input__inner {
    line-height: 40px;
    height: 40px;
  }

  .el-input--medium .el-range-editor--small .el-range-separator {
    line-height: 32px;
  }
  .el-select,
  .el-input {
    width: 100%;
  }
  .dialog-footer {
    text-align: center;
  }
}
.model-wrap {
  border: 1px solid rgba(236, 241, 248, 1);
  .el-form-item {
    margin-bottom: 30px;
  }
  .model-wrap-style {
    .title {
      text-align: center;
      font-size: 14px;
      height: 50px;
      line-height: 50px;
      padding-left: 40px;
      background: rgba(248, 250, 253, 1);
      border-bottom: 1px solid rgba(236, 241, 248, 1);
    }
    .model-content {
      padding: 30px 30px 8px 0;
    }
    .el-range-separator {
      line-height: 48px;
    }
  }
  .el-col {
    margin-bottom: 10px;
    span {
      display: inline-block;
      min-width: 80px;
      color: #666;
      &:first-child {
        color: #111;
      }
    }
  }
}
</style>
