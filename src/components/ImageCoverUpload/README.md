# ImageCoverUpload 组件使用说明

## 功能特性

- 支持单图和多图上传
- 支持数组和字符串格式的 value
- 支持分段上传大文件
- 自动计算文件MD5
- 支持拖拽排序
- 响应式设计
- **自动拼接服务器返回的域名和路径为完整URL**

## 基本用法

### 单图上传

```vue
<template>
  <div>
    <ImageCoverUpload 
      v-model="singleImage" 
      :limit="1"
      @change="handleImageChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      singleImage: '' // 字符串格式
    }
  },
  methods: {
    handleImageChange(urls) {
      console.log('图片更新:', urls);
    }
  }
}
</script>
```

### 多图上传

```vue
<template>
  <div>
    <ImageCoverUpload 
      v-model="multipleImages" 
      :limit="5"
      @change="handleImagesChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 数组格式：['url1', 'url2', 'url3']
      multipleImages: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg']
    }
  },
  methods: {
    handleImagesChange(urls) {
      console.log('图片数组更新:', urls);
      // urls 格式: ['url1', 'url2', 'url3']
    }
  }
}
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | Array/String | [] | 图片URL数组或逗号分隔的字符串 |
| limit | Number | 1 | 最大上传数量 |
| width | Number/String | 140 | 上传框宽度 |
| height | Number/String | 140 | 上传框高度 |
| uploadMode | String | 'chunk' | 上传模式 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | urls | 图片URL数组或字符串更新时触发 |
| change | urls | 图片变化时触发 |
| upload-success | response, file, fileList | 上传成功时触发 |
| upload-error | error, file, fileList | 上传失败时触发 |

## 数据格式说明

### 输入格式
- **数组格式**: `['url1', 'url2', 'url3']`
- **字符串格式**: `'url1,url2,url3'`

### 输出格式
- 如果输入是数组，输出也是数组
- 如果输入是字符串，输出也是字符串（逗号分隔）

## 服务器响应处理

组件会自动处理服务器返回的数据格式，并拼接完整的URL：

### 服务器响应格式
```json
{
  "status": 200,
  "message": "",
  "data": {
    "domain": "http://binyo.net",
    "path": ["/u_file/app/20/fee8bd6b0f."]
  }
}
```

### URL拼接逻辑
- 自动去除域名末尾的斜杠
- 确保路径以斜杠开头
- 拼接结果：`http://binyo.net/u_file/app/20/fee8bd6b0f.`

### 示例
```javascript
// 服务器返回
{
  domain: "http://binyo.net",
  path: ["/u_file/app/20/fee8bd6b0f."]
}

// 拼接后的完整URL
"http://binyo.net/u_file/app/20/fee8bd6b0f."
```

## 注意事项

1. 组件会自动处理数据格式转换
2. 上传成功后会自动更新 value
3. 删除图片时会自动更新 value
4. 支持拖拽排序（需要配置）
5. 大文件会自动分段上传
6. **自动处理服务器返回的域名和路径拼接**
7. 如果服务器没有返回数据，会使用默认URL格式 