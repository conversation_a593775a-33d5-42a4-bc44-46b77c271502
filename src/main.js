import Vue from "vue";
import Cookies from "js-cookie";
import i18n from "@/lang";
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive";
import MainComponent from "@/utils/mainOperation/mainComponent";
import PerviewImg from "@/components/PreviewImg";
import "@/utils/mainOperation/mainFn";

import "normalize.css/normalize.css"; // a modern alternative to CSS resets 1
import "./assets/styles/element-variables.scss"; // Element UI theme variables (must be imported before Element UI)
import Element from "element-ui"; // Import Element UI after theme variables
import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/ruoyi.scss"; // ruoyi css
import "@/assets/iconfont/iconfont.css";
import "./assets/icons"; // icon
import "./permission"; // permission control

import "viewerjs/dist/viewer.css";
import Vue<PERSON>iewer from "v-viewer";
Vue.use(VueViewer, {
  defaultOptions: {
    zIndex: 9999
  }
});

Vue.use(directive);
Vue.use(MainComponent);
Vue.use(PerviewImg);
Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value),
  size: Cookies.get("size") || "mini" // set element-ui default size
});

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  i18n,
  render: h => h(App)
});
