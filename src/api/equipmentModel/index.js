import request from '@/utils/request'

// 获取头盔的型号
export function helmetModelList(params) {
  return request({
    url: '/regulation/equip/model/list',
    method: 'get',
    params
  })
}

// 新增头盔型号
export function helmetModelCreate(data) {
  return request({
    url: '/regulation/equip/model/create',
    method: 'post',
    data
  })
}

// 禁用、启用
export function helmetModelAuth(data) {
  return request({
    url: '/regulation/equip/model/auth',
    method: 'put',
    data
  })
}

// 修改型号
export function helmetModelEdit(data) {
  return request({
    url: '/regulation/equip/model/edit',
    method: 'put',
    data
  })
}

// 型号详情
export function helmetModelDetail(id) {
  return request({
    url: '/regulation/equip/model/detail/' + id,
    method: 'get'
  })
}

// 智能头盔信息
export function helmetList(params) {
  return request({
    url: '/helmet/helmet/list',
    method: 'get',
    params
  })
}

// 解绑智能头盔
export function helmetDelBind({ id, userId }) {
  return request({
    url: '/helmet/helmet/del/bind/' + id,
    method: 'post',
    data: { userId }
  })
}

