import request from '@/utils/request'

// 获取头盔的型号
export function helmetModelList(params) {
  return request({
    url: '/regulation/dvr/model/list',
    method: 'get',
    params
  })
}

// 新增头盔型号
export function helmetModelCreate(data) {
  return request({
    url: '/regulation/dvr/model/create',
    method: 'post',
    data
  })
}

// 禁用、启用
export function helmetModelAuth(data) {
  return request({
    url: '/regulation/dvr/model/auth',
    method: 'put',
    data
  })
}

// 修改型号
export function helmetModelEdit(data) {
  return request({
    url: '/regulation/dvr/model/edit',
    method: 'put',
    data
  })
}

// 型号详情
export function helmetModelDetail(id) {
  return request({
    url: '/regulation/dvr/model/detail/' + id,
    method: 'get'
  })
}



/**
 * iot
 */



/**
 * 头盔
 */

export function helmetList(params) {
  return request({
    url: '/helmet/helmet/list',
    method: 'get',
    params
  })
}

export function helmetDelBind(data) {
  return request({
    url: `/helmet/helmet/del/bind`,
    method: 'post',
    data
  })
}


/**
 * 雷达
 */

export function radarList(params) {
  return request({
    url: '/radar/radar/list',
    method: 'get',
    params
  })
}

export function radarDelBind(data) {
  return request({
    url: `/radar/radar/del/bind`,
    method: 'put',
    data
  })
}



/**
 * 运动相机
 */

export function cameraList(params) {
  return request({
    url: '/camera/camera/list',
    method: 'get',
    params
  })
}

export function cameraDelBind(data) {
  return request({
    url: '/camera/camera/del/bind',
    method: 'put',
    data
  })
}


/**
 * 按键
 */

export function wireLessList(params) {
  return request({
    url: '/wireless/key/list',
    method: 'get',
    params
  })
}

export function wireLessDelBind(data) {
  return request({
    url: '/wireless/key/del/bind',
    method: 'put',
    data
  })
}
