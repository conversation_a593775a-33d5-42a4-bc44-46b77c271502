/*
 * @Author: your name
 * @Date: 2021-04-25 11:59:58
 * @LastEditTime: 2021-05-11 17:10:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /web_bikewise_pro/src/api/system/update.js
 */
import request from '@/utils/request'

// 控制器升级
export function listSoftUpdate(query) {
  return request({
    url: '/sys/ctrl/list',
    method: 'get',
    params: query
  })
}

// 启用/禁用
export function authSoftUpdate(data) {
  return request({
    url: '/sys/ctrl/auth',
    method: 'put',
    data: data
  })
}

// 创建
export function addSoftUpdate(data) {
  return request({
    url: '/sys/ctrl',
    method: 'post',
    data: data
  })
}

// 编辑
export function editSoftUpdate(data) {
  return request({
    url: '/sys/ctrl',
    method: 'put',
    data: data
  })
}

//删除
export function editSoftDelete(data) {
  return request({
    url: '/sys/ctrl/del',
    method: 'delete',
    data: data
  })
}


export function softListGroup(data) {
  return request({
    url: '/sys/ctrl/list/group',
    method: 'get',
    params: data
  })
}

/**
 * 更新审核状态
 * @param {*} data
 * @returns
 */
export function changeState(data) {
  return request({
    url: '/sys/ctrl/state',
    method: 'put',
    data
  })
}


/**
 * 设备升级
 */

export function listDeviceUpdate(params) {
  return request({
    url: '/update/list',
    method: 'get',
    params
  })
}

export function authDeviceUpdate(data) {
  return request({
    url: '/update/auth',
    method: 'put',
    data
  })
}

export function updateSave(data) {
  return request({
    url: '/update/save',
    method: 'post',
    data
  })
}

export function editDeviceUpdate(data) {
  return request({
    url: '/update/update',
    method: 'put',
    data
  })
}

export function editDeviceDelete(data) {
  return request({
    url: '/update/delete',
    method: 'delete',
    data
  })
}


export function listDeviceGroup(params) {
  return request({
    url: '/update/list/group',
    method: 'get',
    params
  })
}

export function changeDeviceState(data) {
  return request({
    url: '/update/state',
    method: 'put',
    data
  })
} 