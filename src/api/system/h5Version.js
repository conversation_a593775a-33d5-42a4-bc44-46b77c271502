/*
 * H5 version management API functions
 */
import request from '@/utils/request'

// H5 version list
export function listH5Version(query) {
  return request({
    url: '/version/list',
    method: 'get',
    params: query
  })
}

// Enable/Disable H5 version
export function authH5Version(data) {
  return request({
    url: '/version/auth',
    method: 'put',
    data: data
  })
}

// Add new H5 version
export function addH5Version(data) {
  return request({
    url: '/version/save',
    method: 'post',
    data: data
  })
}

// Edit H5 version
export function editH5Version(data) {
  return request({
    url: '/version/update',
    method: 'put',
    data: data
  })
}

// Delete H5 version
export function deleteH5Version(data) {
  return request({
    url: '/version/delete',
    method: 'delete',
    data: data
  })
}

// Get H5 version details
export function getH5VersionInfo(id) {
  return request({
    url: `/version/info/${id}`,
    method: 'get'
  })
}
