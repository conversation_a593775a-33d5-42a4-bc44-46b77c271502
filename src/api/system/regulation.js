/*
 * @Author: your name
 * @Date: 2021-04-25 11:59:58
 * @LastEditTime: 2021-05-11 17:10:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /bikewise-pro-web/src/api/system/update.js
 */
import request from '@/utils/request'

// 码表,Android 升级列表
export function listSoftUpdate(query) {
  return request({
    url: '/regulation/version/list',
    method: 'get',
    params: query
  })
}

// 启用/禁用
export function authSoftUpdate(data) {
  return request({
    url: '/regulation/version/auth',
    method: 'put',
    data: data
  })
}

// 创建
export function addSoftUpdate(data) {
  return request({
    url: '/regulation/version',
    method: 'post',
    data: data
  })
}

// 编辑
export function editSoftUpdate(data) {
  return request({
    url: '/regulation/version/edit',
    method: 'put',
    data: data
  })
}

//删除
export function editSoftDelete(data) {
  return request({
    url: '/sys/del',
    method: 'delete',
    data: data
  })
}


 export function softListGroup(data) {
  return request({
    url: '/sys/soft/list/group',
    method: 'get',
    params: data
  })
}

/**
 * 更新审核状态
 * @param {*} data
 * @returns
 */
export function changeState(data) {
  return request({
    url: '/sys/soft/state',
    method: 'put',
    data
  })
}
