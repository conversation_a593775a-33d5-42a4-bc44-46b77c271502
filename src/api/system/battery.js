import request from '@/utils/request'

// 电池升级
export function listBatteryUpdate(params) {
  return request({
    url: '/battery/update/list',
    method: 'get',
    params
  })
}

// 启用/禁用
export function authBatteryUpdate(data) {
  return request({
    url: '/battery/update/auth',
    method: 'put',
    data
  })
}

// 创建
export function addBatteryUpdate(data) {
  return request({
    url: '/battery/update',
    method: 'post',
    data
  })
}

// 编辑
export function editBatteryUpdate(data) {
  return request({
    url: '/battery/update',
    method: 'put',
    data
  })
}

//删除
export function editBatteryDelete(data) {
  return request({
    url: '/battery/update/del',
    method: 'delete',
    data
  })
}

// 历史列表
export function softListGroup(data) {
  return request({
    url: '/battery/update/list/group',
    method: 'get',
    params: data
  })
}

/**
 * 更新审核状态
 * @param {*} data
 * @returns
 */
export function changeState(data) {
  return request({
    url: '/battery/update/state',
    method: 'put',
    data
  })
}


/**
 * 设备升级
 */

export function listDeviceUpdate(params) {
  return request({
    url: '/update/list',
    method: 'get',
    params
  })
}

export function authDeviceUpdate(data) {
  return request({
    url: '/update/auth',
    method: 'put',
    data
  })
}

export function updateSave(data) {
  return request({
    url: '/update/save',
    method: 'post',
    data
  })
}

export function editDeviceUpdate(data) {
  return request({
    url: '/update/update',
    method: 'put',
    data
  })
}

export function editDeviceDelete(data) {
  return request({
    url: '/update/delete',
    method: 'delete',
    data
  })
}


export function listDeviceGroup(params) {
  return request({
    url: '/update/list/group',
    method: 'get',
    params
  })
}

export function changeDeviceState(data) {
  return request({
    url: '/update/state',
    method: 'put',
    data
  })
} 