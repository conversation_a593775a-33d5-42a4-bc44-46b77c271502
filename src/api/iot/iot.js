import request from '@/utils/request'

// IOt订单
export function orderList(params) {
  return request({
    url: '/order/list',
    method: 'get',
    params
  })
}

// 详情
export function orderInfo(id) {
  return request({
    url: `/order/info/${id}`,
    method: 'get'
  })
}

/**
 * IOT 资费
 */

export function productList(params) {
  return request({
    url: '/product/list',
    method: 'get',
    params
  })
}

export function productSave(data) {
  return request({
    url: '/product/save',
    method: 'post',
    data
  })
}

export function productUpdate(data) {
  return request({
    url: '/product/update',
    method: 'put',
    data
  })
}

export function productAuth(data) {
  return request({
    url: '/product/auth',
    method: 'put',
    data
  })
}

export function productDetail(id) {
  return request({
    url: `/product/info/${id}`,
    method: 'get'
  })
}

export function productDelete(data) {
  return request({
    url: '/product/delete',
    method: 'delete',
    data
  })
}


/**
 * IOT 权益
 */

export function tightsList(params) {
  return request({
    url: '/tights/list',
    method: 'get',
    params
  })
}

export function tightsAuth(data) {
  return request({
    url: '/tights/auth',
    method: 'put',
    data
  })
}

/**
 * IOT诊断记录
 */
export function iotBikeDiagnosis(params) {
  return request({
    url: '/iot/bike/diagnosis',
    method: 'get',
    params
  })
}


/**
 * IOT报警记录
 */
export function iotBikeAlarm(params) {
  return request({
    url: '/iot/bike/alarm',
    method: 'get',
    params
  })
}


/**
 * IOT日志记录
 */
export function iotDeviceLogList(params) {
  return request({
    url: '/device/log/list',
    method: 'get',
    params
  })
}


/**
 * IOT设备列表
 */
export function iotDeviceList(params) {
  return request({
    url: '/device/list',
    method: 'get',
    params
  })
}



// 设备控制
export function equipList(params) {
  return request({
    url: '/regulation/equip/list',
    method: 'get',
    params
  })
}

// 解绑
export function equipDelBind({ id, bikeId, type }) {
  return request({
    url: `/regulation/equip/del/bind/${id}?bikeId=${bikeId}&type=${type}`,
    method: 'post'
  })
}
