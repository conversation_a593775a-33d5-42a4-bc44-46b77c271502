import request from '@/utils/request'

// 获取设备页广告列表
export function getDeviceAdList(params) {
  return request({
    url: '/device/ad/list',
    method: 'get',
    params
  })
}

// 获取设备页广告详情
export function getDeviceAdInfo(id) {
  return request({
    url: `/device/ad/info/${id}`,
    method: 'get'
  })
}

// 新增设备页广告
export function addDeviceAd(data) {
  return request({
    url: '/device/ad/save',
    method: 'post',
    data
  })
}

// 修改设备页广告
export function updateDeviceAd(data) {
  return request({
    url: '/device/ad/update',
    method: 'put',
    data
  })
}

// 删除设备页广告
export function deleteDeviceAd(ids) {
  return request({
    url: '/device/ad/delete',
    method: 'delete',
    data: ids
  })
}

// 禁用/启用设备页广告
export function authDeviceAd(authList) {
  return request({
    url: '/device/ad/auth',
    method: 'put',
    data: authList
  })
} 