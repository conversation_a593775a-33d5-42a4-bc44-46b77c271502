import request from '@/utils/request'

// 获取骑游攻略列表
export function getGuidebookList(params) {
  return request({
    url: '/guidebook/list',
    method: 'get',
    params
  })
}

// 获取骑游攻略详情
export function getGuidebookInfo(id) {
  return request({
    url: `/guidebook/info/${id}`,
    method: 'get'
  })
}

// 新增骑游攻略
export function addGuidebook(data) {
  return request({
    url: '/guidebook/save',
    method: 'post',
    data
  })
}

// 修改骑游攻略
export function updateGuidebook(data) {
  return request({
    url: '/guidebook/update',
    method: 'put',
    data
  })
}

// 删除骑游攻略
export function deleteGuidebook(ids) {
  return request({
    url: '/guidebook/delete',
    method: 'delete',
    data: ids
  })
}

// 禁用/启用骑游攻略
export function authGuidebook(authList) {
  return request({
    url: '/guidebook/auth',
    method: 'put',
    data: authList
  })
}

// 获取参与人员列表
export function getJoinList(id) {
  return request({
    url: '/guidebook/join/list',
    method: 'get',
    params: { id }
  })
}

// 获取主题列表
export function getThemeList(params) {
  return request({
    url: '/guidebook/theme/list',
    method: 'get',
    params
  })
}

// 新增主题
export function addTheme(data) {
  return request({
    url: '/guidebook/theme/add',
    method: 'post',
    data
  })
}

// 修改主题
export function updateTheme(data) {
  return request({
    url: '/guidebook/theme/edit',
    method: 'put',
    data
  })
}

// 启用/禁用主题
export function authTheme(data) {
  return request({
    url: '/guidebook/theme/auth',
    method: 'put',
    data
  })
} 