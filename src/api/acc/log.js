import request from '@/utils/request'

// 全部
export function listApiLog(query) {
  return request({
    url: '/acc/api/log/list',
    method: 'get',
    params: query
  })
}


// 设备控制
export function equipList(params) {
  return request({
    url: '/regulation/equip/list',
    method: 'get',
    params
  })
}

// 解绑
export function equipDelBind({ id, bikeId, type }) {
  return request({
    url: `/regulation/equip/del/bind/${id}?bikeId=${bikeId}&type=${type}`,
    method: 'post'
  })
}

// 详情
export function equipInfo({ id, type }) {
  return request({
    url: `/regulation/equip/info/${id}?type=${type}`,
    method: 'get'
  })
}