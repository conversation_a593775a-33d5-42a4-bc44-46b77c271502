import request from '@/utils/request'

// 身份认证管理相关API

/**
 * 获取身份认证列表
 * @param {Object} params - 查询参数
 * @param {string} params.p - 页码
 * @param {string} params.l - 每页数量
 * @param {string} params.name - 认证名称
 */
export function getIdentityList(params) {
  return request({
    url: '/user/identity/list',
    method: 'get',
    params
  })
}

/**
 * 创建身份认证
 * @param {Object} data - 身份认证数据
 */
export function addIdentity(data) {
  return request({
    url: '/user/identity/add',
    method: 'post',
    data
  })
}

/**
 * 更新身份认证
 * @param {Object} data - 身份认证数据
 */
export function editIdentity(data) {
  return request({
    url: '/user/identity/edit',
    method: 'put',
    data
  })
}

/**
 * 启用/禁用身份认证
 * @param {Object} data - 启用/禁用数据
 * @param {number} data.id - 身份认证ID
 * @param {boolean} data.enabled - 是否启用
 */
export function authIdentity(data) {
  return request({
    url: '/user/identity/auth',
    method: 'put',
    data
  })
}

// 用户申请管理相关API

/**
 * 获取用户申请列表
 * @param {Object} params - 查询参数
 * @param {string} params.p - 页码
 * @param {string} params.l - 每页数量
 * @param {string} params.name - 认证名称
 * @param {string} params.nickName - 用户昵称
 * @param {string} params.state - 状态(1申请中 2审核通过 3审核失败)
 */
export function getIdentityRecordList(params) {
  return request({
    url: '/user/identity/record/list',
    method: 'get',
    params
  })
}

/**
 * 审核用户申请
 * @param {Object} data - 审核数据
 * @param {number} data.id - 申请记录ID
 * @param {number} data.state - 审核状态(2通过 3不通过)
 * @param {string} data.reason - 审核理由
 */
export function authRecord(data) {
  return request({
    url: '/user/identity/record/auth',
    method: 'put',
    data
  })
}
