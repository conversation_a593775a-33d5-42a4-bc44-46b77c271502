import request from '@/utils/request'

// 消息列表
export function listMsg(query) {
    return request({
        url: '/acc/msg/list',
        method: 'get',
        params: query
    })
}
// 添加消息
export function addMsg(data) {
    return request({
        url: '/acc/msg',
        method: 'post',
        data: data
    })
}

// 修改消息
export function editMsg(data) {
    return request({
        url: '/acc/msg',
        method: 'put',
        data: data
    })
}



// 修改消息
export function pushAcc(data) {
    return request({
        url: '/acc/push',
        method: 'post',
        data
    })
}

export function msgDetail(id) {
    return request({
        url: '/acc/msg/' + id,
        method: 'get'
    })
}

// 详情
export function authUser(data) {
    return request({
        url: '/acc/auth',
        method: 'put',
        data: data
    })
}