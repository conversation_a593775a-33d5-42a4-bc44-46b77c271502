import request from '@/utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/acc/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户详情
export function detailUser(query) {
  return request({
    url: '/acc/user/detail/' + query,
    method: 'get',
  })
}

// 删除用户
export function authUser(data) {
  return request({
    url: '/acc/user/auth',
    method: 'put',
    data: data
  })
}

// 注册异常
export function userLose(data) {
  return request({
      url: '/user/lose',
      method: 'post',
      data: data
  })
}

// 注册异常列表
export function userLoseSearch(params) {
  return request({
      url: '/user/lose/search',
      method: 'get',
      params
  })
}


// 用户注销列表
export function userSearch(params) {
  return request({
      url: '/user/search',
      method: 'get',
      params
  })
}


// 用户注销
export function userLogout(data) {
  return request({
      url: '/user/logout',
      method: 'post',
      data
  })
}