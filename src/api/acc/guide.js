import request from '@/utils/request'

// 获取引导页列表
export function getGuideList(params) {
  return request({
    url: '/guide/list',
    method: 'get',
    params
  })
}

// 获取引导页详情
export function getGuideInfo(id) {
  return request({
    url: `/guide/info/${id}`,
    method: 'get'
  })
}

// 新增引导页
export function addGuide(data) {
  return request({
    url: '/guide/save',
    method: 'post',
    data
  })
}

// 修改引导页
export function updateGuide(data) {
  return request({
    url: '/guide/update',
    method: 'put',
    data
  })
}

// 删除引导页
export function deleteGuide(ids) {
  return request({
    url: '/guide/delete',
    method: 'delete',
    data: ids
  })
}

// 禁用/启用引导页
export function authGuide(authList) {
  return request({
    url: '/guide/auth',
    method: 'put',
    data: authList
  })
} 