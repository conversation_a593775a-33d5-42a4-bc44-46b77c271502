import request from '@/utils/request'

// 无轨迹列表回
export function ridingTrackNotList(query) {
  return request({
    url: '/acc/riding/track/not/list',
    method: 'get',
    params: query
  })
}

 
// 详情
export function rackNoDetail(query) {
  return request({
    url: '/acc/riding/track/not/detail/' + query,
    method: 'get',
    params: query
  })
}

 
// 有轨迹列表
export function ridingTracktList(query) {
  return request({
    url: '/acc/riding/track/list',
    method: 'get',
    params: query
  })
}

//详情 

export function rackDetail(query) {
  return request({
    url: '/acc/riding/track/detail/' + query,
    method: 'get',
    params: query
  })
}
