import request from '@/utils/request'

// 首页
export function statisticsCount(query) {
  return request({
    url: '/sys/statistics/count',
    method: 'get',
    params: query
  })
}


export function faultCount(query) {
  return request({
    url: '/sys/statistics/fault/count',
    method: 'get',
    params: query
  })
}


export function rankCount(query) {
  return request({
    url: '/sys/statistics/rank/count',
    method: 'get',
    params: query
  })
}


export function brandModel(query) {
  return request({
    url: '/sys/soft/brand/model',
    method: 'get',
    params: query
  })
}

export function postedCount(query) {
  return request({
    url: '/sys/statistics/statistics/uab',
    method: 'get',
    params: query
  })
}


export function trackCount(query) {
  return request({
    url: '/sys/statistics/not/track/count',
    method: 'get',
    params: query
  })
}

//获取文章量/评论数据
export function commentCount() {
  return request({
    url: '/sys/statistics/statistics/comment',
    method: 'get',
  })
}

// 品牌对比
export function proportion() {
  return request({
    url: '/sys/statistics/statistics/proportion'
  })
}