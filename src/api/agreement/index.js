import request from '@/utils/request'

export function logList(params) {
    return request({
        url: '/regulation/document/list',
        method: 'get',
        params
    })
}

export function logCreate(data) {
    return request({
        url: '/regulation/document',
        method: 'post',
        data
    })
}

export function logEdit(data) {
    return request({
        url: '/regulation/document',
        method: 'put',
        data
    })
}

export function logDel(id) {
    return request({
        url: '/regulation/document/' + id,
        method: 'delete'
    })
}
