import request from '@/utils/request'

export function listSysOrder(params) {
  return request({
    url: '/svs/order/list',
    method: 'get',
    params
  })
}

export function SysOrderDetail(id) {
  return request({
    url: '/svs/order/detail/' + id,
    method: 'get'
  })
}

export function listSvsTights(params) {
  return request({
    url: '/svs/tights/list',
    method: 'get',
    params
  })
}

export function svsTighAuth(data) {
  return request({
    url: '/svs/tights/auth',
    method: 'put',
    data
  })
}


