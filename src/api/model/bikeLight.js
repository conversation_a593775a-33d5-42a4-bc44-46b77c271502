import request from '@/utils/request';
/**组车辆档位 */
export function listgroupGear(params) {
  return request({
    url: '/light/group/group/list',
    method: 'get',
    params,
  });
}

export function addGroup(data) {
  return request({
    url: '/light/group/group',
    method: 'post',
    data,
  });
}

export function updateGroup(data) {
  return request({
    url: '/light/group/group',
    method: 'put',
    data,
  });
}

export function deletGroup(query) {
  return request({
    url: '/light/group/group/del/' + query,
    method: 'delete',
  });
}
/**明细车辆档位 */
// 查列表
export function listGear(query) {
  return request({
    url: '/light/group/list',
    method: 'get',
    params: query,
  });
}


export function deleteGear(query) {
  return request({
    url: '/light/group/del/' + query,
    method: 'delete',
  });
}

export function addGear(data) {
  return request({
    url: '/light/group',
    method: 'post',
    data,
  });
}

export function updateGear(data) {
  return request({
    url: '/light/group',
    method: 'put',
    data,
  });
}

export function lightGroupDict() {
  return request({
    url: '/light/group/group/dict',
    method: 'get',
  });
}

