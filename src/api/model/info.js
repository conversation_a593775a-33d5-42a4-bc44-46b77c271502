import request from '@/utils/request'

//  
export function infoList(query) {
  return request({
    url: '/model/info/list',
    method: 'get',
    params: query
  })
}


export function infoDel(query) {
  return request({
    url: '/model/info/del/' + query,
    method: 'DELETE',
    params: query
  })
}


export function updateInfo(data) {
  return request({
    url: '/model/info',
    method: 'put',
    data
  })
}

export function addInfo(data) {
  return request({
    url: '/model/info',
    method: 'post',
    data
  })
}
