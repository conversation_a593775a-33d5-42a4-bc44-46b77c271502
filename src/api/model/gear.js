import request from '@/utils/request';
/**组车辆档位 */
export function listgroupGear(params) {
  return request({
    url: '/model/gear/group/list',
    method: 'get',
    params,
  });
}


export function addGroup(data) {
  return request({
    url: '/model/gear/group',
    method: 'post',
    data,
  });
}

export function updateGroup(data) {
  return request({
    url: '/model/gear/group',
    method: 'put',
    data,
  });
}

export function deletGroup(query) {
  return request({
    url: '/model/gear/group/del/' + query,
    method: 'delete',
  });
}
/**明细车辆档位 */
// 查列表
export function listGear(query) {
  return request({
    url: '/model/gear/list',
    method: 'get',
    params: query,
  });
}


export function deleteGear(query) {
  return request({
    url: '/model/gear/del/' + query,
    method: 'delete',
  });
}

export function addGear(data) {
  return request({
    url: '/model/gear',
    method: 'post',
    data,
  });
}

export function updateGear(data) {
  return request({
    url: '/model/gear',
    method: 'put',
    data,
  });
}

export function groupDict() {
  return request({
    url: '/model/gear/group/dict',
    method: 'get',
  });
}

