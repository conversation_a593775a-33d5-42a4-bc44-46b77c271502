import request from '@/utils/request'

// 获取新闻列表
export function getNewsList(params) {
  return request({
    url: '/news/list',
    method: 'get',
    params
  })
}

// 获取新闻详情
export function getNewsInfo(id) {
  return request({
    url: `/news/info/${id}`,
    method: 'get'
  })
}

// 新增新闻
export function saveNews(data) {
  return request({
    url: '/news/save',
    method: 'post',
    data
  })
}

// 修改新闻
export function updateNews(data) {
  return request({
    url: '/news/update',
    method: 'put',
    data
  })
}

// 删除新闻
export function deleteNews(ids) {
  return request({
    url: '/news/delete',
    method: 'delete',
    data: ids
  })
} 