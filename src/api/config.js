const urlList = {
  // "development": "http://192.168.2.127:28700",
  "development": "http://addmotor-admin-api.riding-evolved.com",
  // "development": "https://admin-api.addmotor.com",
  // "development": "http://192.168.2.26:28700",
  "test": "http://addmotor-admin-api.riding-evolved.com",

  "production": process.env.VUE_APP_BASE_URL
};

// 专用上传服务器地址配置
const uploadUrlList = {
  "development": "http://api.binyo.net",
  //  "development": "https://storeapi.addmotor.com",
  "test": "http://api.binyo.net",
  "production": "https://storeapi.addmotor.com"
};

const NODE_ENV = process.env.NODE_ENV;
const VUE_BASE_URL = urlList[NODE_ENV];
const VUE_BASE_UPLOAD = uploadUrlList[NODE_ENV];

export {
  VUE_BASE_URL,
  VUE_BASE_UPLOAD
};