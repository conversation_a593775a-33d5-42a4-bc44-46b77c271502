import request from '@/utils/request';

// 查询列表
export function confList(params) {
  return request({
    url: '/bike/conf/list',
    method: 'get',
    params,
  });
}

export function confAdd(data) {
  return request({
    url: '/bike/conf',
    method: 'post',
    data,
  });
}

export function confUpdate(data) {
  return request({
    url: '/bike/conf',
    method: 'put',
    data,
  });
}

export function confAuth(data) {
  return request({
    url: '/bike/conf/auth',
    method: 'put',
    data,
  });
}

export function confDict(params) {
  return request({
    url: '/bike/conf/dict',
    method: 'get',
    params
  });
}

export function batteryList(query) {
  return request({
    url: '/bike/conf/battery/list',
    method: 'get',
    params: query,
  });
}
