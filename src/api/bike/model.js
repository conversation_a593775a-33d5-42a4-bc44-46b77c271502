/*
 * @Author: your name
 * @Date: 2021-04-25 11:59:58
 * @LastEditTime: 2021-05-11 18:29:29
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /bikewise-pro-web/src/api/bike/model.js
 */
import request from '@/utils/request'

// 查询品牌列表
export function listModel(query) {
  return request({
    url: '/bike/model/list',
    method: 'get',
    params: query
  })
}

// 查询品牌详情
export function detailModel(query) {
  return request({
    url: '/bike/model/' + query,
    method: 'get',
  })
}

// 启用/禁用品牌
export function authModel(data) {
  return request({
    url: '/bike/model/auth',
    method: 'put',
    data: data
  })
}

// 创建品牌
export function addModel(data) {
  return request({
    url: '/bike/model',
    method: 'post',
    data: data
  })
}

// 编辑品牌
export function editModel(data) {
  return request({
    url: '/bike/model',
    method: 'put',
    data: data
  })
}


export function brandComputer(params) {
  return request({
    url: '/bike/model/customer/brand/computer',
    method: 'get',
    params
  })
}

export function modelFuzzy(params) {
  return request({
    url: '/bike/model/dict/fuzzy',
    method: 'get',
    params
  })
}

export function modelRandom() {
  return request({
    url: '/bike/model/random',
    method: 'get'
  })
}


// 客户列表
export function modelCustomer() {
  return request({
    url: '/bike/model/customer',
    method: 'get'
  })
}

// 品牌列表
export function modelBrand(params) {
  return request({
    url: '/bike/model/brand',
    method: 'get',
    params
  })
}