import request from '@/utils/request'

// 查询组队活动列表
export function listTeamActivity(query) {
  return request({
    url: '/bike/teamActivity/list',
    method: 'get',
    params: query
  })
}

// 查询组队活动详细
export function getTeamActivity(id) {
  return request({
    url: '/bike/teamActivity/' + id,
    method: 'get'
  })
}

// 新增组队活动
export function addTeamActivity(data) {
  return request({
    url: '/bike/teamActivity',
    method: 'post',
    data: data
  })
}

// 修改组队活动
export function updateTeamActivity(data) {
  return request({
    url: '/bike/teamActivity',
    method: 'put',
    data: data
  })
}

// 删除组队活动
export function delTeamActivity(ids) {
  return request({
    url: '/bike/teamActivity/' + ids,
    method: 'delete'
  })
}

// 导出组队活动
export function exportTeamActivity(query) {
  return request({
    url: '/bike/teamActivity/export',
    method: 'get',
    params: query
  })
}

// 查询骑行声明公告列表
export function listAnnouncement(query) {
  return request({
    url: '/bike/announcement/list',
    method: 'get',
    params: query
  })
}

// 查询骑行声明公告详细
export function getAnnouncement(id) {
  return request({
    url: '/bike/announcement/' + id,
    method: 'get'
  })
}

// 新增骑行声明公告
export function addAnnouncement(data) {
  return request({
    url: '/bike/announcement',
    method: 'post',
    data: data
  })
}

// 修改骑行声明公告
export function updateAnnouncement(data) {
  return request({
    url: '/bike/announcement',
    method: 'put',
    data: data
  })
}

// 删除骑行声明公告
export function delAnnouncement(ids) {
  return request({
    url: '/bike/announcement/' + ids,
    method: 'delete'
  })
}

// 导出骑行声明公告
export function exportAnnouncement(query) {
  return request({
    url: '/bike/announcement/export',
    method: 'get',
    params: query
  })
}

// Disclaimer list
export function getDisclaimerList(query) {
  console.log('API: 发送请求参数:', query); // 添加调试日志
  return request({
    url: '/disclaimer/list',
    method: 'get',
    params: {
      p: query.pageNum,
      l: query.pageSize,
      title: query.title,
      type: query.type,
      status: query.status
    }
  })
}

// Get disclaimer detail
export function getDisclaimerInfo(id) {
  return request({
    url: `/disclaimer/info/${id}`,
    method: 'get'
  })
}

// Add new disclaimer
export function addDisclaimer(data) {
  return request({
    url: '/disclaimer/save',
    method: 'post',
    data
  })
}

// Update disclaimer
export function updateDisclaimer(data) {
  return request({
    url: '/disclaimer/update',
    method: 'put',
    data
  })
}

// Delete disclaimer
export function deleteDisclaimer(ids) {
  return request({
    url: '/disclaimer/delete',
    method: 'delete',
    data: ids
  })
}

// Enable/Disable disclaimer
export function updateDisclaimerAuth(data) {
  return request({
    url: '/disclaimer/auth',
    method: 'put',
    data
  })
}

// ============ 组队活动管理 API ============

// Activity list
export function getActivityList(query) {
  console.log('API: 发送组队活动请求参数:', query); // 添加调试日志
  return request({
    url: '/activity/list',
    method: 'get',
    params: {
      p: query.pageNum,
      l: query.pageSize,
      titleName: query.titleName,
      startTime: query.startTime,
      endTime: query.endTime
    }
  })
}

// Get activity detail
export function getActivityInfo(id) {
  return request({
    url: `/activity/info/${id}`,
    method: 'get'
  })
}

// Add new activity
export function addActivity(data) {
  return request({
    url: '/activity/save',
    method: 'post',
    data
  })
}

// Update activity
export function updateActivity(data) {
  return request({
    url: '/activity/update',
    method: 'put',
    data
  })
}

// Delete activity
export function deleteActivity(ids) {
  return request({
    url: '/activity/delete',
    method: 'delete',
    data: ids
  })
}

// Enable/Disable activity
export function updateActivityAuth(data) {
  return request({
    url: '/activity/auth',
    method: 'put',
    data
  })
}

// Get activity rank
export function getActivityRank(query) {
  return request({
    url: '/activity/rank',
    method: 'get',
    params: query
  })
}

// Get activity detail for preview
export function getActivityDetail(id) {
  return request({
    url: `/activity/info/${id}`,
    method: 'get'
  })
} 