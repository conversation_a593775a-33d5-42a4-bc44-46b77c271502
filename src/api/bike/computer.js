/*
 * @Author: your name
 * @Date: 2021-04-25 11:59:58
 * @LastEditTime: 2021-05-11 17:12:47
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /bikewise-pro-web/src/api/bike/computer.js
 */
import request from '@/utils/request'

// 查询仪表列表
export function listComputer(query) {
  return request({
    url: '/bike/computer/list',
    method: 'get',
    params: query
  })
}

// 查询仪表详情
export function detailComputer(query) {
  return request({
    url: '/bike/computer/' + query,
    method: 'get',
  })
}

// 启用/禁用仪表
export function authComputer(data) {
  return request({
    url: '/bike/computer/auth',
    method: 'put',
    data: data
  })
}

// 创建仪表
export function addComputer(data) {
  return request({
    url: '/bike/computer',
    method: 'post',
    data: data
  })
}

// 编辑仪表
export function editComputer(data) {
  return request({
    url: '/bike/computer',
    method: 'put',
    data: data
  })
}
/**
 * 获取品类
 */
export function getProductClass(data) {
  return request({
    url: `/bike/computer/dict/productClass?productClass=${data}`,
    method: 'get',
  })
}
/**
 * 获取型号
 */
export function getProductModel(data) {
  return request({
    url: `/bike/computer/dict/productModel?productClass=${data}`,
    method: 'get',
  })
}
/**
 * 获取编码
 */
export function getProductCode(productClass, productModel) {
  return request({
    url: `/bike/computer/dict/code`,
    method: 'get',
    params: {
      productClass,
      productModel
    }
  })
}

export function modelRandom() {
  return request({
    url: '/bike/computer/random',
    method: 'get'
  })
}