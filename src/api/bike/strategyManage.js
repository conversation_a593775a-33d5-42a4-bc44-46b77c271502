import request from '@/utils/request'

// 查询攻略列表
export function listStrategy(query) {
  return request({
    url: '/bike/strategy/list',
    method: 'get',
    params: query
  })
}

// 查询攻略详细
export function getStrategy(id) {
  return request({
    url: '/bike/strategy/' + id,
    method: 'get'
  })
}

// 新增攻略
export function addStrategy(data) {
  return request({
    url: '/bike/strategy',
    method: 'post',
    data: data
  })
}

// 修改攻略
export function updateStrategy(data) {
  return request({
    url: '/bike/strategy',
    method: 'put',
    data: data
  })
}

// 删除攻略
export function delStrategy(ids) {
  return request({
    url: '/bike/strategy/' + ids,
    method: 'delete'
  })
}

// 导出攻略
export function exportStrategy(query) {
  return request({
    url: '/bike/strategy/export',
    method: 'get',
    params: query
  })
}

// 发布攻略
export function publishStrategy(id) {
  return request({
    url: '/bike/strategy/publish/' + id,
    method: 'put'
  })
}

// 取消发布攻略
export function unpublishStrategy(id) {
  return request({
    url: '/bike/strategy/unpublish/' + id,
    method: 'put'
  })
}

// 批量发布攻略
export function batchPublishStrategy(ids) {
  return request({
    url: '/bike/strategy/batchPublish',
    method: 'put',
    data: ids
  })
}

// 获取攻略统计信息
export function getStrategyStats() {
  return request({
    url: '/bike/strategy/stats',
    method: 'get'
  })
} 