/*
 * @Author: your name
 * @Date: 2021-04-25 11:59:58
 * @LastEditTime: 2021-05-11 17:46:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /bikewise-pro-web/src/api/bike/bike.js
 */
import request from '@/utils/request'

// 查询品牌列表
export function listBike(query) {
  return request({
    url: '/bike/bike/list',
    method: 'get',
    params: query
  })
}

// 查询品牌详情
export function detailBike(query) {
  return request({
    url: '/bike/bike/' + query,
    method: 'get',
  })
}

// 启用/禁用品牌
export function authBike(data) {
  return request({
    url: '/bike/bike/auth',
    method: 'put',
    data: data
  })
}

// 创建品牌
export function addBike(data) {
  return request({
    url: '/bike/bike',
    method: 'post',
    data: data
  })
}

// 编辑品牌
export function editBike(data) {
  return request({
    url: '/bike/bike',
    method: 'put',
    data: data
  })
}


// 解绑
export function computerUnbind(data) {
  return request({
    url: '/bike/bike/computer/unbind/' + data,
    method: 'put',
    data: data
  })
}

// 车辆位置

export function bikeGps(params) {
  return request({
    url: '/bike/bike/bike/position',
    method: 'get',
    params
  })
}