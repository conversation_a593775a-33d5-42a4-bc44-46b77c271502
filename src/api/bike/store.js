import request from '@/utils/request'

// 查询品牌列表
export function listStore(query) {
  return request({
    url: '/bike/store/list',
    method: 'get',
    params: query
  })
}

// 查询品牌详情
export function detailModel(query) {
  return request({
    url: '/bike/model/' + query,
    method: 'get',
  })
}

// 启用/禁用品牌
export function authModel(data) {
  return request({
    url: '/bike/model/auth',
    method: 'put',
    data: data
  })
}

// 创建门店
export function addStore(data) {
  return request({
    url: '/bike/store',
    method: 'post',
    data: data
  })
}

// 修改门店
export function eidtStore(data) {
  return request({
    url: '/bike/store',
    method: 'put',
    data: data
  })
}


// 启用/禁用门店
export function authStore(data) {
  return request({
    url: '/bike/store/auth',
    method: 'put',
    data: data
  })
}