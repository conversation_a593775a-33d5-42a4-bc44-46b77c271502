import request from '@/utils/request';

export function computerBindAdd (query) {
  return request ({
    url: '/computerBind/bind',
    method: 'post',
    data: query,
  });
}

export function computerBindUpdate (query) {
  return request ({
    url: '/computerBind/bind',
    method: 'put',
    data: query,
  });
}

export function computerBindAuth (query) {
  return request ({
    url: '/computerBind/bind/auth',
    method: 'put',
    data: query,
  });
}

export function computerBindList (query) {
  return request ({
    url: '/computerBind/bind/list',
    method: 'get',
    params: query,
  });
}
