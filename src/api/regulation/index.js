import request from '@/utils/request'

// 查询群组
export function listUserGroup(params) {
  return request({
    url: '/regulation/user/group/list',
    method: 'get',
    params
  })
}

//   启用、禁用
export function regulationAuth(data) {
  return request({
    url: '/regulation/user/group/auth',
    method: 'put',
    data
  })
}

//  根据群组id查询
export function regulationMember({ p, l, id }) {
  return request({
    url: '/regulation/user/group/member/' + id,
    method: 'get',
    params: { p, l }
  })
}

// 删除
export function regulationDelete({ userId, id }) {
  return request({
    url: '/regulation/user/group/delete/' + id,
    method: 'delete',
    params: { userId }
  })
}

// 获取头盔的型号
export function helmetModelList(params) {
  return request({
    url: '/regulation/helmet/model/list',
    method: 'get',
    params
  })
}

// 新增头盔型号
export function helmetModelCreate(data) {
  return request({
    url: '/regulation/helmet/model/create',
    method: 'post',
    data
  })
}

// 禁用、启用
export function helmetModelAuth(data) {
  return request({
    url: '/regulation/helmet/model/auth',
    method: 'put',
    data
  })
}

// 修改型号
export function helmetModelEdit(data) {
  return request({
    url: '/regulation/helmet/model/edit',
    method: 'put',
    data
  })
}

// 型号详情
export function helmetModelDetail(id) {
  return request({
    url: '/regulation/helmet/model/detail/' + id,
    method: 'get'
  })
}

// 智能头盔信息
export function helmetList(params) {
  return request({
    url: '/helmet/helmet/list',
    method: 'get',
    params
  })
}

// 解绑智能头盔
export function helmetDelBind({ id, userId }) {
  return request({
    url: '/helmet/helmet/del/bind/' + id,
    method: 'post',
    data: { userId }
  })
}

/**
 * 雷达型号
 */

export function radarList(params) {
  return request({
    url: '/radar/list',
    method: 'get',
    params
  })
}

export function radarAuth(data) {
  return request({
    url: '/radar/auth',
    method: 'put',
    data
  })
}


export function radarSave(data) {
  return request({
    url: '/radar/save',
    method: 'post',
    data
  })
}

export function radarUpdate(data) {
  return request({
    url: '/radar/update',
    method: 'put',
    data
  })
}


/**
 * 运动相机型号
 */

export function cameraList(params) {
  return request({
    url: '/camera/list',
    method: 'get',
    params
  })
}

export function cameraAuth(data) {
  return request({
    url: '/camera/auth',
    method: 'put',
    data
  })
}

export function cameraSave(data) {
  return request({
    url: '/camera/save',
    method: 'post',
    data
  })
}

export function cameraUpdate(data) {
  return request({
    url: '/camera/update',
    method: 'put',
    data
  })
}

/**
 * 按键型号
 */

export function keyList(params) {
  return request({
    url: '/key/list',
    method: 'get',
    params
  })
}

export function keyAuth(data) {
  return request({
    url: '/key/auth',
    method: 'put',
    data
  })
}

export function keySave(data) {
  return request({
    url: '/key/save',
    method: 'post',
    data
  })
}

export function keyUpdate(data) {
  return request({
    url: '/key/update',
    method: 'put',
    data
  })
}


/**
 * IOT型号
 */

export function typeList(params) {
  return request({
    url: '/type/list',
    method: 'get',
    params
  })
}

export function typeAuth(data) {
  return request({
    url: '/type/auth',
    method: 'put',
    data
  })
}

export function typeSave(data) {
  return request({
    url: '/type/save',
    method: 'post',
    data
  })
}

export function typeUpdate(data) {
  return request({
    url: '/type/update',
    method: 'put',
    data
  })
}




