import request from '@/utils/request'

// 查询品牌列表
export function listBaseMedal(query) {
  return request({
    url: '/base/medalInteraction/list',
    method: 'get',
    params: query
  })
}

// 查询品牌详情
export function detailBaseMedal(query) {
  return request({
    url: '/base/medalInteraction/' + query,
    method: 'get',
  })
}

// 启用/禁用品牌
export function authBaseMedal(data) {
  return request({
    url: '/base/medalInteraction/auth',
    method: 'put',
    data: data
  })
}

// 创建品牌
export function addBaseMedal(data) {
  return request({
    url: '/base/medalInteraction',
    method: 'post',
    data: data
  })
}

// 编辑品牌
export function editBaseMedal(data) {
  return request({
    url: '/base/medalInteraction',
    method: 'put',
    data: data
  })
}
