import request from '@/utils/request'

// 查询反馈列表
export function listFeedback(query) {
  return request({
    url: '/base/feedback/list',
    method: 'get',
    params: query
  })
}


// 查询反馈列表详情
export function feedbackDetail(query) {
  return request({
    url: '/base/feedback/'+query,
    method: 'get',
    params: query
  })
}

 // 回复反馈
export function feedbackMsg(data) {
  return request({
    url: '/base/feedback/msg',
    method: 'post',
    data: data
  })
}

 

 