import request from '@/utils/request'

// 查询异常列表
export function listException(query) {
  return request({
    url: '/base/exception/list',
    method: 'get',
    params: query
  })
}

// 修改
export function exceptionupdate(data) {
  return request({
    url: '/base/exception',
    method: 'put',
    data: data
  })
}



export function addExceptionu(data) {
  return request({
    url: '/base/exception',
    method: 'post',
    data: data
  })
}




export function exceptionAuth(data) {
  return request({
    url: '/base/exception/auth',
    method: 'put',
    data: data
  })
}
 


export function authStore(data) {
  return request({
    url: '/bike/store/auth',
    method: 'put',
    data: data
  })
}



export const listDict = (params) => {
  return request({
    url: '/base/exception/list/dict',
    method: 'get',
    params,
  })
}
