import request from '@/utils/request'

// 查询FQA列表
export function listFqa(query) {
    return request({
        url: '/base/fqa/list',
        method: 'get',
        params: query
    })
}

// 修改
export function  fqaupdate(data) {
    return request({
        url: '/base/fqa',
        method: 'put',
        data: data
    })
}

// 修改
export function  fqaAuth(data) {
    return request({
        url: '/base/fqa/auth',
        method: 'put',
        data: data
    })
}

// 增加
export function addfqa(data) {
    return request({
        url: '/base/fqa',
        method: 'post',
        data: data
    })
}

