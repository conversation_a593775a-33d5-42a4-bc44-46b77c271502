import request from '@/utils/request'

// 全部
export function allList(query) {
  return request({
    url: '/base/data',
    method: 'get',
    params: query
  })
}

export function listDictCustomer(query) {
  return request({
    url: '/base/data/customer',
    method: 'get',
    params: query
  })
}

export function listDictBrand(query) {
  return request({
    url: '/base/data/brand',
    method: 'get',
    params: query
  })
}

export function listDictComputer(query) {
  return request({
    url: '/base/data/computer',
    method: 'get',
    params: query
  })
}


export function listDictModel(params) {
  return request({
    url: '/base/data/device',
    method: 'get',
    params
  })
}

//获取客户字典
export function listDictCustomers(query) {
  return request({
    url: '/bike/customer/dict',
    method: 'get',
    params: query,
  })
}
