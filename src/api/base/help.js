import request from '@/utils/request'

// 帮助列表
export function listHelp(query) {
  return request({
    url: '/base/help/list',
    method: 'get',
    params: query
  })
}


// 添加列表
export function addHelp(data) {
  return request({
    url: '/base/help',
    method: 'post',
    data: data
  })
}


// 编辑列表
export function editHelp(data) {
  return request({
    url: '/base/help',
    method: 'put',
    data: data
  })
}

// 编辑列表
export function helpDetail(id) {
  return request({
    url: '/base/help/' + id,
    method: 'get',
  })
}
