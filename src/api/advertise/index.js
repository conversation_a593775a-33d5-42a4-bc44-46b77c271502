import request from '@/utils/request'

// 广告列表
export function bannerList(params) {
    return request({
      url: '/regulation/posted/banner/list',
      method: 'get',
      params
    })
  }

//   启用、禁用
  export function bannerAuth(data) {
    return request({
      url: '/regulation/posted/banner/auth',
      method: 'put',
      data
    })
  }
 
//   创建广告内容
export function bannerCreate(data) {
    return request({
      url: '/regulation/posted/banner/create',
      method: 'post',
      data
    })
}

//   创建广告内容
export function bannerImgCreate(data) {
    return request({
      url: '/regulation/posted/banner/create/img',
      method: 'post',
      data
    })
}

//   查看广告详情
export function bannerDetail(id) {
    return request({
      url: '/regulation/posted/banner/detail/' + id,
      method: 'get'
    })
}

// 修改广告内容
export function bannerEdit(data) {
    return request({
      url: '/regulation/posted/banner/edit',
      method: 'put',
      data
    })
  }

  // 修改广告图片
export function bannerEditImg(data) {
    return request({
      url: '/regulation/posted/banner/edit/img',
      method: 'put',
      data
    })
  }