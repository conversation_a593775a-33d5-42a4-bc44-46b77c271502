module.exports = {
  title: 'AddMotor 管理系统',

  /**
   * 是否系统布局配置
   */
  showSettings: false,

  /**
   * 是否显示 tagsView
   */
  tagsView: true,

  /**
   * 是否固定头部
   */
  fixedHeader: false,

  /**
   * 是否显示logo
   */
  sidebarLogo: true,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: 'production',

  /**
   * Mapbox Access Token
   * @description Used for map components
   */
  mapboxAccessToken: process.env.VUE_APP_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYWRkbW90b3IiLCJhIjoiY2x6dGltaDFzMmgybzJtb2NtNmsxYTIxaCJ9.b3yBd9nkM3EDNzebk_gVDA'
}
