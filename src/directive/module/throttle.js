/**
 * v-throttle-click 节流
 * Copyright (c) 2024
 */

let timer = null;
let flag = false;

const throttle = (func, wait = 500, immediate = false) => {
  if (immediate) {
    if (!flag) {
      flag = true;
      // 如果是立即执行， 则在wait毫秒内开始时执行
      typeof func === "function" && func();
      timer = setTimeout(() => {
        flag = false;
      }, wait);
    }
  } else {
    if (!flag) {
      flag = true;
      // 如果是非立即执行， 则在wait毫秒内结束处执行

      timer = setTimeout(() => {
        flag = false;
        typeof func === "function" && func();
      }, wait);
    }
  }
};

export default {
  inserted(el, binding) {
    el.addEventListener("click", throttle(binding.value)); // 这里的500是延迟时间，单位是毫秒
  }
};
