import _ from "lodash";

export default {
  bind(el, binding) {
    // 确保el是dialog的标题部分
    if (binding.arg === "title") {
      const dialog = el.querySelector(".el-dialog");
      const elHeader = dialog.querySelector(".el-dialog__header");
      elHeader.style.cursor = "move";
      dialog.style.position = "absolute";
      dialog.style.top = "50px";
      dialog.style.left = 0;
      dialog.style.margin = "0";

      let offsetX = 0;
      let offsetY = 0;
      let startMouseX = 0;
      let startMouseY = 0;

      const handleMousedown = e => {
        // 记录鼠标按下时的位置
        startMouseX = e.clientX;
        startMouseY = e.clientY;

        // 计算鼠标与dialog左上角的偏移量
        const rect = dialog.getBoundingClientRect();
        offsetX = e.clientX - rect.left;
        offsetY = e.clientY - rect.top;

        // 监听鼠标移动和松开事件
        document.addEventListener("mousemove", handleMousemove);
        document.addEventListener("mouseup", handleMouseup);
      };

      const handleMousemove = e => {
        // 移动dialog
        const x = e.clientX - offsetX;
        const y = e.clientY - offsetY;
        dialog.style.top = `${y}px`;
        dialog.style.left = `${x}px`;
      };

      const handleMouseup = () => {
        // 移除鼠标移动和松开事件监听器
        document.removeEventListener("mousemove", handleMousemove);
        document.removeEventListener("mouseup", handleMouseup);
      };

      // 给标题添加mousedown事件监听器
      elHeader.addEventListener("mousedown", handleMousedown);
    }
  }
};
