import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';

export default {
  mounted() {
    // 获取 renderer
    const renderer = this.$ownerInstance.renderer;

    if (typeof KTX2Loader === 'function') {
      try {
        const loader = new KTX2Loader();
        loader.setTranscoderPath('https://binyo.net/static/themes/default/products/3d/libs/basis/');
        loader.detectSupport(renderer);
        loader.setWorkerLimit(2);

        // 你可以用 this 传出 loader
        this.ktx2Loader = loader;
        this.gltfLoader.setKTX2Loader(loader);

        console.log('✅ KTX2加载器配置完成');
      } catch (e) {
        console.warn('❌ KTX2加载器初始化失败:', e);
      }
    }
  }
};
