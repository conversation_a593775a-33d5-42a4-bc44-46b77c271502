const commonJs = {
  data() {
    return {
      open: false,
      // 搜索框显示隐藏
      showSearch: true,
      // 元素加载Loading
      isCodeLoading: true,
      // 提交loading
      isBtnLoading: false,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      dateRange: []
    };
  },
  methods: {
    tabSelection(selection, singleOrMultity = true) {
      // singleOrMultity (true 多选 反之 单选)
      if (selection.length === 0) {
        return true; // 判断是否选择列
      }
      return false;
    },
    warningMessage(msg, isType) {
      // type 1 成功、2 警告、 3 失败
      let type = null;
      switch (+isType) {
        case 1:
          type = "success";
          break;
        case 2:
          type = "warning";
          break;
        case 3:
          type = "error";
          break;
      }
      this.$message({
        message: msg,
        type
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /**
     * 用户状态修改
     * @param {Object} row 列表项数据
     * @param {Function} aFn Api
     * @param {Functoin} getList 列表方法
     */
    handleStatusChange(row, aFn, getList) {
      let text =
        row.status === 0
          ? this.$t("bike.customer.startUsing")
          : this.$t("bike.customer.blockUp");

      this.$confirm(
        `${this.$t("bike.customer.sure")}${text}?`,
        this.$t("bike.customer.warn"),
        {
          confirmButtonText: this.$t("bike.customer.confirm"),
          cancelButtonText: this.$t("bike.customer.cancel"),
          type: "warning"
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = { id: row.id, status: row.status };
          data.push(authData);
          aFn(data)
            .then(() => {
              this.msgSuccess(text + this.$t("bike.customer.succeed"));
              this.loading = false;
              getList();
            })
            .catch(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    // 批量启用、禁用处理
    handleAuth(status, aFn, getList) {
      if (this.tabSelection(this.ids)) {
        if (status === 0) {
          return this.warningMessage(`${this.$t("form.enableData")}`, 1);
        } else {
          return this.warningMessage(`${this.$t("form.disabledData")}`, 3);
        }
      }

      let text =
        status === 0
          ? this.$t("system.computer.auth")
          : this.$t("system.computer.disabled");
      this.$confirm(
        this.$t("system.computer.handleStatusChange.text1") +
          text +
          this.$t("system.computer.handleStatusChange.text2"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("bike.customer.confirm"),
          cancelButtonText: this.$t("bike.customer.cancel"),
          type: "warning"
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let authData = {
              id: this.ids[i],
              status
            };
            data.push(authData);
          }
          aFn(data)
            .then(() => {
              // 清除勾选项
              this.$refs.multipleTableRef.clearSelection();
              this.msgSuccess(text + this.$t("system.computer.success"));
              getList();
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          status = status === 0 ? 1 : 0;
        });
    },
    // 删除操作
    handleDel(data, delFn, getList) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        delFn(data)
          .then(() => {
            this.msgSuccess(this.$t("dialog.deleteSuccess"));
            getList();
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.isBtnLoading = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery({ queryForm = "queryForm" } = {}) {
      this.dateRange = [];
      this.queryParams = {};
      this.resetForm(queryForm);
      this.handleQuery();
    }
  }
};

export { commonJs };
