<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo2" :src="logo2" class="sidebar-logo" />
        <h1 v-else class="sidebar-title">{{ title }}</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <div class="logo-wrapper">
          <img v-if="logo2" :src="logo2" class="sidebar-logo" />
          <h1 class="sidebar-title">
            <span v-for="(letter, index) in titleLetters" :key="index" class="title-letter">
              {{ letter }}
            </span>
          </h1>
        </div>
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoImg from "@/assets/logo/logo.png";
import logoImg2 from "@/assets/logo/logo-mini.png";

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: "ADDMOTOR",
      logo: logoImg,
      logo2: logoImg2
    };
  },
  computed: {
    titleLetters() {
      return this.title.split('');
    }
  }
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: center;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .logo-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 0 16px;
      transition: all 0.3s ease;
      height: 100%;
    }

    & .sidebar-logo {
      width: auto;
      height: 100%;
      height: auto;
      vertical-align: middle;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      font-weight: 700;
      line-height: 50px;
      font-size: 18px;
      font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'Roboto', Arial, sans-serif;
      vertical-align: middle;
      letter-spacing: 1.5px;
      transition: all 0.1s ease;
      position: relative;

      .title-letter {
        display: inline-block;
        transition: all 0.31 ease;
        animation-name: letterColorWave;
        animation-duration: 1s;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
        animation-direction: alternate;
        animation-fill-mode: both;
        position: relative;
        font-size: inherit;
        line-height: inherit;
      }

    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}

// 字母颜色波浪动画
@keyframes letterColorWave {
  from {
    color: #ccc;
  }

  to {
    color: #FF8A50;
  }
}

// SCSS循环为每个字母设置不同延迟
@for $i from 1 through 9 {
  .sidebar-logo-container .sidebar-title .title-letter:nth-child(#{$i}) {
    animation-delay: ($i - 1) * 0.1s;
  }
}
</style>
