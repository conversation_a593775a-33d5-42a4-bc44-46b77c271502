import i18n from "@/lang";
import { is_Empty } from "@/utils/ruoyi";

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} phone
 * @returns {Boolean}
 */
export function validPhone(phone) {
  const reg = /(^0\d{2,3}-\d{7,8}$)|(^1[3456789]\d{9}$)/;
  return reg.test(phone)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * 
 * @param {string} versionName 
 * @returns { Boolean }
 */
export function validVerionName(versionName) {
  const reg = /^(v|V)?([1-9]\d|[1-9])(.([1-9]\d|\d)){2}$/;
  return reg.test(versionName);
}

// 校验联系方式
export function checkValidPhone(rule, value, callback) {
  if (value && !validPhone(value)) {
    callback(new Error(i18n.t("base.store.contactPhone") + i18n.t("checkConfigData.illegal")));
  } else {
    callback();
  }
};

// 校验联系邮箱
export function checkValidEmail(rule, value, callback) {
  if (value && !validEmail(value)) {
    callback(new Error(i18n.t("base.store.contactEmail") + i18n.t("checkConfigData.illegal")));
  } else {
    callback();
  }
};

// 校验网址
export function checkValidUrl(rule, value, callback) {
  if (value && !validURL(value)) {
    callback(new Error(i18n.t("base.store.website") + i18n.t("checkConfigData.illegal")));
  } else {
    callback();
  }
};

// 校验版本号
export function checkVersionName(rule, value, callback) {
  if (is_Empty(value)) {
    callback(new Error(i18n.t("system.app.rules.versionNum")));
  } else if (!validVerionName(value)) {
    callback(new Error(i18n.t("mointerObj.correctVerionNumber")));
  } else {
    callback();
  }
};