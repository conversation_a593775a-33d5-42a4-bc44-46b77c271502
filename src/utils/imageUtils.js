import { VUE_BASE_UPLOAD } from "@/api/config";

/**
 * 图片工具函数
 * 提供图片URL格式化和处理相关的工具方法
 */

/**
 * 格式化图片URL，自动拼接上传服务器地址
 * 根据环境自动选择：
 * - test环境: http://api.binyo.net + url
 * - 正式环境: https://storeapi.addmotor.com + url
 * @param {string} url - 原始URL
 * @param {boolean} autoPrefix - 是否自动拼接前缀，默认true
 * @returns {string} - 格式化后的完整URL
 */
export function formatImageUrl(url, autoPrefix = true) {
  if (!url || !autoPrefix) {
    return url || '';
  }

  // 如果已经是完整的URL（包含http或https），直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // 如果是相对路径，拼接上传服务器地址
  if (url.startsWith('/')) {
    return VUE_BASE_UPLOAD + url;
  }

  // 如果不是以/开头的相对路径，添加/再拼接
  return VUE_BASE_UPLOAD + '/' + url;
}

/**
 * 批量格式化图片URL列表
 * @param {string|Array} urls - 图片URL，支持字符串（逗号分隔）或数组
 * @param {boolean} autoPrefix - 是否自动拼接前缀，默认true
 * @returns {Array} - 格式化后的URL数组
 */
export function formatImageUrls(urls, autoPrefix = true) {
  if (!urls) {
    return [];
  }

  let urlList = [];
  if (typeof urls === 'string') {
    urlList = urls.split(',').filter(url => url.trim());
  } else if (Array.isArray(urls)) {
    urlList = urls.filter(url => url);
  } else {
    return [];
  }

  return urlList.map(url => formatImageUrl(url.trim(), autoPrefix));
}

/**
 * 获取当前环境的上传服务器地址
 * @returns {string} - 上传服务器地址
 */
export function getUploadBaseUrl() {
  return VUE_BASE_UPLOAD;
}

/**
 * 获取当前环境信息
 * @returns {object} - 环境信息对象
 */
export function getEnvironmentInfo() {
  const env = process.env.NODE_ENV;
  return {
    environment: env,
    uploadUrl: VUE_BASE_UPLOAD,
    isProduction: env === 'production',
    isTest: env === 'test',
    isDevelopment: env === 'development'
  };
}

/**
 * 检查URL是否为完整的HTTP/HTTPS地址
 * @param {string} url - 要检查的URL
 * @returns {boolean} - 是否为完整URL
 */
export function isFullUrl(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }
  return url.startsWith('http://') || url.startsWith('https://');
}

/**
 * 从完整URL中提取文件名
 * @param {string} url - 图片URL
 * @returns {string} - 文件名
 */
export function getFileNameFromUrl(url) {
  if (!url) return 'unknown';
  const parts = url.split('/');
  return parts[parts.length - 1] || 'unknown';
}

/**
 * 获取图片文件扩展名
 * @param {string} url - 图片URL
 * @returns {string} - 文件扩展名（小写）
 */
export function getImageExtension(url) {
  if (!url) return '';
  const fileName = getFileNameFromUrl(url);
  const parts = fileName.split('.');
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
}

/**
 * 检查是否为支持的图片格式
 * @param {string} url - 图片URL
 * @returns {boolean} - 是否为支持的图片格式
 */
export function isSupportedImageFormat(url) {
  const supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
  const extension = getImageExtension(url);
  return supportedFormats.includes(extension);
}

/**
 * 创建图片预加载Promise
 * @param {string} url - 图片URL
 * @returns {Promise} - 预加载Promise
 */
export function preloadImage(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = formatImageUrl(url);
  });
}

/**
 * 批量预加载图片
 * @param {Array} urls - 图片URL数组
 * @returns {Promise} - 所有图片预加载完成的Promise
 */
export function preloadImages(urls) {
  const formattedUrls = formatImageUrls(urls);
  const promises = formattedUrls.map(url => preloadImage(url));
  return Promise.all(promises);
}

/**
 * 生成缩略图URL（如果服务器支持）
 * @param {string} url - 原始图片URL
 * @param {number} width - 缩略图宽度
 * @param {number} height - 缩略图高度
 * @returns {string} - 缩略图URL
 */
export function generateThumbnailUrl(url, width = 150, height = 150) {
  const formattedUrl = formatImageUrl(url);
  // 这里可以根据实际的缩略图服务规则来实现
  // 示例：添加缩略图参数
  if (formattedUrl.includes('?')) {
    return `${formattedUrl}&w=${width}&h=${height}`;
  } else {
    return `${formattedUrl}?w=${width}&h=${height}`;
  }
}

// 默认导出所有工具函数
export default {
  formatImageUrl,
  formatImageUrls,
  getUploadBaseUrl,
  getEnvironmentInfo,
  isFullUrl,
  getFileNameFromUrl,
  getImageExtension,
  isSupportedImageFormat,
  preloadImage,
  preloadImages,
  generateThumbnailUrl
};
