import SelectLoadMore from "@/components/selectLoadMore";
import DrUpload from "@/components/Upload";
import ModalStyle from "@/components/ModalStyle";
import PreviewImg from "@/components/PreviewImg";
import RightToolbar from "@/components/RightToolbar";
import Pagination from "@/components/Pagination";
import ElUploadSortable from "@/components/el-upload-sortable";
import ElImgIcon from "@/components/el-img-icon";
import ElShowText from "@/components/ShowText";
import Dialog from "@/components/Dialog";
import Tooltip from "@/components/Tooltip";

const componentList = [
  SelectLoadMore,
  DrUpload,
  ModalStyle,
  PreviewImg,
  RightToolbar,
  Pagination,
  ElUploadSortable,
  ElImgIcon,
  ElShowText,
  Dialog,
  Tooltip
];

const install = Vue => {
  componentList.forEach(comName => {
    Vue.component(comName.name, comName);
  });
};

export default install;
