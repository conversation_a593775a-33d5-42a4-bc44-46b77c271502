import Vue from "vue";

import { getDicts } from "@/api/system/dict/data";
import { checkRole, checkPermi } from "@/utils/permission";
import { getConfigKey } from "@/api/system/config";
import {
  extend,
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  download,
  handleTree,
  is_Empty,
  clearValidateItem,
  formlabelW,
  toBDMap,
  toJumpPagePath,
  urlDownload,
  tableHeight,
  formattedDurationTime,
  filterUrl
} from "@/utils/ruoyi";
import moment from "moment";

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.checkRole = checkRole;
Vue.prototype.checkPermi = checkPermi;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.extend = extend;
Vue.prototype.$IS_Empty = is_Empty;
Vue.prototype.clearValidateItem = clearValidateItem;
Vue.prototype.formlabelW = formlabelW;
Vue.prototype.toBDMap = toBDMap;
Vue.prototype.toJumpPagePath = toJumpPagePath;
Vue.prototype.urlDownload = urlDownload;
Vue.prototype.tableHeight = tableHeight;
Vue.prototype.moment = moment;
Vue.prototype.formattedDurationTime = formattedDurationTime;
Vue.prototype.filterUrl = filterUrl;

Vue.prototype.msgSuccess = function(msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
};

Vue.prototype.msgError = function(msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
};

Vue.prototype.msgInfo = function(msg) {
  this.$message.info(msg);
};
