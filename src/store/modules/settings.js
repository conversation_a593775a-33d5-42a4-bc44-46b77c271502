import variables from '@/assets/styles/element-variables.scss'
import defaultSettings from '@/settings'

const { showSettings, tagsView, fixedHeader, sidebarLogo } = defaultSettings

const state = {
  theme: variables.theme,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo
}

// const mutations = {
//   CHANGE_SETTING: (state, { key, value }) => {
//     if (state.hasOwnProperty(key)) {
//       state[key] = value
//     }
//   }
// }

// const actions = {
//   changeSetting({ commit }, data) {
//     commit('CHANGE_SETTING', data)
//   }
// }


const mutations = {
  SET_STASTEVAL(state, { key, value }) {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  }
}

const actions = {
  CHANGE_STATEITEMVAL({ commit }, data) {
    commit('SET_STASTEVAL', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

