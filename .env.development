# 开发环境配置
ENV = 'development'

port = 28700

# 测试
VUE_APP_BASE_API = '/dev-api'
# VUE_APP_BASE_URL = 'http://admin-api.addmotor.com'
VUE_APP_BASE_URL = 'http://192.168.2.8:2870'
 
# VUE_APP_BASE_API = 'http://ew-admin-api.riding-evolved.com'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# 开发环境性能优化
# 启用源码映射但优化速度
GENERATE_SOURCEMAP = true

# 禁用ESLint检查以提高编译速度(在快速开发模式下)
# VUE_APP_DISABLE_ESLINT = true

# webpack优化设置
# 启用缓存
VUE_APP_ENABLE_CACHE = true

# 多线程构建
VUE_APP_ENABLE_THREAD_LOADER = true

VUE_APP_WEB_TITLE = "测试文件"

