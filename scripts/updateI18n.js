#!/usr/bin/env node

/**
 * 骑行管理模块国际化更新脚本
 * 自动将硬编码的中文文本替换为国际化函数调用
 */

const fs = require('fs');
const path = require('path');

// 文本替换映射
const textMapping = {
  // 公共文本
  '新增': '$t(\'bike.ridingManage.add\')',
  '编辑': '$t(\'bike.ridingManage.edit\')',
  '删除': '$t(\'bike.ridingManage.delete\')',
  '导出': '$t(\'bike.ridingManage.export\')',
  '搜索': '$t(\'bike.ridingManage.search\')',
  '重置': '$t(\'bike.ridingManage.reset\')',
  '确定': '$t(\'bike.ridingManage.confirm\')',
  '取消': '$t(\'bike.ridingManage.cancel\')',
  '操作': '$t(\'bike.ridingManage.operation\')',
  '状态': '$t(\'bike.ridingManage.status\')',
  '序号': '$t(\'bike.ridingManage.index\')',
  
  // 组队活动相关
  '活动名称': '$t(\'bike.ridingManage.activityName\')',
  '组织者': '$t(\'bike.ridingManage.organizer\')',
  '活动描述': '$t(\'bike.ridingManage.description\')',
  '开始时间': '$t(\'bike.ridingManage.startTime\')',
  '结束时间': '$t(\'bike.ridingManage.endTime\')',
  '参与人数': '$t(\'bike.ridingManage.participants\')',
  '最大参与人数': '$t(\'bike.ridingManage.maxParticipants\')',
  '进行中': '$t(\'bike.ridingManage.activityStatus.ongoing\')',
  '待开始': '$t(\'bike.ridingManage.activityStatus.pending\')',
  '已结束': '$t(\'bike.ridingManage.activityStatus.ended\')',
  
  // 公告相关
  '公告标题': '$t(\'bike.ridingManage.title\')',
  '公告类型': '$t(\'bike.ridingManage.type\')',
  '公告内容': '$t(\'bike.ridingManage.content\')',
  '发布时间': '$t(\'bike.ridingManage.publishTime\')',
  '发布人': '$t(\'bike.ridingManage.publisher\')',
  '已发布': '$t(\'bike.ridingManage.announcementStatus.published\')',
  '草稿': '$t(\'bike.ridingManage.announcementStatus.draft\')',
  '骑行声明': '$t(\'bike.ridingManage.announcementType.statement\')',
  '公告通知': '$t(\'bike.ridingManage.announcementType.notice\')',
  
  // Placeholder文本
  '请输入活动名称': '$t(\'bike.ridingManage.placeholders.activityName\')',
  '请输入组织者': '$t(\'bike.ridingManage.placeholders.organizer\')',
  '请输入活动描述': '$t(\'bike.ridingManage.placeholders.description\')',
  '选择开始时间': '$t(\'bike.ridingManage.placeholders.startTime\')',
  '选择结束时间': '$t(\'bike.ridingManage.placeholders.endTime\')',
  '请输入公告标题': '$t(\'bike.ridingManage.placeholders.announcementTitle\')',
  '请输入公告内容': '$t(\'bike.ridingManage.placeholders.content\')',
  '请输入发布人': '$t(\'bike.ridingManage.placeholders.publisher\')',
  '请选择状态': '$t(\'form.select\') + $t(\'bike.ridingManage.status\')',
  '请选择类型': '$t(\'form.select\') + $t(\'bike.ridingManage.type\')',
  
  // 验证消息
  '活动名称不能为空': '$t(\'bike.ridingManage.validations.activityNameRequired\')',
  '组织者不能为空': '$t(\'bike.ridingManage.validations.organizerRequired\')',
  '开始时间不能为空': '$t(\'bike.ridingManage.validations.startTimeRequired\')',
  '结束时间不能为空': '$t(\'bike.ridingManage.validations.endTimeRequired\')',
  '公告标题不能为空': '$t(\'bike.ridingManage.validations.titleRequired\')',
  '公告类型不能为空': '$t(\'bike.ridingManage.validations.typeRequired\')',
  '公告内容不能为空': '$t(\'bike.ridingManage.validations.contentRequired\')',
  
  // 操作消息
  '新增组队活动': '$t(\'bike.ridingManage.addTeamActivity\')',
  '修改组队活动': '$t(\'bike.ridingManage.editTeamActivity\')',
  '新增骑行声明公告': '$t(\'bike.ridingManage.addAnnouncement\')',
  '修改骑行声明公告': '$t(\'bike.ridingManage.editAnnouncement\')',
  '确定删除该组队活动吗？': '$t(\'bike.ridingManage.deleteActivityConfirm\')',
  '确定删除该公告吗？': '$t(\'bike.ridingManage.deleteAnnouncementConfirm\')',
  '提示': '$t(\'bike.ridingManage.warning\')',
  '删除成功': '$t(\'bike.ridingManage.deleteSuccess\')',
  '添加成功': '$t(\'bike.ridingManage.addSuccess\')',
  '修改成功': '$t(\'bike.ridingManage.updateSuccess\')',
  '导出成功': '$t(\'bike.ridingManage.deleteSuccess\')'
};

/**
 * 更新文件中的国际化文本
 * @param {string} filePath - 文件路径
 */
function updateI18nInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    
    // 替换label属性
    Object.keys(textMapping).forEach(chineseText => {
      const i18nText = textMapping[chineseText];
      
      // 替换 label="中文" 为 :label="$t(...)"
      const labelRegex = new RegExp(`label="${chineseText}"`, 'g');
      if (content.match(labelRegex)) {
        content = content.replace(labelRegex, `:label="${i18nText}"`);
        updated = true;
      }
      
      // 替换 placeholder="中文" 为 :placeholder="$t(...)"
      const placeholderRegex = new RegExp(`placeholder="${chineseText}"`, 'g');
      if (content.match(placeholderRegex)) {
        content = content.replace(placeholderRegex, `:placeholder="${i18nText}"`);
        updated = true;
      }
      
      // 替换模板中的文本 >中文< 为 >{{ $t(...) }}<
      const textRegex = new RegExp(`>${chineseText}<`, 'g');
      if (content.match(textRegex)) {
        content = content.replace(textRegex, `>{{ ${i18nText} }}<`);
        updated = true;
      }
      
      // 替换字符串 "中文" 为 $t(...)
      const stringRegex = new RegExp(`"${chineseText}"`, 'g');
      if (content.match(stringRegex)) {
        content = content.replace(stringRegex, i18nText);
        updated = true;
      }
    });
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
    } else {
      console.log(`⏭️ 无需更新: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`❌ 更新失败: ${filePath}`, error.message);
  }
}

/**
 * 递归遍历目录更新文件
 * @param {string} dir - 目录路径
 */
function updateDirectory(dir) {
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        updateDirectory(filePath);
      } else if (file.endsWith('.vue') || file.endsWith('.js')) {
        updateI18nInFile(filePath);
      }
    });
    
  } catch (error) {
    console.error(`❌ 读取目录失败: ${dir}`, error.message);
  }
}

// 主函数
function main() {
  console.log('🚀 开始更新骑行管理模块国际化...\n');
  
  const targetDirectories = [
    'src/views/bike/ridingManage',
    'src/views/bike/strategyManage'
  ];
  
  targetDirectories.forEach(dir => {
    console.log(`📁 处理目录: ${dir}`);
    updateDirectory(dir);
    console.log('');
  });
  
  console.log('✨ 国际化更新完成！');
  console.log('\n📝 请检查以下内容：');
  console.log('1. 验证所有中文文本已替换为国际化函数');
  console.log('2. 确保语言包中包含所有必要的翻译文本');
  console.log('3. 测试中英文切换功能');
  console.log('4. 检查组件是否正常工作');
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = {
  updateI18nInFile,
  updateDirectory,
  textMapping
}; 