#!/usr/bin/env node

const { execSync } = require('child_process');
const semver = require('semver');

// Get current Node.js version
const nodeVersion = process.version;

// Check if Node.js version is 17 or higher (requires --openssl-legacy-provider)
const needsLegacyProvider = semver.gte(nodeVersion, '17.0.0');

// Get the build mode from command line arguments
const mode = process.argv.includes('--mode') 
  ? process.argv[process.argv.indexOf('--mode') + 1] 
  : null;

// Build the command
let command = mode 
  ? `vue-cli-service build --mode ${mode}`
  : 'vue-cli-service build';

if (needsLegacyProvider) {
  command = `NODE_OPTIONS='--openssl-legacy-provider' ${command}`;
}

console.log(`Node.js version: ${nodeVersion}`);
console.log(`Running: ${command}`);

// Execute the command
try {
  execSync(command, { stdio: 'inherit' });
} catch (error) {
  process.exit(error.status);
}