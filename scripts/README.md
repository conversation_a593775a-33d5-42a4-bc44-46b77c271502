# Vue模块生成脚本使用说明

## 简介

`createVueModule.js` 是一个自动化脚本，用于快速创建Vue页面模块，包含完整的国际化支持。

## 特性

✨ **动态国际化加载** - 每个模块管理自己的国际化资源
🔧 **自动文件生成** - 一键生成Vue文件和国际化文件  
📁 **规范化命名** - 自动按照 `文件夹名-文件夹名.vue` 格式命名
🌐 **多语言支持** - 自动生成中英文国际化文件
🛡️ **错误处理** - 包含fallback机制确保稳定性

## 使用方法

### 基本用法

```bash
node scripts/createVueModule.js <文件夹名> [父路径]
```

### 参数说明

- `文件夹名` (必需): 要创建的模块文件夹名称
- `父路径` (可选): 父级路径，默认为 `views`

### 使用示例

```bash
# 在 src/views/userProfile 下创建模块
node scripts/createVueModule.js userProfile

# 在 src/views/acc/userProfile 下创建模块  
node scripts/createVueModule.js userProfile views/acc

# 在 src/views/system/config 下创建模块
node scripts/createVueModule.js config views/system
```

## 生成的文件结构

```
src/views/[父路径]/[文件夹名]/
├── [文件夹名]-[文件夹名].vue  # Vue组件文件
├── zh.js                      # 中文国际化
└── en.js                      # 英文国际化
```

## 生成的文件内容

### Vue组件特性

- 🎯 **完整的CRUD界面** - 包含搜索、表格、分页等
- 🌐 **动态国际化加载** - 自动加载当前模块的国际化文件
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🎨 **Element UI组件** - 使用项目统一的UI组件库
- 🔄 **语言切换支持** - 支持运行时语言切换

### 国际化文件特性

- 📝 **预定义文本** - 包含常用的页面文本
- 🔧 **易于扩展** - 结构化的文本组织方式
- 🌍 **中英文对照** - 完整的双语支持

## 工作原理

### 动态国际化加载

组件在 `created` 生命周期中会自动：

1. 检测当前语言设置
2. 动态导入对应的国际化文件
3. 将本地国际化合并到全局i18n实例
4. 提供fallback机制确保稳定性

```javascript
// 核心加载逻辑
async loadLocalI18n() {
  const currentLang = this.$i18n.locale
  const langModule = await import(`./\${currentLang}.js`)
  
  this.$i18n.mergeLocaleMessage(currentLang, {
    [模块名]: langModule.default
  })
}
```

### 文本使用方式

在模板中使用国际化文本：

```vue
<template>
  <h3>{{ $t('userProfile.title') }}</h3>
  <p>{{ $t('userProfile.description') }}</p>
</template>
```

## 自定义配置

### 修改生成的Vue组件

生成后可以根据需要修改：

- 调整表格列定义
- 修改搜索表单字段
- 添加自定义业务逻辑
- 实现API调用

### 完善国际化文件

根据业务需求完善文本内容：

```javascript
// zh.js
export default {
  title: '用户资料管理',
  description: '管理用户的个人资料信息',
  // ... 添加更多文本
}
```

## 最佳实践

### 1. 命名规范

- 使用小驼峰命名法：`userProfile`、`orderManage`
- 避免使用特殊字符和空格
- 保持名称简洁且具有描述性

### 2. 国际化文本组织

```javascript
export default {
  // 页面标题和描述
  title: '页面标题',
  description: '页面描述',
  
  // 搜索相关
  search: {
    keyword: '关键词',
    // ...
  },
  
  // 表格相关
  table: {
    id: 'ID',
    name: '名称',
    // ...
  },
  
  // 操作相关
  actions: {
    add: '添加',
    edit: '编辑',
    // ...
  }
}
```

### 3. 路由配置

生成模块后，需要在路由中添加配置：

```javascript
// router/index.js
{
  path: '/userProfile',
  component: () => import('@/views/userProfile/userProfile-userProfile.vue'),
  meta: { title: 'userProfile.title' }
}
```

## 注意事项

⚠️ **webpack配置**: 确保webpack支持动态导入  
⚠️ **路径问题**: 注意相对路径的正确性  
⚠️ **浏览器兼容**: 动态导入需要现代浏览器支持  
⚠️ **构建优化**: 可能需要配置代码分割

## 故障排除

### 国际化文件加载失败

如果出现国际化加载问题：

1. 检查文件路径是否正确
2. 确认文件内容格式正确
3. 查看浏览器控制台错误信息
4. 验证webpack动态导入配置

### 组件渲染异常

如果组件无法正常渲染：

1. 检查依赖组件是否正确导入
2. 确认Element UI组件可用
3. 验证API调用逻辑
4. 检查路由配置

## 版本历史

- v1.0 - 初始版本，支持基本的Vue组件和国际化文件生成
- v1.1 - 添加动态导入功能，移除全局国际化文件依赖
- v1.2 - 优化错误处理和fallback机制

---

# 国际化提取工具使用说明

## 简介

为了解决项目迭代过程中的国际化处理问题，我们提供了两个强大的工具：

### 1. 单文件提取工具 (`extractI18n.js`)

自动提取Vue文件中的中文文本，生成对应的国际化文件，并替换原文件中的硬编码文本。

### 2. 批量提取工具 (`batchExtractI18n.js`)

批量处理整个目录下的Vue文件，适合大规模项目的国际化迁移。

## 功能特性

🔍 **智能文本识别** - 自动识别模板、脚本中的中文文本  
🏷️ **智能Key生成** - 根据文本内容自动生成语义化的国际化key  
📁 **同级文件管理** - 在每个模块目录下生成独立的zh.js和en.js  
🔄 **动态导入支持** - 配合createVueModule.js的动态国际化加载  
💾 **安全备份** - 自动备份原文件，确保数据安全  
📊 **详细报告** - 提供完整的处理统计和结果报告  

## 使用方法

### NPM Scripts (推荐)

```bash
# 创建新的Vue模块
npm run create:vue userProfile views/acc

# 提取单个文件的国际化
npm run extract:i18n src/views/user/index.vue

# 批量提取整个目录
npm run extract:i18n:batch src/views/user --auto --recursive
```

### 直接调用

```bash
# 单文件提取
node scripts/extractI18n.js src/views/user/index.vue --prefix=user

# 批量处理
node scripts/batchExtractI18n.js src/views --auto --recursive
```

## 参数说明

### extractI18n.js 参数

- `vueFilePath` (必需) - 要处理的Vue文件路径
- `--auto` - 自动模式，不需要用户确认
- `--prefix=moduleName` - 指定国际化key的前缀

### batchExtractI18n.js 参数

- `directoryPath` (必需) - 要处理的目录路径
- `--auto` - 自动模式，不需要用户确认
- `--recursive` - 递归处理子目录

## 工作流程

### 1. 项目初期 - 创建标准化模块

```bash
# 创建新模块，自带国际化支持
npm run create:vue productManage views/shop
```

### 2. 项目迭代 - 处理已有硬编码

```bash
# 单个文件处理
npm run extract:i18n src/views/shop/oldModule.vue

# 批量处理整个模块
npm run extract:i18n:batch src/views/shop --auto
```

### 3. 大规模迁移 - 整站国际化

```bash
# 递归处理所有views目录
npm run extract:i18n:batch src/views --auto --recursive
```

## 处理示例

### 处理前的Vue文件

```vue
<template>
  <div>
    <h1>用户管理</h1>
    <el-button>添加用户</el-button>
    <el-input placeholder="请输入用户名" />
  </div>
</template>

<script>
export default {
  methods: {
    showMessage() {
      this.$message.success('操作成功')
    }
  }
}
</script>
```

### 处理后的Vue文件

```vue
<template>
  <div>
    <h1>{{ $t('userManage.用户管理') }}</h1>
    <el-button>{{ $t('userManage.添加用户') }}</el-button>
    <el-input :placeholder="$t('userManage.请输入用户名')" />
  </div>
</template>

<script>
export default {
  async created() {
    await this.loadLocalI18n()
  },
  methods: {
    async loadLocalI18n() {
      try {
        const currentLang = this.$i18n.locale
        const langModule = await import(`./\${currentLang}.js`)
        
        this.$i18n.mergeLocaleMessage(currentLang, {
          userManage: langModule.default
        })
      } catch (error) {
        console.warn('国际化文件加载失败:', error)
      }
    },
    
    showMessage() {
      this.$message.success(this.$t('userManage.操作成功'))
    }
  }
}
</script>
```

### 生成的zh.js

```javascript
export default {
  "用户管理": "用户管理",
  "添加用户": "添加用户", 
  "请输入用户名": "请输入用户名",
  "操作成功": "操作成功"
}
```

### 生成的en.js

```javascript
export default {
  "用户管理": "[EN] 用户管理",
  "添加用户": "[EN] 添加用户",
  "请输入用户名": "[EN] 请输入用户名", 
  "操作成功": "[EN] 操作成功"
}
```

## 最佳实践

### 1. 渐进式迁移策略

```bash
# 第一步：处理核心模块
npm run extract:i18n:batch src/views/user --auto

# 第二步：处理业务模块  
npm run extract:i18n:batch src/views/business --auto

# 第三步：处理其他模块
npm run extract:i18n:batch src/views --auto --recursive
```

### 2. 质量控制流程

```bash
# 1. 批量处理
npm run extract:i18n:batch src/views/target --auto

# 2. 人工检查生成的国际化文件
# 3. 完善英文翻译
# 4. 测试页面功能
# 5. 清理备份文件
```

### 3. 团队协作建议

- 🎯 **分模块处理** - 不同开发者负责不同模块的国际化
- 📝 **统一命名** - 制定国际化key的命名规范
- 🔄 **增量处理** - 新功能开发时直接使用国际化
- 📊 **定期检查** - 定期扫描是否有新的硬编码文本

## 注意事项

⚠️ **备份重要性** - 处理前确保代码已提交到版本控制  
⚠️ **测试充分性** - 处理后需要充分测试页面功能  
⚠️ **翻译质量** - 及时将[EN]标记替换为正确的英文翻译  
⚠️ **动态导入** - 确保webpack配置支持动态导入功能  

## 故障排除

### 常见问题

1. **提取不完整** - 检查正则表达式是否匹配复杂的中文文本模式
2. **替换错误** - 检查原文件格式是否规范
3. **动态导入失败** - 检查webpack和浏览器对动态导入的支持
4. **国际化不生效** - 检查i18n配置和组件中的加载逻辑

### 恢复方案

如果处理出现问题，可以通过备份文件恢复：

```bash
# 恢复单个文件
cp src/views/user/index.vue.backup src/views/user/index.vue

# 批量恢复（如果需要）
find src/views -name "*.vue.backup" -exec sh -c 'cp "$1" "${1%.backup}"' _ {} \;
```

## 贡献

如果您有改进建议或发现问题，请提交Issue或Pull Request。 