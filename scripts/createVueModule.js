#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length === 0) {
  console.error('❌ 请提供文件夹名称')
  console.log('用法: node scripts/createVueModule.js <folderName> [parentPath]')
  console.log('示例: node scripts/createVueModule.js userProfile views/acc')
  process.exit(1)
}

const folderName = args[0]
const parentPath = args[1] || 'views' // 默认在views目录下创建

// 路径配置
const basePath = path.join(process.cwd(), 'src', parentPath, folderName)
const vueFileName = `index.vue`

// 确保父目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
  }
}

// 生成Vue文件内容 - 简化版，使用国际化管理器
function generateVueFileContent(folderName) {
  const componentName = folderName.charAt(0).toUpperCase() + folderName.slice(1).replace(/-./g, x => x[1].toUpperCase())
  
  return `<template>
  <div class="app-container">
    <div class="page-header">
      <h3>{{ t('title') }}</h3>
      <p>{{ t('description') }}</p>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-card>
        <div slot="header" class="clearfix">
          <span>{{ t('mainContent') }}</span>
        </div>
        
        <!-- 搜索表单 -->
        <el-form ref="searchForm" :model="searchForm" :inline="true" class="search-form">
          <el-form-item :label="t('search.keyword')">
            <el-input
              v-model="searchForm.keyword"
              :placeholder="t('search.keywordPlaceholder')"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              搜索
            </el-button>
            <el-button @click="resetSearch">
              重置
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 操作按钮 -->
        <div class="toolbar">
          <el-button 
            type="primary" 
            icon="el-icon-plus" 
            @click="handleAdd"
          >
            {{ t('actions.add') }}
          </el-button>
        </div>
        
        <!-- 数据表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column 
            prop="id" 
            :label="t('table.id')" 
            width="80"
          />
          <el-table-column 
            prop="name" 
            :label="t('table.name')"
          />
          <el-table-column 
            prop="createTime" 
            :label="t('table.createTime')"
            width="180"
          />
          <el-table-column 
            :label="t('table.actions')" 
            width="200"
          >
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button 
                size="mini" 
                type="danger" 
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.limit"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { createI18nManager } from './lang'

export default {
  name: "${componentName}",
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchForm: {
        keyword: '',
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.getList()
  },
  methods: {
    // 简洁的国际化文本获取方法（支持参数）
    t(key, args) {
      return this.i18nManager.getText(key, args)
    },
    
    // 获取列表数据
    getList() {
      this.loading = true
      // TODO: 调用API获取数据
      setTimeout(() => {
        this.tableData = [
          {
            id: 1,
            name: 'Sample Data',
            createTime: '2024-01-01 12:00:00'
          }
        ]
        this.total = 1
        this.loading = false
      }, 1000)
    },
    
    // 搜索
    handleSearch() {
      this.searchForm.page = 1
      this.getList()
    },
    
    // 重置搜索
    resetSearch() {
      this.$refs.searchForm.resetFields()
      this.getList()
    },
    
    // 添加
    handleAdd() {
      this.$message.info(this.t('actions.add'))
    },
    
    // 编辑
    handleEdit(row) {
      this.$message.info(this.t('actions.edit') + ': ' + row.name)
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm(
        this.t('actions.confirmDelete'),
        '确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // TODO: 调用删除API
        this.$message.success('删除成功')
        this.getList()
      }).catch(() => {
        // 取消删除
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  p {
    margin: 5px 0 0 0;
    color: #606266;
    font-size: 14px;
  }
}

.search-form {
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;
}
</style>
`
}

// 生成国际化管理器内容
function generateI18nManagerContent(folderName) {
  const moduleKey = folderName.replace(/-/g, '')
  
  return `import zhLang from './zh.js'
import enLang from './en.js'

/**
 * 国际化管理器
 * 负责处理模块的国际化文本获取和注册
 */
export function createI18nManager(i18nInstance) {
  const moduleKey = '${moduleKey}'
  
  // 本地国际化资源
  const localI18n = {
    zh: zhLang,
    en: enLang
  }
  
  // 注册国际化文件到全局i18n
  function registerI18n() {
    try {
      const currentLang = i18nInstance.locale
      
      // 将本地化文件合并到全局i18n中
      i18nInstance.mergeLocaleMessage(currentLang, {
        [moduleKey]: localI18n[currentLang] || localI18n.zh
      })
      
      // 确保所有语言都注册
      Object.keys(localI18n).forEach(lang => {
        i18nInstance.mergeLocaleMessage(lang, {
          [moduleKey]: localI18n[lang]
        })
      })
      
      console.log(\`✅ [\${moduleKey}] 国际化文件注册成功:\`, currentLang)
    } catch (error) {
      console.error(\`❌ [\${moduleKey}] 注册国际化文件失败:\`, error)
    }
  }
  
  // 安全获取国际化文本
  function getText(key) {
    const fullKey = \`\${moduleKey}.\${key}\`
    try {
      // 尝试获取国际化文本
      const text = i18nInstance.t(fullKey)
      // 如果返回的是key本身，说明没有找到翻译
      if (text === fullKey) {
        // 使用本地备用文本
        return getFallbackText(key)
      }
      return text
    } catch (error) {
      // 如果出错，使用备用文本
      console.warn(\`获取国际化文本失败: \${fullKey}\`, error)
      return getFallbackText(key)
    }
  }
  
  // 备用文本
  function getFallbackText(key) {
    const fallbackTexts = {
      'title': '数据管理',
      'description': '数据管理界面',
      'mainContent': '主要内容',
      'search.keyword': '关键词',
      'search.keywordPlaceholder': '请输入搜索关键词',
      'table.id': 'ID',
      'table.name': '名称',
      'table.createTime': '创建时间',
      'table.actions': '操作',
      'actions.add': '添加数据',
      'actions.edit': '编辑数据',
      'actions.confirmDelete': '确定要删除这条记录吗？'
    }
    return fallbackTexts[key] || key
  }
  
  // 立即注册国际化文件
  registerI18n()
  
  // 返回管理器接口
  return {
    getText,
    registerI18n,
    moduleKey,
    localI18n
  }
}

// 导出默认的国际化工厂函数
export default createI18nManager
`
}

// 生成中文国际化文件内容
function generateZhContent(folderName) {
  return `export default {
  title: '数据管理',
  description: '数据管理界面',
  mainContent: '主要内容',
  
  search: {
    keyword: '关键词',
    keywordPlaceholder: '请输入搜索关键词'
  },
  
  table: {
    id: 'ID',
    name: '名称',
    createTime: '创建时间',
    actions: '操作'
  },
  
  actions: {
    add: '添加数据',
    edit: '编辑数据',
    confirmDelete: '确定要删除这条记录吗？'
  },
  
  form: {
    name: '名称',
    namePlaceholder: '请输入名称',
    description: '描述',
    descriptionPlaceholder: '请输入描述'
  },
  
  validation: {
    nameRequired: '名称不能为空'
  },
  
  messages: {
    addSuccess: '添加成功',
    updateSuccess: '更新成功',
    deleteSuccess: '删除成功'
  }
}
`
}

// 生成英文国际化文件内容
function generateEnContent(folderName) {
  return `export default {
  title: 'Data Management',
  description: 'Data management interface',
  mainContent: 'Main Content',
  
  search: {
    keyword: 'Keyword',
    keywordPlaceholder: 'Please enter search keyword'
  },
  
  table: {
    id: 'ID',
    name: 'Name',
    createTime: 'Create Time',
    actions: 'Actions'
  },
  
  actions: {
    add: 'Add Data',
    edit: 'Edit Data',
    confirmDelete: 'Are you sure to delete this record?'
  },
  
  form: {
    name: 'Name',
    namePlaceholder: 'Please enter name',
    description: 'Description',
    descriptionPlaceholder: 'Please enter description'
  },
  
  validation: {
    nameRequired: 'Name is required'
  },
  
  messages: {
    addSuccess: 'Add successfully',
    updateSuccess: 'Update successfully',
    deleteSuccess: 'Delete successfully'
  }
}
`
}

// 主函数
function createVueModule() {
  try {
    console.log(`🚀 开始创建Vue模块: ${folderName}`)
    console.log(`📁 目标路径: ${basePath}`)
    
    // 创建文件夹
    ensureDirectoryExists(basePath)
    
    // 创建Vue文件
    const vueFilePath = path.join(basePath, vueFileName)
    fs.writeFileSync(vueFilePath, generateVueFileContent(folderName))
    console.log(`✅ 已创建Vue文件: ${vueFileName}`)
    
    // 创建lang子文件夹
    const langDirPath = path.join(basePath, 'lang')
    ensureDirectoryExists(langDirPath)
    
    // 创建国际化管理器
    const i18nManagerPath = path.join(langDirPath, 'index.js')
    fs.writeFileSync(i18nManagerPath, generateI18nManagerContent(folderName))
    console.log(`✅ 已创建国际化管理器: lang/index.js`)
    
    // 创建中文国际化文件
    const zhFilePath = path.join(langDirPath, 'zh.js')
    fs.writeFileSync(zhFilePath, generateZhContent(folderName))
    console.log(`✅ 已创建中文国际化文件: lang/zh.js`)
    
    // 创建英文国际化文件
    const enFilePath = path.join(langDirPath, 'en.js')
    fs.writeFileSync(enFilePath, generateEnContent(folderName))
    console.log(`✅ 已创建英文国际化文件: lang/en.js`)
    
    console.log('\n🎉 Vue模块创建完成！')
    console.log(`📂 文件夹: ${basePath}`)
    console.log(`📄 Vue文件: ${vueFileName}`)
    console.log('📄 国际化文件: lang/index.js, lang/zh.js, lang/en.js')
    console.log('\n✨ 特性:')
    console.log('• 国际化逻辑完全封装在 lang/index.js 中')
    console.log('• Vue组件只需调用简洁的 t() 方法')
    console.log('• 自动注册和错误处理')
    console.log('• 支持多语言切换和fallback机制')
    console.log('• 模块化设计，易于维护')
    console.log('\n📝 使用方法:')
    console.log('• Vue组件中使用: this.t("key") 获取国际化文本')
    console.log('• 国际化逻辑都在 lang/index.js 中管理')
    console.log('• 文本内容在 lang/zh.js 和 lang/en.js 中定义')
    console.log('\n📝 下一步:')
    console.log('1. 在路由中添加新页面的路由配置')
    console.log('2. 根据需要修改Vue组件的内容')
    console.log('3. 完善国际化文件中的文本内容')
    console.log('4. 实现具体的API调用逻辑')
    
  } catch (error) {
    console.error('❌ 创建Vue模块失败:', error.message)
    process.exit(1)
  }
}

// 执行创建
createVueModule() 