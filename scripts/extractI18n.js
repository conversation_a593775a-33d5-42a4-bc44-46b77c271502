#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length === 0) {
  console.error('❌ 请提供Vue文件路径')
  console.log('用法: node scripts/extractI18n.js <vueFilePath> [--auto] [--prefix=moduleName]')
  console.log('示例: node scripts/extractI18n.js src/views/user/index.vue --prefix=user')
  process.exit(1)
}

const vueFilePath = args[0]
const isAutoMode = args.includes('--auto')
const prefixArg = args.find(arg => arg.startsWith('--prefix='))
const modulePrefix = prefixArg ? prefixArg.split('=')[1] : path.basename(path.dirname(vueFilePath))

// 中文正则表达式
const CHINESE_REGEX = /[\u4e00-\u9fa5]+/g
const CHINESE_TEXT_REGEX = /^.*[\u4e00-\u9fa5]+.*$/

// 存储提取的文本信息
let keyCounter = 1

// 生成国际化key
function generateI18nKey(text, prefix = modulePrefix) {
  // 移除特殊字符，保留中文、英文、数字
  const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '')
  
  if (cleanText.length <= 4) {
    return `${prefix}.${cleanText}`
  }
  
  // 提取关键词
  const keywords = extractKeywords(text)
  if (keywords.length > 0) {
    return `${prefix}.${keywords.join('')}`
  }
  
  // 使用数字编号
  return `${prefix}.text${keyCounter++}`
}

// 提取关键词
function extractKeywords(text) {
  const keywords = []
  const commonWords = ['的', '了', '是', '在', '有', '和', '就', '不', '人', '都', '一', '个', '上', '来', '到', '说', '要', '去', '你', '会', '着', '没', '看', '好', '自己', '这', '那', '里', '就是', '可以', '我们', '请', '输入', '选择', '确定', '取消', '删除', '添加', '修改', '查询', '重置']
  
  // 简单的关键词提取逻辑
  const chars = text.split('')
  let currentWord = ''
  
  for (let char of chars) {
    if (CHINESE_REGEX.test(char)) {
      currentWord += char
    } else {
      if (currentWord.length >= 2 && !commonWords.includes(currentWord)) {
        keywords.push(currentWord)
      }
      currentWord = ''
    }
  }
  
  if (currentWord.length >= 2 && !commonWords.includes(currentWord)) {
    keywords.push(currentWord)
  }
  
  return keywords.slice(0, 2) // 最多取2个关键词
}

// 解析Vue文件
function parseVueFile(filePath) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`文件不存在: ${filePath}`)
  }

  const content = fs.readFileSync(filePath, 'utf8')
  
  // 分离template、script、style部分
  const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/)
  const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/)
  
  return {
    template: templateMatch ? templateMatch[1] : '',
    script: scriptMatch ? scriptMatch[1] : '',
    original: content
  }
}

// 从模板中提取中文文本
function extractFromTemplate(template) {
  const texts = []
  const foundTexts = new Set()
  
  // 1. 匹配插值表达式中的中文字符串 {{ '中文' }} 或 {{ "中文" }}
  const interpolationMatches = template.match(/\{\{[^}]*(['"])[^'"]*[\u4e00-\u9fa5][^'"]*\1[^}]*\}\}/g)
  if (interpolationMatches) {
    interpolationMatches.forEach(match => {
      const textMatches = match.match(/(['"])([^'"]*[\u4e00-\u9fa5][^'"]*)\1/g)
      if (textMatches) {
        textMatches.forEach(textMatch => {
          const text = textMatch.slice(1, -1) // 移除引号
          if (!foundTexts.has(text)) {
            foundTexts.add(text)
            texts.push({
              original: textMatch,
              text: text,
              type: 'interpolation',
              context: match
            })
          }
        })
      }
    })
  }
  
  // 2. 匹配属性中的中文 placeholder="中文" 或 title="中文"
  const attributeMatches = template.match(/\s\w+\s*=\s*(['"])[^'"]*[\u4e00-\u9fa5][^'"]*\1/g)
  if (attributeMatches) {
    attributeMatches.forEach(match => {
      const textMatch = match.match(/(['"])([^'"]*[\u4e00-\u9fa5][^'"]*)\1/)
      if (textMatch) {
        const text = textMatch[2]
        if (!foundTexts.has(text)) {
          foundTexts.add(text)
          texts.push({
            original: textMatch[0],
            text: text,
            type: 'attribute',
            context: match.trim(),
            attributeName: match.match(/(\w+)\s*=/)[1]
          })
        }
      }
    })
  }
  
  // 3. 匹配标签内容中的中文（纯文本）
  const contentMatches = template.match(/>([^<]*[\u4e00-\u9fa5][^<]*)</g)
  if (contentMatches) {
    contentMatches.forEach(match => {
      const text = match.slice(1, -1).trim() // 移除 > 和 <
      if (text && !text.includes('{{') && !text.includes('}}') && CHINESE_TEXT_REGEX.test(text)) {
        if (!foundTexts.has(text)) {
          foundTexts.add(text)
          texts.push({
            original: text,
            text: text,
            type: 'content',
            context: match
          })
        }
      }
    })
  }
  
  return texts
}

// 从script中提取中文文本
function extractFromScript(script) {
  const texts = []
  const foundTexts = new Set()
  
  // 匹配单引号字符串中的中文
  const singleQuoteMatches = script.match(/'[^']*[\u4e00-\u9fa5][^']*'/g)
  if (singleQuoteMatches) {
    singleQuoteMatches.forEach(match => {
      const text = match.slice(1, -1) // 移除引号
      if (!foundTexts.has(text)) {
        foundTexts.add(text)
        texts.push({
          original: match,
          text: text,
          type: 'string',
          context: match
        })
      }
    })
  }
  
  // 匹配双引号字符串中的中文
  const doubleQuoteMatches = script.match(/"[^"]*[\u4e00-\u9fa5][^"]*"/g)
  if (doubleQuoteMatches) {
    doubleQuoteMatches.forEach(match => {
      const text = match.slice(1, -1) // 移除引号
      if (!foundTexts.has(text)) {
        foundTexts.add(text)
        texts.push({
          original: match,
          text: text,
          type: 'string',
          context: match
        })
      }
    })
  }
  
  // 匹配模板字符串中的中文
  const templateMatches = script.match(/`[^`]*[\u4e00-\u9fa5][^`]*`/g)
  if (templateMatches) {
    templateMatches.forEach(match => {
      // 简单处理，不处理复杂的模板字符串
      if (!match.includes('${')) {
        const text = match.slice(1, -1) // 移除反引号
        if (!foundTexts.has(text)) {
          foundTexts.add(text)
          texts.push({
            original: match,
            text: text,
            type: 'template',
            context: match
          })
        }
      }
    })
  }
  
  return texts
}

// 生成国际化文件内容
function generateI18nContent(texts, lang = 'zh') {
  const i18nObj = {}
  
  texts.forEach(item => {
    const key = item.key.replace(`${modulePrefix}.`, '')
    setNestedValue(i18nObj, key, lang === 'zh' ? item.text : translateToEnglish(item.text))
  })
  
  return `export default ${JSON.stringify(i18nObj, null, 2)}\n`
}

// 简单的英文翻译（标记待翻译）
function translateToEnglish(chineseText) {
  // 这里可以集成翻译API，暂时返回标记
  return `[EN] ${chineseText}`
}

// 设置嵌套对象值
function setNestedValue(obj, key, value) {
  const keys = key.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {}
    }
    current = current[keys[i]]
  }
  
  current[keys[keys.length - 1]] = value
}

// 替换Vue文件中的中文文本
function replaceChineseTexts(vueContent, texts) {
  let newContent = vueContent
  
  // 按原始文本长度排序，先替换长的文本，避免部分替换问题
  const sortedTexts = [...texts].sort((a, b) => b.original.length - a.original.length)
  
  sortedTexts.forEach(item => {
    const i18nCall = `$t('${item.key}')`
    
    switch (item.type) {
      case 'interpolation':
        // {{ '中文' }} -> {{ $t('key') }}
        newContent = newContent.replace(item.original, i18nCall)
        break
      case 'attribute':
        // placeholder="中文" -> :placeholder="$t('key')"
        const attrPattern = new RegExp(`\\s${item.attributeName}\\s*=\\s*${escapeRegExp(item.original)}`, 'g')
        newContent = newContent.replace(attrPattern, ` :${item.attributeName}="${i18nCall}"`)
        break
      case 'content':
        // >中文< -> >{{ $t('key') }}<
        newContent = newContent.replace(`>${item.original}<`, `>{{ ${i18nCall} }}<`)
        break
      case 'string':
      case 'template':
        // '中文' -> this.$t('key')
        newContent = newContent.replace(item.original, `this.${i18nCall}`)
        break
    }
  })
  
  return newContent
}

// 转义正则表达式特殊字符
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 交互式确认
async function interactiveConfirm(texts) {
  if (isAutoMode) return texts
  
  console.log('\n📝 发现以下中文文本:')
  console.log('=' .repeat(80))
  
  texts.forEach((item, index) => {
    console.log(`\n${index + 1}. 文本: "${item.text}"`)
    console.log(`   类型: ${item.type}`)
    console.log(`   上下文: ${item.context}`)
    console.log(`   建议key: ${item.key}`)
  })
  
  console.log('\n✅ 自动确认所有文本进行处理')
  return texts
}

// 主函数
async function extractI18n() {
  try {
    console.log(`🚀 开始提取国际化文本: ${vueFilePath}`)
    console.log(`📝 模块前缀: ${modulePrefix}`)
    
    // 解析Vue文件
    const vueFile = parseVueFile(vueFilePath)
    
    // 提取中文文本
    const templateTexts = extractFromTemplate(vueFile.template)
    const scriptTexts = extractFromScript(vueFile.script)
    
    const allTexts = [...templateTexts, ...scriptTexts]
    
    if (allTexts.length === 0) {
      console.log('✅ 未发现中文文本，无需处理')
      return
    }
    
    console.log(`📊 共发现 ${allTexts.length} 个中文文本`)
    
    // 生成国际化key
    allTexts.forEach(item => {
      item.key = generateI18nKey(item.text)
    })
    
    // 交互式确认
    const confirmedTexts = await interactiveConfirm(allTexts)
    
    if (confirmedTexts.length === 0) {
      console.log('❌ 没有确认的文本需要处理')
      return
    }
    
    // 生成国际化文件
    const dirPath = path.dirname(vueFilePath)
    const langDirPath = path.join(dirPath, 'lang')
    
    // 确保lang目录存在
    if (!fs.existsSync(langDirPath)) {
      fs.mkdirSync(langDirPath, { recursive: true })
    }
    
    const zhFilePath = path.join(langDirPath, 'zh.js')
    const enFilePath = path.join(langDirPath, 'en.js')
    
    const zhContent = generateI18nContent(confirmedTexts, 'zh')
    const enContent = generateI18nContent(confirmedTexts, 'en')
    
    fs.writeFileSync(zhFilePath, zhContent)
    fs.writeFileSync(enFilePath, enContent)
    
    console.log(`✅ 已生成中文国际化文件: ${path.relative(process.cwd(), zhFilePath)}`)
    console.log(`✅ 已生成英文国际化文件: ${path.relative(process.cwd(), enFilePath)}`)
    
    // 替换Vue文件内容
    const newVueContent = replaceChineseTexts(vueFile.original, confirmedTexts)
    const backupPath = vueFilePath + '.backup'
    
    // 备份原文件
    fs.writeFileSync(backupPath, vueFile.original)
    fs.writeFileSync(vueFilePath, newVueContent)
    
    console.log(`✅ 已更新Vue文件: ${vueFilePath}`)
    console.log(`💾 原文件备份: ${backupPath}`)
    
    // 输出国际化文件预览
    console.log('\n📄 生成的中文国际化文件预览:')
    console.log('-'.repeat(50))
    console.log(zhContent)
    
    console.log('\n🎉 国际化提取完成！')
    console.log(`📄 处理了 ${confirmedTexts.length} 个文本`)
    console.log('\n📝 下一步:')
    console.log('1. 检查生成的国际化文件内容')
    console.log('2. 完善英文翻译（替换[EN]标记）') 
    console.log('3. 测试页面功能是否正常')
    console.log('4. 确认无误后可删除备份文件')
    console.log('5. 记得在组件中添加动态国际化加载逻辑')
    console.log('6. 国际化文件已放置在lang/目录下')
    
  } catch (error) {
    console.error('❌ 提取国际化失败:', error.message)
    console.error(error.stack)
    process.exit(1)
  }
}

// 执行提取
extractI18n() 