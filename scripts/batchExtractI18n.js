#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length === 0) {
  console.error('❌ 请提供目录路径')
  console.log('用法: node scripts/batchExtractI18n.js <directoryPath> [--auto] [--recursive]')
  console.log('示例: node scripts/batchExtractI18n.js src/views/user --auto --recursive')
  process.exit(1)
}

const directoryPath = args[0]
const isAutoMode = args.includes('--auto')
const isRecursive = args.includes('--recursive')

// 获取所有Vue文件
function getVueFiles(dir, recursive = false) {
  const files = []
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && recursive) {
        // 跳过 node_modules, .git 等目录
        if (!['node_modules', '.git', 'dist', 'build', '.nuxt'].includes(item)) {
          scanDirectory(fullPath)
        }
      } else if (stat.isFile() && item.endsWith('.vue')) {
        files.push(fullPath)
      }
    }
  }
  
  scanDirectory(dir)
  return files
}

// 检查文件是否包含中文
function hasChineseText(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    return /[\u4e00-\u9fa5]/.test(content)
  } catch (error) {
    return false
  }
}

// 检查是否已经有国际化文件
function hasI18nFiles(vueFilePath) {
  const dir = path.dirname(vueFilePath)
  const zhFile = path.join(dir, 'zh.js')
  const enFile = path.join(dir, 'en.js')
  
  return fs.existsSync(zhFile) || fs.existsSync(enFile)
}

// 主函数
async function batchExtractI18n() {
  try {
    console.log(`🚀 开始批量提取国际化文本`)
    console.log(`📁 目录: ${directoryPath}`)
    console.log(`🔄 递归: ${isRecursive ? '是' : '否'}`)
    console.log(`🤖 自动模式: ${isAutoMode ? '是' : '否'}`)
    
    if (!fs.existsSync(directoryPath)) {
      throw new Error(`目录不存在: ${directoryPath}`)
    }
    
    // 获取所有Vue文件
    const vueFiles = getVueFiles(directoryPath, isRecursive)
    console.log(`📄 发现 ${vueFiles.length} 个Vue文件`)
    
    if (vueFiles.length === 0) {
      console.log('✅ 没有找到Vue文件')
      return
    }
    
    // 筛选需要处理的文件
    const filesToProcess = []
    const skippedFiles = []
    
    for (const vueFile of vueFiles) {
      if (!hasChineseText(vueFile)) {
        skippedFiles.push({ file: vueFile, reason: '无中文文本' })
        continue
      }
      
      if (hasI18nFiles(vueFile) && !isAutoMode) {
        skippedFiles.push({ file: vueFile, reason: '已存在国际化文件' })
        continue
      }
      
      filesToProcess.push(vueFile)
    }
    
    console.log(`\n📊 统计信息:`)
    console.log(`✅ 需要处理: ${filesToProcess.length} 个文件`)
    console.log(`⏭️  跳过: ${skippedFiles.length} 个文件`)
    
    if (skippedFiles.length > 0) {
      console.log(`\n📝 跳过的文件:`)
      skippedFiles.forEach(({ file, reason }) => {
        console.log(`   ${path.relative(process.cwd(), file)} - ${reason}`)
      })
    }
    
    if (filesToProcess.length === 0) {
      console.log('\n✅ 没有需要处理的文件')
      return
    }
    
    console.log(`\n🔄 开始处理文件...`)
    
    const results = {
      success: [],
      failed: [],
      total: filesToProcess.length
    }
    
    // 处理每个文件
    for (let i = 0; i < filesToProcess.length; i++) {
      const vueFile = filesToProcess[i]
      const relativePath = path.relative(process.cwd(), vueFile)
      
      console.log(`\n[${i + 1}/${filesToProcess.length}] 处理: ${relativePath}`)
      
      try {
        // 构建命令
        const extractScript = path.join(__dirname, 'extractI18n.js')
        const cmd = `node "${extractScript}" "${vueFile}"${isAutoMode ? ' --auto' : ''}`
        
        // 执行提取命令
        const output = execSync(cmd, { 
          encoding: 'utf8',
          stdio: 'pipe'
        })
        
        console.log('   ✅ 成功')
        results.success.push(vueFile)
        
        // 如果不是自动模式，显示输出
        if (!isAutoMode && output.trim()) {
          console.log('   输出:', output.trim().split('\n').slice(-3).join('\n   '))
        }
        
      } catch (error) {
        console.log('   ❌ 失败:', error.message.split('\n')[0])
        results.failed.push({ file: vueFile, error: error.message })
      }
    }
    
    // 输出总结
    console.log('\n🎉 批量处理完成！')
    console.log('=' .repeat(60))
    console.log(`📊 处理结果:`)
    console.log(`   总文件数: ${results.total}`)
    console.log(`   成功: ${results.success.length}`)
    console.log(`   失败: ${results.failed.length}`)
    
    if (results.success.length > 0) {
      console.log(`\n✅ 成功处理的文件:`)
      results.success.forEach(file => {
        console.log(`   ${path.relative(process.cwd(), file)}`)
      })
    }
    
    if (results.failed.length > 0) {
      console.log(`\n❌ 失败的文件:`)
      results.failed.forEach(({ file, error }) => {
        console.log(`   ${path.relative(process.cwd(), file)} - ${error.split('\n')[0]}`)
      })
    }
    
    console.log(`\n📝 后续步骤:`)
    console.log(`1. 检查所有生成的国际化文件`)
    console.log(`2. 完善英文翻译内容`)
    console.log(`3. 测试每个页面的功能`)
    console.log(`4. 确认无误后删除.backup文件`)
    console.log(`5. 统一处理动态国际化加载逻辑`)
    
    // 生成清理脚本命令
    if (results.success.length > 0) {
      console.log(`\n🧹 清理备份文件命令 (确认无误后执行):`)
      const cleanupCmd = results.success
        .map(file => `rm "${file}.backup"`)
        .join(' && ')
      console.log(`   ${cleanupCmd}`)
    }
    
  } catch (error) {
    console.error('❌ 批量处理失败:', error.message)
    process.exit(1)
  }
}

// 执行批量处理
batchExtractI18n() 