#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 定义路径
const identityAuthDir = 'src/views/userManage/identity-auth'
const vueFilePath = path.join(identityAuthDir, 'identity-auth-identity-auth.vue')
const newVueFilePath = path.join(identityAuthDir, 'index.vue')
const langDir = path.join(identityAuthDir, 'lang')

console.log('🚀 开始修复identity-auth文件...')

try {
  // 1. 创建lang目录
  if (!fs.existsSync(langDir)) {
    fs.mkdirSync(langDir, { recursive: true })
    console.log('✅ 创建lang目录')
  }

  // 2. 移动国际化文件到lang目录
  const zhFile = path.join(identityAuthDir, 'zh.js')
  const enFile = path.join(identityAuthDir, 'en.js')
  const newZhFile = path.join(langDir, 'zh.js')
  const newEnFile = path.join(langDir, 'en.js')

  if (fs.existsSync(zhFile)) {
    // 读取并修改zh.js内容
    let zhContent = fs.readFileSync(zhFile, 'utf8')
    zhContent = zhContent.replace(/添加identity-auth/g, '添加数据')
    zhContent = zhContent.replace(/编辑identity-auth/g, '编辑数据')
    fs.writeFileSync(newZhFile, zhContent)
    fs.unlinkSync(zhFile)
    console.log('✅ 移动并修复zh.js')
  }

  if (fs.existsSync(enFile)) {
    // 读取并修改en.js内容
    let enContent = fs.readFileSync(enFile, 'utf8')
    enContent = enContent.replace(/Add identity-auth/g, 'Add Data')
    enContent = enContent.replace(/Edit identity-auth/g, 'Edit Data')
    fs.writeFileSync(newEnFile, enContent)
    fs.unlinkSync(enFile)
    console.log('✅ 移动并修复en.js')
  }

  // 3. 重命名Vue文件并修复内容
  if (fs.existsSync(vueFilePath)) {
    let vueContent = fs.readFileSync(vueFilePath, 'utf8')
    
    // 修复动态导入路径
    vueContent = vueContent.replace(
      /const langModule = await import\(`\.\/${currentLang}\.js`\)/g,
      'const langModule = await import(`./lang/${currentLang}.js`)'
    )
    
    vueContent = vueContent.replace(
      /const zhModule = await import\('\.\/zh\.js'\)/g,
      "const zhModule = await import('./lang/zh.js')"
    )

    // 修复组件名
    vueContent = vueContent.replace(
      /name: "Identity-auth"/g,
      'name: "IdentityAuth"'
    )

    // 备份原文件
    fs.writeFileSync(vueFilePath + '.backup', fs.readFileSync(vueFilePath))
    
    // 写入修复后的内容
    fs.writeFileSync(newVueFilePath, vueContent)
    
    // 删除旧文件
    fs.unlinkSync(vueFilePath)
    
    console.log('✅ 重命名Vue文件为index.vue并修复内容')
  }

  console.log('\n🎉 修复完成！')
  console.log('📁 文件结构:')
  console.log('  ├── index.vue')
  console.log('  └── lang/')
  console.log('      ├── zh.js')
  console.log('      └── en.js')
  
  console.log('\n📝 已修复的问题:')
  console.log('• 重命名Vue文件为index.vue')
  console.log('• 移动国际化文件到lang/目录')
  console.log('• 修复动态导入路径')
  console.log('• 更新国际化文本内容')
  console.log('• 修复组件名称')

} catch (error) {
  console.error('❌ 修复失败:', error.message)
  process.exit(1)
} 